{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.organizeImports": "explicit"
  },

  // 🛠️ ESLint & Prettier Setup
  "eslint.validate": ["javascript", "typescript", "typescriptreact", "json"],
  "prettier.singleQuote": true,
  "prettier.trailingComma": "all",
  "prettier.printWidth": 100,
  "prettier.tabWidth": 2,
  "prettier.arrowParens": "avoid",
  "prettier.useTabs": false,

  // 🔍 Files & Performance Boost
  "files.exclude": {
    "**/.git": true,
    // "**/node_modules": true,
    "**/.turbo": true,
    "**/dist": true
  },
  "files.watcherExclude": {
    "**/.git/objects/**": true,
    "**/node_modules/**": true,
    "**/.next/**": true,
    "**/dist/**": true
  },
  "search.exclude": {
    "**/node_modules": true,
    "**/build": true,
    "**/.next": true
  },

  // ⚡ Performance Optimization
  "typescript.tsdk": "node_modules/typescript/lib",
  "typescript.disableAutomaticTypeAcquisition": true,
  "typescript.preferences.importModuleSpecifier": "shortest",
  "typescript.updateImportsOnFileMove.enabled": "always",
  "javascript.preferences.importModuleSpecifier": "shortest",

  // 🎨 UI & Productivity Enhancements
  "editor.tabSize": 2,
  "editor.insertSpaces": true,
  "editor.autoClosingBrackets": "always",
  "editor.autoClosingQuotes": "always",
  "editor.wordWrap": "on",
  "editor.quickSuggestions": {
    "other": true,
    "comments": false,
    "strings": true
  },
  "editor.suggest.snippetsPreventQuickSuggestions": false,
  "editor.suggest.showWords": true,
  "editor.suggest.localityBonus": true,
  "editor.suggestSelection": "first",
  "editor.cursorBlinking": "smooth",
  "editor.cursorSmoothCaretAnimation": "on",
  "editor.linkedEditing": true,
  "editor.stickyScroll.enabled": true,
  "editor.inlineSuggest.enabled": true,
  "editor.bracketPairColorization.enabled": true,
  "editor.guides.bracketPairs": "active",
  "editor.minimap.enabled": true,
  "editor.minimap.scale": 1.1,
  "editor.fontSize": 14,
  "editor.lineHeight": 22,
  "editor.renderWhitespace": "boundary",
  "editor.formatOnPaste": true,
  "editor.formatOnType": true,
  "editor.folding": true,
  "editor.foldingStrategy": "auto",
  "editor.hover.delay": 100,
  "editor.hover.enabled": true,
  "editor.cursorSurroundingLines": 3,
  "editor.scrollBeyondLastLine": false,
  "editor.accessibilitySupport": "off",

  // 🚀 Terminal & Git Integration
  "terminal.integrated.defaultProfile.linux": "bash",
  "terminal.integrated.scrollback": 10000,
  "git.confirmSync": false,
  "git.enableSmartCommit": true,
  "git.autofetch": true,
  "git.allowForcePush": true,
  "git.branchProtection": ["main", "master"],

  // ⚡ Workbench UI Enhancements
  "workbench.startupEditor": "none",
  "workbench.colorTheme": "One Dark Pro",
  "workbench.iconTheme": "vscode-icons",
  "workbench.list.smoothScrolling": true,
  "workbench.editor.enablePreview": false,

  // 🛠️ Advanced Editor Config for TS & JS
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[json]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[jsonc]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },

  // 🛡️ Security & Trust Settings
  "security.workspace.trust.untrustedFiles": "open"
}
