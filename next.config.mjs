import { withPayload } from '@payloadcms/next/withPayload';

/** @type {import('next').NextConfig} */
const nextConfig = {
  env: {
    DATABASE_URL: process.env.DATABASE_URI,
    PAYLOAD_SECRET: process.env.PAYLOAD_SECRET,
    NEXT_PUBLIC_BACKEND_URL: process.env.NEXT_PUBLIC_BACKEND_URL,
    SITE_KEY_CAPTCHA: process.env.SITE_KEY_CAPTCHA,
    SMTP_HOST: process.env.SMTP_HOST,
    SMTP_USER: process.env.SMTP_USER,
    SMTP_PASS: process.env.SMTP_PASS,
    SMTP_PORT: process.env.SMTP_PORT,
    SMTP_FROM: process.env.SMTP_FROM,
    _AWS_ACCESS_KEY_ID: process.env._AWS_ACCESS_KEY_ID,
    _AWS_SECRET_ACCESS_KEY: process.env._AWS_SECRET_ACCESS_KEY,
    _AWS_REGION: process.env._AWS_REGION,
    _AWS_END_POINT: process.env._AWS_END_POINT,
    _AWS_BUCKET_NAME: process.env._AWS_BUCKET_NAME,
  },
  // Performance optimizations
  poweredByHeader: false,
  reactStrictMode: true,
  // Optimize build performance
  swcMinify: true,
  // Optimize image loading
  images: {
    formats: ['image/avif', 'image/webp'],
    minimumCacheTTL: 60,
    domains: [
      'storage.googleapis.com',
      'prod-icmr-innovation.s3.ap-south-1.amazonaws.com',
      'd3k0o2j3cgt77r.cloudfront.net',
      process.env.SMTP_HOST,
    ],
  },
  output: 'standalone',
  // Your Next.js config here
};

export default withPayload(nextConfig);
