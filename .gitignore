# Node modules
node_modules/

# Build outputs
.next/

out/

# Dependency & package manager files
package-lock.json
pnpm-lock.yaml
yarn.lock

# Environment variables (DO NOT COMMIT SECRETS)
.env.local
.env.development.local
.env.production.local
.env.test.local
.env

# Logs & debugging files
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
debug.log
*.log

# TypeScript cache
*.tsbuildinfo

# IDE-specific files
.idea/
*.swp
*.swo
*.sublime-workspace

# MacOS files
.DS_Store

# Payload CMS (if using local file storage)
payload/uploads/
.qodo

public/robots.txt

