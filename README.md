# blank

blank

## Attributes

- **Database**: mongodb
- **Storage Adapter**: localDisk

```
icmr-atlas-web
├─ .prettierignore
├─ .prettierrc
├─ Dockerfile
├─ README.md
├─ docker-compose.yml
├─ eslint.config.mjs
├─ next-env.d.ts
├─ next-sitemap.js
├─ next.config.mjs
├─ package.json
├─ postcss.config.mjs
├─ public
│  ├─ images
│  │  ├─ Vector (2).png
│  │  ├─ icons
│  │  │  ├─ Atlas-of-Innovators.png
│  │  │  ├─ arrowIcon.svg
│  │  │  ├─ arrowUpRightIcon.svg
│  │  │  ├─ blueStripArrow.svg
│  │  │  ├─ build-icon.svg
│  │  │  ├─ caret-right.svg
│  │  │  ├─ darkArrowIcon.svg
│  │  │  ├─ detect-icon.svg
│  │  │  ├─ edit.svg
│  │  │  ├─ filterIcon.svg
│  │  │  ├─ lightLoginIcon.svg
│  │  │  ├─ loginIcon.svg
│  │  │  ├─ menu-burger.svg
│  │  │  ├─ prevent-icon.svg
│  │  │  ├─ profile.svg
│  │  │  ├─ searchIcon.svg
│  │  │  ├─ share.svg
│  │  │  ├─ stripArrow.svg
│  │  │  ├─ treat-icon.svg
│  │  │  └─ treat4.svg
│  │  └─ illustrations
│  │     └─ partners
│  │        ├─ GatesFoundation.svg
│  │        ├─ TBInnovationSummit.svg
│  │        ├─ TheUnion.svg
│  │        └─ indiaInnovationSubmitLogo.svg
│  └─ robots.txt
├─ src
│  ├─ Logo.tsx
│  ├─ app
│  │  ├─ (frontend)
│  │  │  ├─ (pages)
│  │  │  │  ├─ browse-innovations
│  │  │  │  │  ├─ Native
│  │  │  │  │  │  └─ innovations.tsx
│  │  │  │  │  └─ page.tsx
│  │  │  │  ├─ contact-us
│  │  │  │  │  ├─ ContactForm.tsx
│  │  │  │  │  └─ page.tsx
│  │  │  │  ├─ faq
│  │  │  │  │  ├─ FAQClient.tsx
│  │  │  │  │  └─ page.tsx
│  │  │  │  ├─ how-to-submit
│  │  │  │  │  ├─ page.tsx
│  │  │  │  │  ├─ submit-an-innovation
│  │  │  │  │  │  ├─ [slug]
│  │  │  │  │  │  │  └─ page.tsx
│  │  │  │  │  │  └─ page.tsx
│  │  │  │  │  └─ terms-of-use
│  │  │  │  │     └─ page.tsx
│  │  │  │  ├─ innovation
│  │  │  │  │  └─ [slug]
│  │  │  │  │     └─ page.tsx
│  │  │  │  ├─ learn-more
│  │  │  │  │  └─ page.tsx
│  │  │  │  ├─ my-innovations
│  │  │  │  │  ├─ Native
│  │  │  │  │  │  └─ innovations.tsx
│  │  │  │  │  └─ page.tsx
│  │  │  │  ├─ reset-password
│  │  │  │  │  ├─ ResetPassword.tsx
│  │  │  │  │  └─ page.tsx
│  │  │  │  └─ token-auth
│  │  │  │     ├─ Auth.tsx
│  │  │  │     ├─ authenticate.ts
│  │  │  │     └─ page.tsx
│  │  │  ├─ components
│  │  │  │  ├─ Buttons
│  │  │  │  │  └─ buttons.tsx
│  │  │  │  ├─ Cards
│  │  │  │  │  ├─ Card.tsx
│  │  │  │  │  ├─ FilterCard.tsx
│  │  │  │  │  ├─ GlassCardSSR.tsx
│  │  │  │  │  ├─ GradientWrapper.tsx
│  │  │  │  │  ├─ InnovationCards.tsx
│  │  │  │  │  ├─ InnovationSectionCard.tsx
│  │  │  │  │  ├─ NavBarClientWrapper.tsx
│  │  │  │  │  ├─ glassCard.tsx
│  │  │  │  │  └─ marqueeStrip.tsx
│  │  │  │  ├─ ErrorBoundary.tsx
│  │  │  │  ├─ Forms
│  │  │  │  │  ├─ InnovationFormOrignal.tsx
│  │  │  │  │  ├─ InnovationPreview.tsx
│  │  │  │  │  └─ InputComponent.tsx
│  │  │  │  ├─ InovationDetails
│  │  │  │  │  ├─ InsightDetails.tsx
│  │  │  │  │  ├─ InsightsLessons.tsx
│  │  │  │  │  └─ SummaryOfInovation.tsx
│  │  │  │  ├─ Skeleton
│  │  │  │  │  ├─ FilterSkeleton.tsx
│  │  │  │  │  └─ InnovationCardSkeleton.tsx
│  │  │  │  ├─ UI
│  │  │  │  │  ├─ ApplicationProcessSection.tsx
│  │  │  │  │  ├─ Button.tsx
│  │  │  │  │  ├─ CircularLoader.tsx
│  │  │  │  │  ├─ FlowChart.tsx
│  │  │  │  │  ├─ HomeComponent.tsx
│  │  │  │  │  ├─ ImageCarousel.tsx
│  │  │  │  │  ├─ InnovationForm.tsx
│  │  │  │  │  ├─ LoadingOverlay.tsx
│  │  │  │  │  ├─ SunburstChart.tsx
│  │  │  │  │  ├─ SunburstSkeletonLoader.tsx
│  │  │  │  │  ├─ Toast.tsx
│  │  │  │  │  ├─ aboutUsComponent.tsx
│  │  │  │  │  ├─ loadingFallBackComponents.tsx
│  │  │  │  │  ├─ navBar.tsx
│  │  │  │  │  └─ submitInnovationForm.tsx
│  │  │  │  ├─ howToSubmitInnovationComponent.tsx
│  │  │  │  └─ modals
│  │  │  │     ├─ AuthModalPortal.tsx
│  │  │  │     ├─ InnovationPreviewModal.tsx
│  │  │  │     ├─ InnovationSuccessModal.tsx
│  │  │  │     ├─ SubmissionSuccessModal.tsx
│  │  │  │     ├─ authModal.tsx
│  │  │  │     └─ profile.tsx
│  │  │  ├─ constants
│  │  │  │  └─ routes.ts
│  │  │  ├─ layout.tsx
│  │  │  ├─ not-found.tsx
│  │  │  ├─ page.tsx
│  │  │  ├─ styles.css
│  │  │  ├─ translations
│  │  │  │  ├─ en.json
│  │  │  │  └─ index.ts
│  │  │  ├─ types
│  │  │  │  └─ data.types.ts
│  │  │  └─ utils
│  │  │     ├─ apiClient.ts
│  │  │     ├─ footerClient.ts
│  │  │     ├─ functions.ts
│  │  │     └─ metadata.ts
│  │  ├─ (payload)
│  │  │  ├─ _provider
│  │  │  │  └─ Auth
│  │  │  ├─ admin
│  │  │  │  ├─ [[...segments]]
│  │  │  │  │  ├─ not-found.tsx
│  │  │  │  │  └─ page.tsx
│  │  │  │  └─ importMap.js
│  │  │  ├─ api
│  │  │  │  ├─ [...slug]
│  │  │  │  │  └─ route.ts
│  │  │  │  ├─ auth
│  │  │  │  │  ├─ signin
│  │  │  │  │  └─ signup
│  │  │  │  ├─ graphql
│  │  │  │  │  └─ route.ts
│  │  │  │  └─ graphql-playground
│  │  │  │     └─ route.ts
│  │  │  ├─ custom.scss
│  │  │  └─ layout.tsx
│  │  ├─ preview
│  │  │  ├─ aboutUs-content
│  │  │  │  └─ page.tsx
│  │  │  ├─ components
│  │  │  │  └─ ClientRefreshWrapper.tsx
│  │  │  ├─ contactPage-content
│  │  │  │  └─ page.tsx
│  │  │  ├─ faq-content
│  │  │  │  └─ page.tsx
│  │  │  ├─ faq-content-ssr
│  │  │  │  └─ page.tsx
│  │  │  ├─ footer-content
│  │  │  │  └─ page.tsx
│  │  │  ├─ innovations-hero
│  │  │  │  └─ page.tsx
│  │  │  ├─ landingPageUi
│  │  │  │  └─ page.tsx
│  │  │  ├─ layout.tsx
│  │  │  ├─ mapOfInnovation-content
│  │  │  │  └─ page.tsx
│  │  │  └─ submit-innovation-content
│  │  │     └─ page.tsx
│  │  └─ sitemap.ts
│  ├─ collections
│  │  ├─ AboutUsUi.ts
│  │  ├─ BrowseInnovationUi.ts
│  │  ├─ ContactUsUi.ts
│  │  ├─ EmailtTemplates.ts
│  │  ├─ FaqUi.ts
│  │  ├─ FlowChart.ts
│  │  ├─ FooterContent.ts
│  │  ├─ FormOptions.ts
│  │  ├─ Innovations.ts
│  │  ├─ Inquiries.ts
│  │  ├─ LandingPageUi.ts
│  │  ├─ MapOfInnovationsUi.ts
│  │  ├─ Media.ts
│  │  ├─ SubmitCriteria.ts
│  │  ├─ SubmitInnovationUi.ts
│  │  ├─ Sunburst.ts
│  │  ├─ Upload.ts
│  │  ├─ Users.ts
│  │  ├─ appUsers.ts
│  │  ├─ innovationClass.ts
│  │  ├─ projects.ts
│  │  └─ utils.ts
│  ├─ components
│  │  └─ LiveVariablesComponent.tsx
│  ├─ payload-types.ts
│  └─ payload.config.ts
└─ tsconfig.json

```
