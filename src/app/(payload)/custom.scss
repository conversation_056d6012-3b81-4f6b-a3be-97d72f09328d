@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Roboto:wght@400;500;700&display=swap');

body,
h2,
h5 {
  font-family: 'Poppins', 'Roboto', sans-serif;
}
.sunburst-node-arc {
  fill-opacity: 0.6;
}
.sunburst-node-arc:hover {
  fill-opacity: 0.3;
}
@keyframes spin-faster {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes blob {
  0%,
  100% {
    transform: translate(0, 0) scale(1);
  }
  25% {
    transform: translate(-20px, 30px) scale(1.2);
  }
  50% {
    transform: translate(20px, -30px) scale(0.9);
  }
  75% {
    transform: translate(-20px, -30px) scale(1.1);
  }
}

.animate-spin-faster {
  animation: spin-faster 1s linear infinite !important;
}
.animate-blob {
  animation: blob 8s ease-in-out infinite;
}

.react-quill-container .ql-toolbar.ql-snow {
  position: sticky;
  top: 0;
  z-index: 9;
}

.react-quill-container .ql-toolbar.ql-snow,
.react-quill-container .ql-container.ql-snow {
  border: 1px solid var(--theme-elevation-150);
  background-color: var(--theme-bg);
  color: var(--theme-text);
}

.ql-editor img {
  max-width: 100%;
  height: auto;
  display: block;
}
