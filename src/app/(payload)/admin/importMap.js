import { RscEntrySlateCell as RscEntrySlateCell_0e78253914a550fdacd75626f1dabe17 } from '@payloadcms/richtext-slate/rsc'
import { RscEntrySlateField as RscEntrySlateField_0e78253914a550fdacd75626f1dabe17 } from '@payloadcms/richtext-slate/rsc'
import { BoldLeafButton as BoldLeafButton_0b388c087d9de8c4f011dd323a130cfb } from '@payloadcms/richtext-slate/client'
import { BoldLeaf as BoldLeaf_0b388c087d9de8c4f011dd323a130cfb } from '@payloadcms/richtext-slate/client'
import { CodeLeafButton as CodeLeafButton_0b388c087d9de8c4f011dd323a130cfb } from '@payloadcms/richtext-slate/client'
import { CodeLeaf as CodeLeaf_0b388c087d9de8c4f011dd323a130cfb } from '@payloadcms/richtext-slate/client'
import { ItalicLeafButton as ItalicLeafButton_0b388c087d9de8c4f011dd323a130cfb } from '@payloadcms/richtext-slate/client'
import { ItalicLeaf as ItalicLeaf_0b388c087d9de8c4f011dd323a130cfb } from '@payloadcms/richtext-slate/client'
import { StrikethroughLeafButton as StrikethroughLeafButton_0b388c087d9de8c4f011dd323a130cfb } from '@payloadcms/richtext-slate/client'
import { StrikethroughLeaf as StrikethroughLeaf_0b388c087d9de8c4f011dd323a130cfb } from '@payloadcms/richtext-slate/client'
import { UnderlineLeafButton as UnderlineLeafButton_0b388c087d9de8c4f011dd323a130cfb } from '@payloadcms/richtext-slate/client'
import { UnderlineLeaf as UnderlineLeaf_0b388c087d9de8c4f011dd323a130cfb } from '@payloadcms/richtext-slate/client'
import { BlockquoteElementButton as BlockquoteElementButton_0b388c087d9de8c4f011dd323a130cfb } from '@payloadcms/richtext-slate/client'
import { BlockquoteElement as BlockquoteElement_0b388c087d9de8c4f011dd323a130cfb } from '@payloadcms/richtext-slate/client'
import { H1ElementButton as H1ElementButton_0b388c087d9de8c4f011dd323a130cfb } from '@payloadcms/richtext-slate/client'
import { Heading1Element as Heading1Element_0b388c087d9de8c4f011dd323a130cfb } from '@payloadcms/richtext-slate/client'
import { H2ElementButton as H2ElementButton_0b388c087d9de8c4f011dd323a130cfb } from '@payloadcms/richtext-slate/client'
import { Heading2Element as Heading2Element_0b388c087d9de8c4f011dd323a130cfb } from '@payloadcms/richtext-slate/client'
import { H3ElementButton as H3ElementButton_0b388c087d9de8c4f011dd323a130cfb } from '@payloadcms/richtext-slate/client'
import { Heading3Element as Heading3Element_0b388c087d9de8c4f011dd323a130cfb } from '@payloadcms/richtext-slate/client'
import { H4ElementButton as H4ElementButton_0b388c087d9de8c4f011dd323a130cfb } from '@payloadcms/richtext-slate/client'
import { Heading4Element as Heading4Element_0b388c087d9de8c4f011dd323a130cfb } from '@payloadcms/richtext-slate/client'
import { H5ElementButton as H5ElementButton_0b388c087d9de8c4f011dd323a130cfb } from '@payloadcms/richtext-slate/client'
import { Heading5Element as Heading5Element_0b388c087d9de8c4f011dd323a130cfb } from '@payloadcms/richtext-slate/client'
import { H6ElementButton as H6ElementButton_0b388c087d9de8c4f011dd323a130cfb } from '@payloadcms/richtext-slate/client'
import { Heading6Element as Heading6Element_0b388c087d9de8c4f011dd323a130cfb } from '@payloadcms/richtext-slate/client'
import { IndentButton as IndentButton_0b388c087d9de8c4f011dd323a130cfb } from '@payloadcms/richtext-slate/client'
import { IndentElement as IndentElement_0b388c087d9de8c4f011dd323a130cfb } from '@payloadcms/richtext-slate/client'
import { ListItemElement as ListItemElement_0b388c087d9de8c4f011dd323a130cfb } from '@payloadcms/richtext-slate/client'
import { LinkButton as LinkButton_0b388c087d9de8c4f011dd323a130cfb } from '@payloadcms/richtext-slate/client'
import { LinkElement as LinkElement_0b388c087d9de8c4f011dd323a130cfb } from '@payloadcms/richtext-slate/client'
import { WithLinks as WithLinks_0b388c087d9de8c4f011dd323a130cfb } from '@payloadcms/richtext-slate/client'
import { OLElementButton as OLElementButton_0b388c087d9de8c4f011dd323a130cfb } from '@payloadcms/richtext-slate/client'
import { OrderedListElement as OrderedListElement_0b388c087d9de8c4f011dd323a130cfb } from '@payloadcms/richtext-slate/client'
import { RelationshipButton as RelationshipButton_0b388c087d9de8c4f011dd323a130cfb } from '@payloadcms/richtext-slate/client'
import { RelationshipElement as RelationshipElement_0b388c087d9de8c4f011dd323a130cfb } from '@payloadcms/richtext-slate/client'
import { WithRelationship as WithRelationship_0b388c087d9de8c4f011dd323a130cfb } from '@payloadcms/richtext-slate/client'
import { TextAlignElementButton as TextAlignElementButton_0b388c087d9de8c4f011dd323a130cfb } from '@payloadcms/richtext-slate/client'
import { ULElementButton as ULElementButton_0b388c087d9de8c4f011dd323a130cfb } from '@payloadcms/richtext-slate/client'
import { UnorderedListElement as UnorderedListElement_0b388c087d9de8c4f011dd323a130cfb } from '@payloadcms/richtext-slate/client'
import { UploadElementButton as UploadElementButton_0b388c087d9de8c4f011dd323a130cfb } from '@payloadcms/richtext-slate/client'
import { UploadElement as UploadElement_0b388c087d9de8c4f011dd323a130cfb } from '@payloadcms/richtext-slate/client'
import { WithUpload as WithUpload_0b388c087d9de8c4f011dd323a130cfb } from '@payloadcms/richtext-slate/client'
import { S3ClientUploadHandler as S3ClientUploadHandler_f97aa6c64367fa259c5bc0567239ef24 } from '@payloadcms/storage-s3/client'

export const importMap = {
  "@payloadcms/richtext-slate/rsc#RscEntrySlateCell": RscEntrySlateCell_0e78253914a550fdacd75626f1dabe17,
  "@payloadcms/richtext-slate/rsc#RscEntrySlateField": RscEntrySlateField_0e78253914a550fdacd75626f1dabe17,
  "@payloadcms/richtext-slate/client#BoldLeafButton": BoldLeafButton_0b388c087d9de8c4f011dd323a130cfb,
  "@payloadcms/richtext-slate/client#BoldLeaf": BoldLeaf_0b388c087d9de8c4f011dd323a130cfb,
  "@payloadcms/richtext-slate/client#CodeLeafButton": CodeLeafButton_0b388c087d9de8c4f011dd323a130cfb,
  "@payloadcms/richtext-slate/client#CodeLeaf": CodeLeaf_0b388c087d9de8c4f011dd323a130cfb,
  "@payloadcms/richtext-slate/client#ItalicLeafButton": ItalicLeafButton_0b388c087d9de8c4f011dd323a130cfb,
  "@payloadcms/richtext-slate/client#ItalicLeaf": ItalicLeaf_0b388c087d9de8c4f011dd323a130cfb,
  "@payloadcms/richtext-slate/client#StrikethroughLeafButton": StrikethroughLeafButton_0b388c087d9de8c4f011dd323a130cfb,
  "@payloadcms/richtext-slate/client#StrikethroughLeaf": StrikethroughLeaf_0b388c087d9de8c4f011dd323a130cfb,
  "@payloadcms/richtext-slate/client#UnderlineLeafButton": UnderlineLeafButton_0b388c087d9de8c4f011dd323a130cfb,
  "@payloadcms/richtext-slate/client#UnderlineLeaf": UnderlineLeaf_0b388c087d9de8c4f011dd323a130cfb,
  "@payloadcms/richtext-slate/client#BlockquoteElementButton": BlockquoteElementButton_0b388c087d9de8c4f011dd323a130cfb,
  "@payloadcms/richtext-slate/client#BlockquoteElement": BlockquoteElement_0b388c087d9de8c4f011dd323a130cfb,
  "@payloadcms/richtext-slate/client#H1ElementButton": H1ElementButton_0b388c087d9de8c4f011dd323a130cfb,
  "@payloadcms/richtext-slate/client#Heading1Element": Heading1Element_0b388c087d9de8c4f011dd323a130cfb,
  "@payloadcms/richtext-slate/client#H2ElementButton": H2ElementButton_0b388c087d9de8c4f011dd323a130cfb,
  "@payloadcms/richtext-slate/client#Heading2Element": Heading2Element_0b388c087d9de8c4f011dd323a130cfb,
  "@payloadcms/richtext-slate/client#H3ElementButton": H3ElementButton_0b388c087d9de8c4f011dd323a130cfb,
  "@payloadcms/richtext-slate/client#Heading3Element": Heading3Element_0b388c087d9de8c4f011dd323a130cfb,
  "@payloadcms/richtext-slate/client#H4ElementButton": H4ElementButton_0b388c087d9de8c4f011dd323a130cfb,
  "@payloadcms/richtext-slate/client#Heading4Element": Heading4Element_0b388c087d9de8c4f011dd323a130cfb,
  "@payloadcms/richtext-slate/client#H5ElementButton": H5ElementButton_0b388c087d9de8c4f011dd323a130cfb,
  "@payloadcms/richtext-slate/client#Heading5Element": Heading5Element_0b388c087d9de8c4f011dd323a130cfb,
  "@payloadcms/richtext-slate/client#H6ElementButton": H6ElementButton_0b388c087d9de8c4f011dd323a130cfb,
  "@payloadcms/richtext-slate/client#Heading6Element": Heading6Element_0b388c087d9de8c4f011dd323a130cfb,
  "@payloadcms/richtext-slate/client#IndentButton": IndentButton_0b388c087d9de8c4f011dd323a130cfb,
  "@payloadcms/richtext-slate/client#IndentElement": IndentElement_0b388c087d9de8c4f011dd323a130cfb,
  "@payloadcms/richtext-slate/client#ListItemElement": ListItemElement_0b388c087d9de8c4f011dd323a130cfb,
  "@payloadcms/richtext-slate/client#LinkButton": LinkButton_0b388c087d9de8c4f011dd323a130cfb,
  "@payloadcms/richtext-slate/client#LinkElement": LinkElement_0b388c087d9de8c4f011dd323a130cfb,
  "@payloadcms/richtext-slate/client#WithLinks": WithLinks_0b388c087d9de8c4f011dd323a130cfb,
  "@payloadcms/richtext-slate/client#OLElementButton": OLElementButton_0b388c087d9de8c4f011dd323a130cfb,
  "@payloadcms/richtext-slate/client#OrderedListElement": OrderedListElement_0b388c087d9de8c4f011dd323a130cfb,
  "@payloadcms/richtext-slate/client#RelationshipButton": RelationshipButton_0b388c087d9de8c4f011dd323a130cfb,
  "@payloadcms/richtext-slate/client#RelationshipElement": RelationshipElement_0b388c087d9de8c4f011dd323a130cfb,
  "@payloadcms/richtext-slate/client#WithRelationship": WithRelationship_0b388c087d9de8c4f011dd323a130cfb,
  "@payloadcms/richtext-slate/client#TextAlignElementButton": TextAlignElementButton_0b388c087d9de8c4f011dd323a130cfb,
  "@payloadcms/richtext-slate/client#ULElementButton": ULElementButton_0b388c087d9de8c4f011dd323a130cfb,
  "@payloadcms/richtext-slate/client#UnorderedListElement": UnorderedListElement_0b388c087d9de8c4f011dd323a130cfb,
  "@payloadcms/richtext-slate/client#UploadElementButton": UploadElementButton_0b388c087d9de8c4f011dd323a130cfb,
  "@payloadcms/richtext-slate/client#UploadElement": UploadElement_0b388c087d9de8c4f011dd323a130cfb,
  "@payloadcms/richtext-slate/client#WithUpload": WithUpload_0b388c087d9de8c4f011dd323a130cfb,
  "@payloadcms/storage-s3/client#S3ClientUploadHandler": S3ClientUploadHandler_f97aa6c64367fa259c5bc0567239ef24
}
