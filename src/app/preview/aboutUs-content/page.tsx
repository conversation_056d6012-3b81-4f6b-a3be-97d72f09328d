'use client';

import AboutUsComponent from '@/app/(frontend)/components/UI/aboutUsComponent';
import { fetchAboutUsContent } from '@/app/(frontend)/utils/apiClient';
import { serializeRichText } from '@/app/(frontend)/utils/functions';
import { RefreshRouteOnSave } from '@payloadcms/live-preview-react';
import { useEffect, useState } from 'react';

export default function AboutUsPreviewPage() {
  const [content, setContent] = useState<any>(null);

  const fetchPage = async () => {
    const data = await fetchAboutUsContent();
    const formattedContent = {
      ...data,
      description: serializeRichText(data?.description),
    };
    setContent(formattedContent);
  };

  useEffect(() => {
    fetchPage();
  }, []);

  // Automatically re-fetch when content is saved in admin panel
  RefreshRouteOnSave({
    refresh: () => {
      fetchPage();
    },
    serverURL: process.env.NEXT_PUBLIC_BACKEND_URL as string,
  });

  if (!content) return <p>Loading preview...</p>;

  return (
    <div>
      <AboutUsComponent content={content} />
    </div>
  );
}
