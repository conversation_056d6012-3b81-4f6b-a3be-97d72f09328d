import FAQClient from '@/app/(frontend)/(pages)/faq/FAQClient';
import GlassCardSSR from '@/app/(frontend)/components/Cards/GlassCardSSR';
import { fetchFaqUi } from '@/app/(frontend)/utils/apiClient';
import { ClientRefreshWrapper } from '../components/ClientRefreshWrapper';

// This is a server component
export default async function FAQPreviewPageSSR() {
  // Fetch data on the server
  const faqData = await fetchFaqUi();

  if (!faqData) {
    return <p>Failed to load data</p>;
  }

  return (
    <ClientRefreshWrapper fetchFunction="fetchFaqUi">
      <div>
        <GlassCardSSR
          title={faqData.title || 'Frequently Asked Questions'}
          description={faqData.subtitle || "Have questions? We've got answers!"}
          pathname="/faq"
          isAuthenticated={false}
          isPreview={true}
        />
        {/* Pass fetched data to the Client Component */}
        <FAQClient faqData={faqData} />
      </div>
    </ClientRefreshWrapper>
  );
}
