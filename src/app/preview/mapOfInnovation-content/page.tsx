'use client';

import GlassCard from '@/app/(frontend)/components/Cards/glassCard';
import SunburstChart from '@/app/(frontend)/components/UI/SunburstChart';
import { fetchLearnMoreUI, fetchSunburstUi } from '@/app/(frontend)/utils/apiClient';
import { RefreshRouteOnSave } from '@payloadcms/live-preview-react';
import Image from 'next/image';
import { useEffect, useState } from 'react';

export default function MapOfInnovationsPreviewPage() {
  const [learnMoreHero, setLearnMoreHero] = useState<any>(null);
  const [sunburstData, setSunburstData] = useState<any>(null);
  const customColors = ['#68c723', '#f25922', '#040B8c', '#feb924'];
  const logos = [
    '/images/icons/treat4.svg',
    '/images/icons/detect-icon.svg',
    '/images/icons/prevent-icon.svg',
    '/images/icons/build-icon.svg',
  ];

  const fetchPage = async () => {
    try {
      const heroData = await fetchLearnMoreUI();
      const chartData = await fetchSunburstUi();

      setLearnMoreHero(heroData);
      setSunburstData(chartData);
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    fetchPage();
  }, []);

  // Automatically re-fetch when content is saved in admin panel
  RefreshRouteOnSave({
    refresh: () => {
      fetchPage();
    },
    serverURL: process.env.NEXT_PUBLIC_BACKEND_URL as string,
  });

  if (!learnMoreHero) return <p>Loading preview...</p>;

  return (
    <div>
      <GlassCard
        title={learnMoreHero?.cardTitle || 'Map Of Innovations'}
        description={
          learnMoreHero?.cardDescription ||
          "The four pillars of tuberculosis (TB) are Detect, Treat, Prevent, and Build (DTPB). These pillars are part of the National Strategic Plan (NSP) for TB elimination in India, which was in effect from 2017 to 2025. The NSP's goal was to reduce the number of TB cases, deaths, and out-of-pocket costs."
        }
        isAuthenticated={false}
        pathname="/learn-more"
      />
      <div className="mx-auto max-w-[95vw] sm:max-w-[80vw] w-[80vw] mb-20">
        <section className="mb-10">
          {sunburstData ? (
            <>
              <div className="flex justify-center w-full items-center py-6">
                <SunburstChart data={sunburstData} />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 mb-10 gap-6 w-full">
                {sunburstData?.children?.map((itm: any, index: number) => {
                  return (
                    <div
                      key={index}
                      className={`border rounded-xl p-4 shadow-md w-full relative`}
                      style={{
                        borderColor: customColors[index % customColors.length],
                      }}
                    >
                      <div className="absolute top-2 right-2 text-gray-500 text-sm font-semibold">
                        {itm?.name}
                      </div>
                      <div className="flex items-center gap-4">
                        <div className="flex items-center justify-center w-12 h-12">
                          <Image
                            src={logos[index % logos.length]}
                            alt={`${itm?.name} Logo`}
                            width={48}
                            height={48}
                          />
                        </div>
                        <div>
                          <span className="text-6xl font-normal text-Midnight-Royal-Blue-02018B">
                            {itm?.innovationCount}
                          </span>
                          <span className=" ml-2 text-Midnight-Royal-Blue-02018B">Innovations</span>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </>
          ) : (
            <div className="text-center py-10">
              <p className="text-gray-500">Loading sunburst chart data...</p>
            </div>
          )}
        </section>
      </div>
    </div>
  );
}
