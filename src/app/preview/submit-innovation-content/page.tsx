'use client';

import GlassCard from '@/app/(frontend)/components/Cards/glassCard';
import ApplicationProcessSection from '@/app/(frontend)/components/UI/ApplicationProcessSection';
import Button from '@/app/(frontend)/components/UI/Button';
import { ApplicationProcessCardProps } from '@/app/(frontend)/types/data.types';
import {
  fetchCriteriaContent,
  fetchSubmitInnovationContent,
} from '@/app/(frontend)/utils/apiClient';
import { RefreshRouteOnSave } from '@payloadcms/live-preview-react';
import Image from 'next/image';
import { useEffect, useState } from 'react';

export default function SubmitInnovationPreviewPage() {
  const [content, setContent] = useState<any>(null);
  const [criteria, setCriteria] = useState<{ docs: ApplicationProcessCardProps[] } | null>(null);

  const fetchPage = async () => {
    try {
      const contentData = await fetchSubmitInnovationContent();
      setContent(contentData);

      // If the content has criteria relationship, fetch the criteria
      if (contentData?.criteria && contentData.criteria.length > 0) {
        // Criteria are already populated in the response
        if (typeof contentData.criteria[0] === 'object' && 'title' in contentData.criteria[0]) {
          setCriteria({ docs: contentData.criteria as ApplicationProcessCardProps[] });
        } else {
          // Need to fetch criteria separately
          const criteriaData = await fetchCriteriaContent();
          setCriteria(criteriaData);
        }
      } else {
        // Fallback to fetching all criteria
        const criteriaData = await fetchCriteriaContent();
        setCriteria(criteriaData);
      }
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    fetchPage();
  }, []);

  // Automatically re-fetch when content is saved in admin panel
  RefreshRouteOnSave({
    refresh: () => {
      fetchPage();
    },
    serverURL: process.env.NEXT_PUBLIC_BACKEND_URL as string,
  });

  if (!content) return <p>Loading preview...</p>;

  return (
    <div className="flex flex-col calculated-height mb-20">
      <div>
        <GlassCard
          title={content?.heroTitle}
          description={content?.heroDescription}
          pathname="/how-to-submit"
          isAuthenticated={Boolean(localStorage.getItem('atlasInfo'))}
        />
      </div>
      <div className="max-w-[95vw] sm:max-w-[80vw] mx-auto py-4 flex-1">
        <div className="py-4">
          <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-6 text-center sm:text-left">
            {content?.cardTitle}
          </h1>
          <div className="mb-4">
            <p className="text-base sm:text-lg md:text-xl leading-relaxed text-center sm:text-left">
              {content?.cardDescription}
            </p>
          </div>

          <Button
            title="Submit an Innovation"
            onClick={() => {}}
            variant="gradient"
            rightIcon={
              <Image
                alt="arrowIcon of Innovations"
                className="w-7 h-7 bg-white p-2 rounded-full "
                height={20}
                width={20}
                src={'/images/icons/darkArrowIcon.svg'}
              />
            }
          />
        </div>

        {criteria && criteria.docs && criteria.docs.length > 0 ? (
          <ApplicationProcessSection sections={criteria.docs} />
        ) : (
          <div className="text-center py-10">
            <p className="text-gray-500">
              No criteria sections available. Please add criteria in the admin panel.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
