'use client';

import FAQClient from '@/app/(frontend)/(pages)/faq/FAQClient';
import GlassCardSSR from '@/app/(frontend)/components/Cards/GlassCardSSR';
import { fetchFaqUi } from '@/app/(frontend)/utils/apiClient';
import { RefreshRouteOnSave } from '@payloadcms/live-preview-react';
import { useEffect, useState } from 'react';

export default function FAQPreviewPage() {
  const [faqData, setFaqData] = useState<any>(null);

  const fetchPage = async () => {
    const data = await fetchFaqUi();
    setFaqData(data);
  };

  useEffect(() => {
    fetchPage();
  }, []);

  // Automatically re-fetch when content is saved in admin panel
  RefreshRouteOnSave({
    refresh: () => {
      fetchPage();
    },
    serverURL: process.env.NEXT_PUBLIC_BACKEND_URL as string,
  });

  if (!faqData) return <p>Loading preview...</p>;

  return (
    <div>
      <GlassCardSSR
        title={faqData.title || 'Frequently Asked Questions'}
        description={faqData.subtitle || "Have questions? We've got answers!"}
        pathname="/faq"
        isAuthenticated
        isPreview={Boolean(localStorage.getItem('atlasInfo'))}
      />
      {/* Pass fetched data to the Client Component */}
      <FAQClient faqData={faqData} />
    </div>
  );
}
