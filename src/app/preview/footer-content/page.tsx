'use client';

import { fetchFooterContent } from '@/app/(frontend)/utils/footerClient';
import { fetchTotalInnovations } from '@/app/(frontend)/utils/apiClient';
import { RefreshRouteOnSave } from '@payloadcms/live-preview-react';
import Image from 'next/image';
import { useEffect, useState } from 'react';

export default function FooterPreviewPage() {
  const [footerContent, setFooterContent] = useState<any>(null);
  const [totalInnovations, setTotalInnovations] = useState<any>(null);

  const fetchPage = async () => {
    try {
      const footerData = await fetchFooterContent();
      const innovationsData = await fetchTotalInnovations();
      
      setFooterContent(footerData);
      setTotalInnovations(innovationsData);
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    fetchPage();
  }, []);

  // Automatically re-fetch when content is saved in admin panel
  RefreshRouteOnSave({
    refresh: () => {
      fetchPage();
    },
    serverURL: process.env.NEXT_PUBLIC_BACKEND_URL as string,
  });

  if (!footerContent) return <p>Loading preview...</p>;

  return (
    <div>
      <div className="flex mx-auto md:max-w-[95vw] sm:max-w-[80vw] md:justify-evenly w-[80vw] md:gap-10 gap-5 mb-10 flex-wrap justify-center">
        <Image
          priority
          alt="Partner logo"
          className="w-auto h-15 lg:h-17 bg-Pure-White-FFFFFF rounded-lg p-1"
          height="100"
          width="100"
          src={'images/illustrations/partners/GatesFoundation.svg'}
        />
        <Image
          priority
          alt="Partner logo"
          className="w-auto h-15 lg:h-17 bg-Pure-White-FFFFFF rounded-lg p-1"
          height="100"
          width="100"
          src={'images/illustrations/partners/TheUnion.svg'}
        />
      </div>

      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-[95vw] sm:max-w-[80vw] mx-auto px-6">
          {/* Header Section */}
          <div className="flex flex-col md:flex-row justify-between items-center mb-8 text-center md:text-left">
            <h1 className="text-2xl font-bold">{footerContent.footerTitle}</h1>
            <span className="text-gray-400 mt-2 md:mt-0">
              {totalInnovations?.totalDocs || 0} Innovations
            </span>
          </div>

          <p className="text-gray-400 text-center md:text-left mb-8">
            {footerContent.footerDescription}
          </p>

          <hr className="border-gray-600 mb-8" />

          {/* Quick Links */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4 text-center md:text-left">Quick Links</h2>
            <ul className="flex flex-wrap justify-center md:justify-start space-x-6 md:space-x-8 text-gray-400 text-sm md:text-base">
              {[
                { name: 'Home', link: '/' },
                { name: 'Browse Innovations', link: '/browse-innovations' },
                { name: 'About Us', link: '/about' },
                { name: 'Contact', link: '/contact-us' },
                { name: 'FAQ', link: '/faq' },
              ].map((link) => {
                return (
                  <li key={link.name}>
                    <a className="hover:text-white" href={link.link}>
                      {link.name}
                    </a>
                  </li>
                );
              })}
            </ul>
          </div>
          <hr className="border-gray-600" />
        </div>
      </footer>
    </div>
  );
}
