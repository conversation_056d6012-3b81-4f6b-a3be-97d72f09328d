'use client';

import ContactForm from '@/app/(frontend)/(pages)/contact-us/ContactForm';

const ContactComponent = ({ contactData }: any) => {
  return (
    <div className="flex items-center max-w-[95vw] sm:max-w-[80vw] mx-auto justify-center">
      <div className="bg-gray-100 items-center justify-center p-8 rounded-2xl my-8 w-full">
        <div className="max-w-[95vw] sm:max-w-[80vw] mx-auto p-6">
          <div className="flex flex-col md:flex-row justify-between">
            <div className="md:w-1/2 mb-8 md:mb-0">
              <div className="mb-6">
                <h2 className="text-2xl font-bold">Email</h2>
                <p className="text-lg mt-2">{contactData.email}</p>
              </div>
              <div className="mb-6">
                <h2 className="text-2xl font-bold">Phone</h2>
                <p className="text-lg mt-2">{contactData.phone}</p>
              </div>
              <div className="mb-6">
                <h2 className="text-2xl font-bold">Address</h2>
                <p className="text-lg mt-2" style={{ whiteSpace: 'pre-line' }}>
                  {contactData.address}
                </p>
              </div>
            </div>
            {/* Render the client component */}
            <ContactForm />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContactComponent;
