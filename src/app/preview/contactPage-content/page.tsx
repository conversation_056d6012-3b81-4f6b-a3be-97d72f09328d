import GlassCard from '@/app/(frontend)/components/Cards/glassCard';
import { fetchContsctUsUi } from '@/app/(frontend)/utils/apiClient';
import { cookies } from 'next/headers';
import ContactComponent from './ContactComponent';

export default async function ContactUsPreviewPage() {
  const contactData = await fetchContsctUsUi();
  const cookieStore = await cookies();

  if (!contactData) return <p>Loading preview...</p>;

  return (
    <div>
      <GlassCard
        title={contactData.contactTitle}
        description={contactData.contactSubtitle}
        pathname="/contact-us"
        isAuthenticated={Boolean(cookieStore.get('payload-token')?.value)}
      />
      <ContactComponent contactData={contactData} />
    </div>
  );
}
