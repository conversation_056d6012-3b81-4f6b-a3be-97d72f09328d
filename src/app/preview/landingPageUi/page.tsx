'use client';

import AtlasOfInnovations from '@/app/(frontend)/components/UI/HomeComponent';
import { fetchLandingPageUi } from '@/app/(frontend)/utils/apiClient';
import { serializeRichText } from '@/app/(frontend)/utils/functions';
import { RefreshRouteOnSave } from '@payloadcms/live-preview-react';
import { useEffect, useState } from 'react';

export default function PreviewPage() {
  const [page, setPage] = useState<any>(null);

  const fetchPage = async () => {
    const data = await fetchLandingPageUi();
    const landingPageContent = {
      ...data,
      heroSection: {
        heading: data.heroSection.heading,
        description: serializeRichText(data.heroSection.description),
        heroImage: data.heroSection.heroImage,
      },
    };
    setPage(landingPageContent);
  };

  useEffect(() => {
    fetchPage();
  }, []);

  // Automatically re-fetch when content is saved in admin panel
  RefreshRouteOnSave({
    refresh: () => {
      fetchPage();
    },
    serverURL: process.env.NEXT_PUBLIC_BACKEND_URL as string,
  });

  if (!page) return <p>Loading preview...</p>;

  return (
    <div>
      <AtlasOfInnovations landingPageContent={page} featuredInnovations={[]} innovationCount={0} />
    </div>
  );
}
