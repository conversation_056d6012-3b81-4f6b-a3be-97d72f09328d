'use client';

import GlassCard from '@/app/(frontend)/components/Cards/glassCard';
import Button from '@/app/(frontend)/components/UI/Button';
import { en } from '@/app/(frontend)/translations';
import { serializeRichText } from '@/app/(frontend)/utils/functions';
import { RefreshRouteOnSave } from '@payloadcms/live-preview-react';
import Image from 'next/image';
import Link from 'next/link';
import { useEffect, useState } from 'react';

export default function BrowseInnovationsPreviewPage() {
  const [innovationsHero, setInnovationsHero] = useState<any>(null);

  const fetchPage = async () => {
    try {
      const res = await fetch(
        `${process.env.NEXT_PUBLIC_BACKEND_URL}/api/globals/innovations-hero`,
      );
      if (!res.ok) throw new Error('Failed to fetch UI content (browse innovation)');
      const data = await res.json();

      const formattedData = {
        ...data,
        description: serializeRichText(data.description),
      };

      setInnovationsHero(formattedData);
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    fetchPage();
  }, []);

  // Automatically re-fetch when content is saved in admin panel
  RefreshRouteOnSave({
    refresh: () => {
      fetchPage();
    },
    serverURL: process.env.NEXT_PUBLIC_BACKEND_URL as string,
  });

  if (!innovationsHero) return <p>Loading preview...</p>;

  return (
    <div>
      <GlassCard
        title={innovationsHero.title}
        isInnerHTML
        description={innovationsHero.description}
        pathname="/browse-innovations"
        isAuthenticated={false}
      />
      <div className="calculated-height max-w-[95vw] sm:max-w-[80vw] w-full mx-auto">
        <section className="flex-1 mt-4 w-full mb-20">
          <div className="py-4 pointer-events-none">
            <Link href="/how-to-submit">
              <Button
                variant="gradient"
                rightIcon={
                  <Image
                    alt="arrowIcon of Innovations"
                    className="w-7 h-7 bg-white p-2 rounded-full"
                    height={20}
                    width={20}
                    src="/images/icons/darkArrowIcon.svg"
                  />
                }
                className="pointer-events-auto"
                title={en.SUBMIT_YOUR_INNOVATION}
              />
            </Link>
          </div>
          <div className="text-center py-10">
            <p className="text-gray-500">
              This is a preview of the Browse Innovations page header. The actual innovations
              listing would appear here in the live site.
            </p>
          </div>
        </section>
      </div>
    </div>
  );
}
