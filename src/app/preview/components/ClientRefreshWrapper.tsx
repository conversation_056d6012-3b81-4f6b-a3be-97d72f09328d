'use client';

import { RefreshRouteOnSave } from '@payloadcms/live-preview-react';
import { useRouter } from 'next/navigation';
import React from 'react';

interface ClientRefreshWrapperProps {
  children: React.ReactNode;
  fetchFunction: string;
}

export const ClientRefreshWrapper: React.FC<ClientRefreshWrapperProps> = ({ children }) => {
  const router = useRouter();

  // Set up the refresh handler for Payload CMS live preview
  RefreshRouteOnSave({
    refresh: () => {
      // Refresh the current route to get the latest data
      router.refresh();
    },
    serverURL: process.env.NEXT_PUBLIC_BACKEND_URL as string,
  });

  return <>{children}</>;
};
