import config from '@payload-config';
import { MetadataRoute } from 'next';
import { getPayload } from 'payload';

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const payload = await getPayload({ config }); // Ensure Payload is initialized here

  // Static pages
  const staticPages = [
    'home',
    'browse-innovations',
    'learn-more',
    'submit-an-innovation',
    'about',
    'contact-us',
    'faq',
  ].map((route) => ({
    url: `https://www.atlasoftbinnovations.in/${route === 'home' ? '' : route}`,
    lastModified: new Date().toISOString(),
    changeFrequency: 'weekly' as const,
    priority: 0.8,
  }));

  try {
    // Fetch innovations dynamically using Payload Local API
    const { docs: innovations } = await payload.find({
      collection: 'innovations',
      where: { status: { equals: 'approved' } },
      limit: 1000,
      depth: 0,
      pagination: false,
    });

    const dynamicPages = innovations.map((innovation: any) => ({
      url: `https://www.atlasoftbinnovations.in/innovations/${innovation.slug}`,
      lastModified: innovation.updatedAt || new Date().toISOString(),
      changeFrequency: 'monthly' as const,
      priority: 0.7,
    }));

    return [...staticPages, ...dynamicPages];
  } catch (error) {
    console.error('Error fetching innovations for sitemap:', error);
    return staticPages;
  }
}
