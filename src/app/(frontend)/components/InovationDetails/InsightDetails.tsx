import React from 'react';
import { DetailItemProps, InsightsDetailsProps, SupportingDocument } from '../../types/data.types';

const DetailItem: React.FC<DetailItemProps> = ({
  label,
  value,
  textColor = 'text-[#29CC39]',
  isLink = false,
}) => {
  if (!value || (Array.isArray(value) && value.length === 0)) return null;
  return (
    <div className="mb-4">
      <h5 className="text-sm font-semibold text-gray-700">{label}</h5>
      <div className="mt-1">
        {isLink && typeof value === 'string' ? (
          <a
            href={value.startsWith('http') ? value : `https://${value}/`}
            target="_blank"
            rel="noopener noreferrer"
            className={`text-sm font-medium ${textColor} hover:underline bg-white p-2 rounded-lg inline-block`}
          >
            {value}
          </a>
        ) : Array.isArray(value) ? (
          <div className="flex flex-wrap gap-2">
            {value.map((item) => {
              // Handle supporting document links
              if ((item as SupportingDocument).url) {
                const doc = item as SupportingDocument;
                return (
                  <a
                    key={doc?.id}
                    href={encodeURI(process.env._AWS_END_POINT + '/' + doc?.filename)}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={`text-sm font-medium ${textColor} bg-white px-3 py-1 rounded-full shadow-sm`}
                  >
                    {doc?.filename}
                  </a>
                );
              }

              // Handle nested value object
              if ((item as any).value) {
                const nested = item as any;
                return (
                  <span
                    key={nested.id}
                    className={`text-sm font-medium ${textColor} bg-white px-3 py-1 rounded-full shadow-sm`}
                  >
                    {nested.value}
                  </span>
                );
              }

              // Fallback for direct string or value
              return (
                <span
                  key={JSON.stringify(item)}
                  className={`text-sm font-medium ${textColor} bg-white px-3 py-1 rounded-full shadow-sm`}
                >
                  {String(item)}
                </span>
              );
            })}
          </div>
        ) : typeof value === 'object' && 'value' in value ? (
          <span className={`text-sm font-medium ${textColor} bg-white p-2 rounded-lg inline-block`}>
            {value.value}
          </span>
        ) : (
          <span className={`text-sm font-medium ${textColor} bg-white p-2 rounded-lg inline-block`}>
            {String(value)}
          </span>
        )}
      </div>
    </div>
  );
};

const InsightDetails: React.FC<InsightsDetailsProps> = ({ innovationDetails }) => {
  const details = [
    {
      label: 'Current Stage',
      value: innovationDetails.currentStageOfDevelopment,
    },
    { label: 'Intervention Type', value: innovationDetails.interventionType },
  ];

  return (
    <div className="rounded-3xl bg-[#F6F6F6] p-6 shadow-lg border border-gray-200">
      <h3 className="text-lg font-bold text-gray-800 mb-4">Innovation Insights</h3>
      <div className="space-y-2">
        {details.map((detail) =>
          detail.value ? (
            <React.Fragment key={detail.label}>
              <DetailItem
                label={detail.label}
                value={detail.value}
                // isLink={detail.isLink}
                textColor="text-[#29CC39]"
              />
              <hr className="border-t border-[#C1B9E8] opacity-50 my-3" />
            </React.Fragment>
          ) : null,
        )}
        {/* {innovationDetails.supportingDocument &&
          innovationDetails.supportingDocument.length > 0 && (
            <DetailItem
              label="Supporting Documents"
              value={innovationDetails.supportingDocument}
              textColor="text-[#29CC39]"
            />
          )} */}
      </div>
    </div>
  );
};

export default InsightDetails;
