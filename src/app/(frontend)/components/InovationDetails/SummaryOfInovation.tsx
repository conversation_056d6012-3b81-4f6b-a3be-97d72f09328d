'use client';
import dynamic from 'next/dynamic';
import { SummaryOfInnovationProps } from '../../types/data.types';
const ReactQuill = dynamic(() => import('react-quill-new'), {
  ssr: false,
});

export default function SummaryOfInnovation({ description }: SummaryOfInnovationProps) {
  return (
    <div className="my-5">
      <ReactQuill
        value={description}
        readOnly={true}
        className="p-0 m-0 ql-editor"
        theme="bubble"
        style={{
          padding: 0,
          margin: 0,
          border: 'none',
        }}
      />
    </div>
  );
}
