'use client';
import { InsightsLessonsProps } from '../../types/data.types';
import DescriptionTooltip from '../UI/DescriptionTooltip';

export default function InsightsLessons({
  innovationDetails,
  className = '', // Default to empty string
}: InsightsLessonsProps) {
  // Combine all categories into a single array
  const allCategories: any = [];
  // Add intervention types
  if (innovationDetails.interventionType && innovationDetails.interventionType.length > 0) {
    allCategories.push(...innovationDetails.interventionType);
  }

  return (
    <div className={`py-2 flex flex-wrap gap-2 ${className}`}>
      {allCategories.length > 0 &&
        allCategories.map(
          (v: { description: string | null; slug?: string; title: string }, i: number) => (
            <div key={i} className="text-gray-500 uppercase font-medium tracking-wide">
              <DescriptionTooltip
                description={v?.description ?? ''}
                position="right-center"
                maxWords={20}
                shouldShowReadMore={true}
                slug={v?.slug}
              >
                <div
                  className={
                    (v?.description || '')?.length >= 15
                      ? `cursor-pointer underline`
                      : 'cursor-default '
                  }
                >
                  {v?.title ?? v} {allCategories?.length - 1 !== i && <span className="">,</span>}
                </div>
              </DescriptionTooltip>
            </div>
          ),
        )}
    </div>
  );
}
