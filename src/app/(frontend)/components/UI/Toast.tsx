'use client';

import { AnimatePresence, motion } from 'framer-motion';
import { useEffect, useState } from 'react';
import { ToastProps } from '../../types/data.types';

const Toast: React.FC<ToastProps> = ({ message, type = 'info', duration = 4000, onClose }) => {
  const [show, setShow] = useState(true);
  const [progress, setProgress] = useState(100);

  useEffect(() => {
    const startTime = Date.now();
    let timer: NodeJS.Timeout;

    const updateProgress = () => {
      const elapsedTime = Date.now() - startTime;
      const newProgress = 100 - (elapsedTime / duration) * 100;
      setProgress(Math.max(0, newProgress));

      if (elapsedTime < duration) {
        timer = setTimeout(updateProgress, 16); // ~60fps
      } else {
        handleClose();
      }
    };

    updateProgress();

    return () => clearTimeout(timer);
  }, [duration]);

  const handleClose = () => {
    setShow(false);
    // Delay onClose to match animation
    setTimeout(() => {
      if (onClose) onClose();
    }, 300);
  };

  const typeStyles = {
    success: 'bg-gradient-to-r from-green-100 to-green-200 text-green-700 border border-green-300',
    error: 'bg-gradient-to-r from-red-100 to-red-200 text-red-700 border border-red-300',
    info: 'bg-gradient-to-r from-blue-100 to-blue-200 text-blue-700 border border-blue-300',
  };

  const progressColors = {
    success: '#34D399',
    error: '#F87171',
    info: '#60A5FA',
  };

  return (
    <AnimatePresence>
      {show && (
        <motion.div
          initial={{ opacity: 0, x: 50, y: 0 }}
          animate={{ opacity: 1, x: 0, y: 0 }}
          exit={{ opacity: 0, x: 50, y: 0 }}
          transition={{ type: 'spring', stiffness: 400, damping: 30 }}
          className={`z-[9999] fixed top-5 right-5 px-5 py-3 rounded-2xl shadow-lg overflow-hidden w-80 ${typeStyles[type]}`}
        >
          <div className="flex items-center justify-between">
            <motion.span
              initial={{ opacity: 0, y: 5 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="font-medium"
            >
              {message}
            </motion.span>
            <motion.button
              type="button"
              whileHover={{ scale: 1.2 }}
              whileTap={{ scale: 0.9 }}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                handleClose();
              }}
              className="text-gray-500 hover:text-gray-700 text-xl font-bold"
            >
              &times;
            </motion.button>
          </div>

          {/* Progress Bar */}
          <div className="w-full h-1 mt-3 bg-gray-300 rounded-full overflow-hidden">
            <motion.div
              initial={{ width: '100%' }}
              animate={{ width: `${progress}%` }}
              transition={{ duration: 0.1, ease: 'linear' }}
              className="h-full bg-opacity-75"
              style={{
                backgroundColor: progressColors[type],
              }}
            />
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default Toast;
