'use client';
import { motion } from 'framer-motion';
import React from 'react';
import { ButtonProps } from '../../types/data.types';

const Button: React.FC<ButtonProps> = ({
  title,
  onClick,
  className = '',
  variant = 'gradient',
  leftIcon,
  rightIcon,
  solidColor = '#ffffff',
  disabled = false,
  size = 'md',
  loading = false,
  loadingText,
  type = 'button',
}) => {
  const isDisabled = disabled || loading;

  const sizeClasses = {
    sm: 'px-4 py-2 text-sm',
    md: 'px-5 py-3 text-base',
    lg: 'px-6 py-4 text-lg',
    xl: 'px-8 py-5 text-xl',
  }[size];

  const baseClasses =
    'rounded-full font-bold flex items-center justify-center transition-all duration-300 shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 font-popins';

  let variantStyles = '';
  switch (variant) {
    case 'gradient':
      variantStyles =
        'bg-gradient-to-r from-[#00008B] to-[#FF4500] text-Pure-White-FFFFFF hover:bg-gradient-to-t from-[#00008B] to-[#FF4500]';
      break;
    case 'outline':
      variantStyles =
        'border-2 border-Dark-Blue-00008B text-Dark-Blue-00008B bg-transparent hover:bg-Dark-Blue-00008B/10 focus:Dark-Blue-00008B';
      break;
    case 'transparent':
      variantStyles =
        'bg-Pure-White-FFFFFF/10 border-2 border-Pure-White-FFFFFF/20 backdrop-blur-lg hover:bg-Pure-White-FFFFFF/20 focus:ring-Pure-White-FFFFFF/30';
      break;
    case 'solid':
      variantStyles = 'hover:opacity-90 focus:ring-current';
      break;
  }

  const disabledStyles = isDisabled
    ? 'opacity-70 cursor-not-allowed !transform-none'
    : '';

  const loadingSpinner = (
    <svg
      className="animate-spin h-5 w-5 mr-3 text-Pure-White-FFFFFF"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
    >
      <circle
        className="opacity-25"
        cx="12"
        cy="12"
        r="10"
        stroke="currentColor"
        strokeWidth="4"
      ></circle>
      <path
        className="opacity-75"
        fill="currentColor"
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      ></path>
    </svg>
  );

  return (
    <motion.button
      type={type}
      onClick={!isDisabled ? onClick : undefined}
      disabled={isDisabled}
      className={`${sizeClasses} ${baseClasses} ${variantStyles} ${disabledStyles} ${className} cursor-pointer`}
      style={variant === 'solid' ? { backgroundColor: solidColor } : undefined}
      aria-disabled={isDisabled}
      whileHover={!isDisabled ? { scale: 1.05, boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)' } : {}}
      whileTap={!isDisabled ? { scale: 0.98 } : {}}
      transition={{ 
        type: "spring", 
        stiffness: 400, 
        damping: 10
      }}
    >
      {loading ? (
        <>
          {loadingSpinner}
          {loadingText || title}
        </>
      ) : (
        <>
          {leftIcon && <span className="mr-3">{leftIcon}</span>}
          <span className="whitespace-nowrap font-bold font-Poppins">{title}</span>
          {rightIcon && <span className="ml-3">{rightIcon}</span>}
        </>
      )}
    </motion.button>
  );
};

export default Button;
