import React from 'react';
import { ApplicationProcessCardProps } from '../../types/data.types';
import CircularLoader from './CircularLoader';

const ApplicationProcessSection: React.FC<{ sections: ApplicationProcessCardProps[] }> = ({
  sections,
}) => {
  if (!sections || sections.length === 0) {
    return <CircularLoader />;
  }

  return (
    <div className="max-w-[95vw] sm:max-w-[80vw] mx-auto">
      <div className="space-y-6">
        {sections
          .sort((a, b) => a.number - b.number)
          .map((section) => (
            <ApplicationProcessCard
              key={section.number}
              number={section.number}
              title={section.title}
              items={section.items}
            />
          ))}
      </div>
    </div>
  );
};

const ApplicationProcessCard: React.FC<ApplicationProcessCardProps> = ({
  number,
  title,
  items,
}) => (
  <div className="bg-white rounded-lg shadow-md mb-8 p-6">
    <div className="flex flex-col lg:flex-row gap-6">
      <div className="flex items-center gap-4 lg:w-1/3">
        <span className="text-7xl font-extralight text-gray-400">{number}</span>
        <h2 className="text-2xl lg:text-3xl font-bold">{title}</h2>
      </div>
      <div className="lg:w-2/3 space-y-3">
        {items.map((item, index) => (
          <div key={index} className="flex gap-3 text-gray-600">
            <span className="text-gray-600">•</span>
            <p>{item.text}</p>
          </div>
        ))}
      </div>
    </div>
  </div>
);

export default ApplicationProcessSection;
