'use client';
import GlassCard from '@/app/(frontend)/components/Cards/glassCard';
import { submitInnovation } from '@/app/(frontend)/utils/apiClient';
import { redirect } from 'next/navigation';
import { Suspense, useState } from 'react';
import InnovationFormOriginal from '../Forms/InnovationFormOrignal';
import InnovationPreview from '../Forms/InnovationPreview';

export default function InnovationForm() {
  const [showPreview, setShowPreview] = useState<boolean>(false);
  const [innovationData, setInnovationData] = useState<FormData | null>(null);
  const handleSuccess = (data: any) => {
    setInnovationData(data);
    setShowPreview(true);
  };
  const handleBack = (data: any) => {
    data.currentStageOfDevelopment = data?.currentStageOfDevelopment?.id;
    setInnovationData(data);
    setShowPreview(false);
  };
  const onPreviewSubmit = () => {
    redirect(`/browse-innovations?myInnovations=${encodeURIComponent('true')}`);
  };
  return (
    <div>
      {!showPreview ? (
        <>
          <div>
            <Suspense fallback={<div />}>
              <GlassCard
                title={'Submit your Innovation'}
                description={
                  'Join us in making a difference in the path to Ending TB. Your innovation could save lives and transform the future of TB management.'
                }
                isAuthenticated={true}
                pathname="/how-to-submit/submit-an-innovation"
              />
            </Suspense>
            <div className="mt-5">
              <div className="max-w-[95vw] sm:max-w-[80vw] mx-auto">
                {/* <h1 className="text-[50px] text-Midnight-Royal-Blue-02018B font-normal px-2">
                  Call for Innovations: India Innovation Summit 2025 - Pioneering solutions to End
                  TB
                </h1> */}
                <p className="text-lg text-gray-600 mt-2 px-2">
                  We are inviting applicants from innovation with groundbreaking solutions and ideas
                  in the fight against TB.{' '}
                </p>
              </div>
            </div>
            <div className="flex flex-col items-center justify-center">
              <InnovationFormOriginal
                onSuccess={handleSuccess}
                submitInnovation={submitInnovation}
                defaultValues={innovationData || undefined}
              />
            </div>
          </div>
        </>
      ) : (
        <InnovationPreview
          onBack={handleBack}
          currentInnovationData={innovationData}
          onSubmit={onPreviewSubmit}
        />
      )}
    </div>
  );
}
