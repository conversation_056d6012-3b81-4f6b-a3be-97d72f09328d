'use client';
import Image from 'next/image';
import { useEffect, useState } from 'react';

interface FullPageBackgroundProps {
  backgroundImages: string[];
  interval?: number;
}

const FullPageBackground: React.FC<FullPageBackgroundProps> = ({
  backgroundImages = [],
  interval = 5000,
}) => {
  const [currentIndex, setCurrentIndex] = useState<number>(0);
  const [isLoading, setIsLoading] = useState(true);

  // Track which images have loaded

  // Handle image loading
  const handleImageLoad = () => {
    setIsLoading(false);
  };

  // Set up the image rotation interval
  useEffect(() => {
    // Only set up the interval if there are multiple images
    if (backgroundImages.length > 1) {
      const intervalId = setInterval(() => {
        setCurrentIndex((prevIndex) => (prevIndex + 1) % backgroundImages.length);
      }, interval);

      return () => clearInterval(intervalId);
    }
  }, [backgroundImages.length, interval]);

  return (
    <div className="absolute inset-0 w-full h-full -z-10 overflow-hidden">
      {/* Black Glass Loading Animation */}
      <div
        className={`absolute inset-0 bg-black/80 backdrop-blur-md transition-opacity duration-1000 ${
          isLoading ? 'opacity-100' : 'opacity-0'
        }`}
        style={{
          zIndex: 5,
        }}
      >
        <div className="absolute inset-0 flex flex-col items-center justify-center">
          {/* Pulse Animation Container */}
          <div className="relative">
            {/* Pulsing Circles */}
            <div className="absolute inset-0 animate-pulse-ring"></div>
            <div
              className="absolute inset-0 animate-pulse-ring"
              style={{ animationDelay: '0.4s' }}
            ></div>
            <div
              className="absolute inset-0 animate-pulse-ring"
              style={{ animationDelay: '0.8s' }}
            ></div>
          </div>
        </div>
      </div>

      {/* Background Images */}
      {backgroundImages.map((src, index) => (
        <Image
          key={index}
          src={(process.env._AWS_END_POINT || '') + '/' + src}
          alt={`Background Image ${index + 1}`}
          fill
          sizes="100vw"
          priority={index === 0}
          quality={100}
          onLoad={() => handleImageLoad()}
          className={`object-cover w-full h-full transition-opacity duration-[1500ms] ease-in-out ${
            index === currentIndex ? 'opacity-100' : 'opacity-0'
          } ${isLoading && index === 0 ? 'animate-fade-in' : ''}`}
          style={{
            objectPosition: 'center',
            objectFit: 'cover',
            transform: index === currentIndex ? 'scale(1.05)' : 'scale(1)',
            transition: 'transform 10s ease-out, opacity 1.5s ease-in-out',
          }}
        />
      ))}

      {/* Modern Gradient Overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-black/60 via-black/40 to-transparent" />

      {/* Subtle Noise Texture for Depth */}
      <div className="absolute inset-0 opacity-10 mix-blend-overlay bg-[url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMDAiIGhlaWdodD0iMzAwIj48ZmlsdGVyIGlkPSJhIiB4PSIwIiB5PSIwIj48ZmVUdXJidWxlbmNlIGJhc2VGcmVxdWVuY3k9Ii43NSIgc3RpdGNoVGlsZXM9InN0aXRjaCIgdHlwZT0iZnJhY3RhbE5vaXNlIi8+PGZlQ29sb3JNYXRyaXggdHlwZT0ic2F0dXJhdGUiIHZhbHVlcz0iMCIvPjwvZmlsdGVyPjxwYXRoIGQ9Ik0wIDBoMzAwdjMwMEgweiIgZmlsdGVyPSJ1cmwoI2EpIiBvcGFjaXR5PSIuMDUiLz48L3N2Zz4=')]" />

      {/* Subtle Vignette Effect */}
      <div
        className="absolute inset-0 opacity-50 pointer-events-none"
        style={{
          background: 'radial-gradient(circle, transparent 0%, rgba(0, 0, 0, 0.7) 100%)',
        }}
      ></div>
    </div>
  );
};

export default FullPageBackground;
