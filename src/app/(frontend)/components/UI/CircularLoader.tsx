const CircularLoader = () => {
  return (
    <div className="flex flex-col items-center justify-center h-[300px] w-full relative overflow-hidden bg-transparent">
      {/* Background Animation */}
      <div className="absolute inset-0" aria-hidden="true">
        <div className="absolute inset-0 opacity-40">
          <div className="absolute top-1/4 left-1/3 w-48 h-48 bg-blue-200/40 rounded-full blur-2xl animate-blob" />
          <div className="absolute top-1/3 right-1/4 w-48 h-48 bg-purple-200/40 rounded-full blur-2xl animate-blob delay-200" />
          <div className="absolute bottom-1/3 left-1/4 w-48 h-48 bg-teal-200/40 rounded-full blur-2xl animate-blob delay-400" />
        </div>
      </div>
      {/* Spinning Loader Content */}
      <div className="relative w-24 h-24 border-[6px] border-gray-300 rounded-full">
        {/* Apply <PERSON><PERSON><PERSON>'s built-in animate-spin */}
        <div className="absolute inset-0 border-t-[6px] border-blue-400 rounded-full animate-spin !important" />
      </div>
    </div>
  );
};

export default CircularLoader;
