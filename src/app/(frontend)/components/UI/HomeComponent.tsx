'use client';

import { en } from '@/app/(frontend)/translations';
import Image from 'next/image';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { AtlasOfInnovationsProps } from '../../types/data.types';
import { InnovationCard } from '../Cards/innovationCard';
import MapOfInnovations from '../Cards/InnovationSectionCard';
import MarqueeStrip from '../Cards/marqueeStrip';
import Container from '../Layout/Container';
import Section from '../Layout/Section';
import FullPageBackground from './FullPageBackground';
import SunburstChart from './SunburstChart';

const AtlasOfInnovations = ({
  featuredInnovations,
  landingPageContent,
  innovationCount,
}: AtlasOfInnovationsProps) => {
  const [data, setData] = useState<any>();

  const build = `${process.env.NEXT_PUBLIC_BACKEND_URL}/images/icons/build-icon.svg`;
  const detect = `${process.env.NEXT_PUBLIC_BACKEND_URL}/images/icons/detect-icon.svg`;
  const treat = `${process.env.NEXT_PUBLIC_BACKEND_URL}/images/icons/treat4.svg`;
  const prevent = `${process.env.NEXT_PUBLIC_BACKEND_URL}/images/icons/prevent-icon.svg`;

  const iconMap: Record<string, string> = {
    Build: build,
    Detect: detect,
    Treat: treat,
    Prevent: prevent,
  };

  const fetchSunburst = async () => {
    try {
      const response = await fetch('/api/sunburst/sunburst-data');
      if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);

      const jsonData = await response.json();
      if (jsonData?.children) {
        jsonData.children.forEach((value: any) => {
          if (iconMap[value?.name]) {
            value.icon = iconMap[value.name];
          }
        });
      }
      setData(jsonData);
    } catch (error) {
      console.error('Error fetching innovations:', error);
    }
  };

  useEffect(() => {
    fetchSunburst();
  }, []);

  return (
    <div className="w-full">
      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-start pt-30">
        <FullPageBackground backgroundImages={landingPageContent?.heroSection?.sliderImages} />

        <Container className="relative z-10 py-20">
          <div className="flex flex-col lg:flex-row items-center lg:items-start gap-10">
            {/* Left Content */}
            <div className="w-full lg:w-3/5 text-center lg:text-left space-y-8 backdrop-blur-sm bg-black/30 p-6 rounded-xl border border-white/10 shadow-lg">
              <div className="relative">
                <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold leading-tight text-transparent bg-gradient-to-r from-white via-white to-white/80 bg-clip-text animate-fade-in-up">
                  {landingPageContent?.heroSection?.heading}
                </h1>
                <div className="hidden lg:block absolute -bottom-2 left-0 w-1/2 h-1 bg-gradient-to-r from-[#0F06D2] via-[#85367A] to-[#F86525] rounded-full"></div>
              </div>

              <div>
                <p
                  className="text-white/90 text-base sm:text-lg leading-relaxed"
                  dangerouslySetInnerHTML={{ __html: landingPageContent?.heroSection?.description }}
                />
              </div>

              <div className="flex flex-col sm:flex-row gap-5">
                {/* Explore Innovations Button */}
                <Link href="/browse-innovations" className="group w-full sm:w-auto">
                  <div className="flex items-center justify-center bg-white/10 hover:bg-white/20 backdrop-blur-md border border-white/20 rounded-full py-3 px-6 transition-all duration-300">
                    <span className="text-base sm:text-lg font-medium text-white mr-2 sm:mr-3">
                      {en.EXPLORE_INNOVATIONS} ({innovationCount})
                    </span>
                    <div className="flex items-center justify-center bg-white rounded-full p-1 sm:p-2 transition-all duration-300 group-hover:translate-x-1">
                      <Image
                        src="/images/icons/darkArrowIcon.svg"
                        alt="Explore"
                        width={16}
                        height={16}
                        className="w-4 h-4 sm:w-5 sm:h-5"
                      />
                    </div>
                  </div>
                </Link>

                {/* Submit Innovation Button */}
                <Link href="/how-to-submit" className="group w-full sm:w-auto">
                  <div className="flex items-center justify-center bg-gradient-to-r from-[#0F06D2] via-[#85367A] to-[#F86525] hover:shadow-lg hover:shadow-[#F86525]/30 rounded-full py-3 px-6 transition-all duration-300">
                    <span className="text-base sm:text-lg font-medium text-white mr-2 sm:mr-3">
                      {en.SUBMIT_AN_INNOVATION}
                    </span>
                    <div className="flex items-center justify-center bg-white rounded-full p-1 sm:p-2 transition-all duration-300 group-hover:translate-x-1">
                      <Image
                        src="/images/icons/darkArrowIcon.svg"
                        alt="Submit"
                        width={16}
                        height={16}
                        className="w-4 h-4 sm:w-5 sm:h-5"
                      />
                    </div>
                  </div>
                </Link>
              </div>
            </div>
          </div>
        </Container>
      </section>

      {/* Map of Innovations Section */}
      <Section>
        <Container className="flex flex-col lg:flex-row items-center gap-2">
          <div className="w-full lg:w-2/5">
            <MapOfInnovations
              buttonText={en.LEARN_MORE}
              description={landingPageContent?.mapSection?.description}
              imageUrl=""
              ButtonHref="/learn-more"
              title={landingPageContent?.mapSection?.title}
            />
          </div>

          <div className="w-full lg:w-3/5 flex justify-center items-center lg:mt-0">
            <SunburstChart data={data} />
          </div>
        </Container>
      </Section>

      {/* Featured Innovations */}
      <Section>
        <Container>
          <MapOfInnovations
            ButtonHref="/browse-innovations"
            buttonText={en.LEARN_MORE}
            description={landingPageContent?.featuredInnovationsSection?.description}
            imageUrl=""
            title={landingPageContent?.featuredInnovationsSection?.title}
          />

          <div className="mt-5 grid gap-8 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
            {featuredInnovations?.map((item) => (
              <InnovationCard
                key={item.id}
                item={item as any}
                isMyInnovation={false}
                isFuture={true}
              />
            ))}
          </div>
        </Container>
      </Section>

      {/* Marquee */}
      {landingPageContent?.marqueeContent && (
        <Section className="h-50 mt-0 pt-0 mb-20">
          <Container>
            <MarqueeStrip stripContent={landingPageContent.marqueeContent} itsBlue />
          </Container>
        </Section>
      )}
    </div>
  );
};

export default AtlasOfInnovations;
