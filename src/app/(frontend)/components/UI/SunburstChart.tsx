'use client';
/**
 * SunburstChart Component
 *
 * This component renders an interactive sunburst chart visualization using D3.js.
 * It displays hierarchical data in a radial layout, allowing users to drill down into categories.
 *
 * Features:
 * - Interactive segments that can be clicked to zoom in
 * - Modern tooltips with contextual information
 * - Responsive design that adapts to container size
 * - Animated transitions between different views
 * - Center text showing current selection
 * - Dynamic icon display based on node data
 *
 * Icon Handling Logic:
 * - Root nodes: Icons are NEVER shown for root nodes
 * - Non-root nodes: Icons are shown for specific categories (Build, Detect, Treat, Prevent)
 * - Center icon: Only shows icons for non-root nodes that have an icon property
 * - When navigating back to root, any center icon is hidden
 *
 * The chart is particularly useful for visualizing hierarchical data structures
 * such as categorized innovations or nested classifications.
 */
import * as d3 from 'd3';
import { useEffect, useRef, useState } from 'react';
import { SunburstNode } from '../../types/data.types';
import { getWindow } from '../../utils/clientUtils';
import SunburstSkeletonLoader from './SunburstSkeletonLoader';

/**
 * SunburstChart Component
 *
 * @param {Object} props - Component props
 * @param {SunburstNode} props.data - Hierarchical data structure for the sunburst chart
 */
const SunburstChart = ({ data }: { data: SunburstNode }) => {
  // References for SVG element and container div
  const svgRef = useRef<SVGSVGElement | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Color palette for the chart segments
  // These colors will be used to differentiate between top-level categories
  const customColors = ['#feb924', '#f25922', '#68c723', '#040B8c'];

  // State for chart dimensions, responsive to container size
  const [dimensions, setDimensions] = useState({ width: 900, height: 900 });

  /**
   * Handle responsive resizing of the chart
   * This effect ensures the chart adapts to the container width while maintaining aspect ratio
   */
  useEffect(() => {
    const window = getWindow();
    if (!window) return;
    // Update dimensions based on container width
    const updateDimensions = () => {
      if (containerRef.current) {
        // Get the current width of the container
        const containerWidth = containerRef.current.offsetWidth;
        // Cap size at 900px for larger screens to prevent the chart from becoming too large
        const newSize = Math.min(containerWidth, 900);
        // Maintain square aspect ratio for the chart
        setDimensions({ width: newSize, height: newSize });
      }
    };

    // Calculate initial size when component mounts
    updateDimensions();

    // Add resize listener to update dimensions when window size changes
    window?.addEventListener('resize', updateDimensions);

    // Clean up event listener on component unmount
    return () => window?.removeEventListener('resize', updateDimensions);
  }, []);

  /**
   * Main effect for rendering and updating the chart
   * This creates the sunburst visualization and sets up all interactions
   */
  useEffect(() => {
    // Skip rendering if SVG ref is not available or data is missing
    if (!svgRef.current || !data) return;

    // Cleanup function to remove tooltip when component unmounts
    const cleanup = () => {
      d3.select('body').select('.sunburst-tooltip').remove();
    };

    const { width, height } = dimensions;
    const isMobile = width <= 600; // Check if the screen is small
    const radius = Math.min(width, height) / (isMobile ? 6 : 9); // Larger radius for mobile
    const fontSize = isMobile ? 8 : 10; // Larger font size for mobile

    // Clear existing SVG content
    d3.select(svgRef.current).selectAll('*').remove();

    // Create color scale
    const color = d3.scaleOrdinal(customColors);

    // Create hierarchy and partition
    const hierarchy: any = d3
      .hierarchy<SunburstNode>(data)
      .sum((d) => d.value || 0)
      .sort((a, b) => (b.value || 0) - (a.value || 0));

    const root: any = d3.partition().size([2 * Math.PI, hierarchy.height + 1])(hierarchy);
    root.each((d: any) => (d.current = d));

    // Create modern tooltip with enhanced styling
    // First, remove any existing tooltip to prevent duplicates when re-rendering
    d3.select('body').select('.sunburst-tooltip').remove();

    // Create tooltip with modern styling and animations
    const tooltip = d3
      .select('body')
      .append('div')
      .attr('class', 'sunburst-tooltip') // Add class for easier selection
      .style('position', 'absolute')
      .style('background', 'rgba(255, 255, 255, 0.98)') // Slightly transparent background
      .style('border', 'none') // Remove border for cleaner look
      .style('border-radius', '8px') // Rounded corners
      .style('padding', '12px 16px') // More generous padding
      .style('font-size', `${fontSize}px`)
      .style('box-shadow', '0 4px 15px rgba(0, 0, 0, 0.15)') // Softer, more modern shadow
      .style('pointer-events', 'none') // Pass through mouse events
      .style('opacity', 0) // Start hidden
      .style('transition', 'opacity 0.2s ease, transform 0.2s ease') // Smooth transitions
      .style('transform', 'translateY(5px)') // Start slightly below for animation
      .style('z-index', '1000')
      .style('max-width', '250px')
      .style('color', '#333')
      .style('font-family', 'system-ui, -apple-system, sans-serif'); // Modern font stack

    // Define arc generator
    const arc = d3
      .arc()
      .startAngle((d: any) => d.x0)
      .endAngle((d: any) => d.x1)
      .padAngle((d: any) => Math.min((d.x1 - d.x0) / 2, 0.005))
      .padRadius(radius * 1.8)
      .innerRadius((d: any) => d.y0 * radius)
      .outerRadius((d: any) => Math.max(d.y0 * radius, d.y1 * radius - 1));

    // Create SVG element
    const svg = d3
      .select(svgRef.current)
      .attr('viewBox', [-width / 2, -height / 2, width, width])
      .style('font', `${fontSize}px sans-serif`);

    // Append arcs
    const path: any = svg
      .append('g')
      .selectAll('path')
      .data(root.descendants().slice(1))
      .join('path')
      .classed('sunburst-node-arc', true)
      .attr('fill', (d: any) => {
        while (d.depth > 1) d = d.parent;
        return color(d.data.name);
      })
      .attr('fill-opacity', (d: any) => (arcVisible(d.current) ? (d.children ? 0.6 : 0.5) : 0))
      .attr('pointer-events', (d: any) => (arcVisible(d.current) ? 'auto' : 'none'))
      .attr('d', (d: any) => arc(d.current))
      .style('cursor', (d: any) => (d.children ? 'pointer' : 'default'))
      .on('click', clicked)
      .on('mouseenter', function (_, d: any) {
        d3.select(this).attr('fill-opacity', 0.8);
        d.ancestors().forEach((ancestor: any) => {
          d3.select(`[data-id="${ancestor.data.name}"]`).attr('fill-opacity', 0.8);
        });
      })
      .on('mouseleave', function (_, d: any) {
        d3.select(this).attr('fill-opacity', d.children ? 0.6 : 0.4);
        d.ancestors().forEach((ancestor: any) => {
          d3.select(`[data-id="${ancestor.data.name}"]`).attr(
            'fill-opacity',
            ancestor.children ? 0.6 : 0.4,
          );
        });
      })
      .attr('data-id', (d: any) => d.data.name)
      .on('mouseover', (event, d: any) => {
        // Get the color of the current segment for the tooltip accent
        const segmentColor = d3.select(event.currentTarget).attr('fill');

        // Prepare innovation text if available
        const innovationText =
          d.data.innovationCount > 0
            ? `<div style="margin-top: 6px; display: flex; align-items: center;">
               <span style="display: inline-block; width: 8px; height: 8px; border-radius: 50%; background-color: ${segmentColor}; margin-right: 6px;"></span>
               <span><b>${d.data.innovationCount}</b> innovations</span>
             </div>`
            : '';

        // Get depth information for context
        const depthInfo =
          d.depth > 1
            ? `<div style="font-size: smaller; opacity: 0.7; margin-top: 6px; color: #666;">
               <span style="display: inline-block; width: 8px; height: 8px; border-radius: 2px; background-color: #ddd; margin-right: 6px;"></span>
               Path: ${d.parent ? d.parent.data.name : 'Root'} > ${d.data.name}
             </div>`
            : '';

        // Create tooltip content with modern HTML structure
        const tooltipContent = `
          <div style="border-left: 3px solid ${segmentColor}; padding-left: 10px;">
            <div style="font-weight: 600; margin-bottom: 2px; font-size: ${fontSize + 1}px;">${d.data.name}</div>
            ${innovationText}
            ${depthInfo}
          </div>
        `;

        // Show tooltip with animation
        tooltip
          .html(tooltipContent)
          .style('left', `${event.pageX + 15}px`)
          .style('top', `${event.pageY}px`)
          .style('opacity', 1)
          .style('transform', 'translateY(0)');
      })
      .on('mousemove', (event) => {
        // Smooth tooltip following with slight offset for better visibility
        tooltip.style('left', `${event.pageX + 15}px`).style('top', `${event.pageY}px`);
      })
      .on('mouseout', () => {
        // Hide tooltip with animation
        tooltip.style('opacity', 0).style('transform', 'translateY(5px)');
      });

    // Append labels
    const label = svg
      .append('g')
      .attr('pointer-events', 'none')
      .attr('text-anchor', 'middle')
      .style('user-select', 'none')
      .style('font-size', `${fontSize}px`)
      .selectAll('g')
      .data(root.descendants().slice(1))
      .join('g')
      .attr('dy', '0.35em')
      .attr('fill-opacity', (d: any) => +labelVisible(d.current))
      .attr('transform', (d: any) => labelTransform(d.current))
      .each(function (d: any) {
        const g = d3.select(this);
        const { name, icon, innovationCount } = d.data;
        const arcWidth = (d.y1 - d.y0) * radius;
        const arcHeight = d.x1 - d.x0;
        const words = name.split(' ');
        let textY = 0;

        // Determine if icon should be shown:
        // 1. Node must have an icon property
        // 2. Label must be visible
        // 3. Node must not be a root node (depth > 0)
        // 4. For specific node names, show their icons
        const shouldShowIcon =
          icon &&
          labelVisible(d.current) &&
          d.depth > 0 && // Explicitly exclude root nodes
          ['Build', 'Detect', 'Treat', 'Prevent'].includes(name);

        // Icon
        if (shouldShowIcon && false) {
          g.append('image')
            .attr('xlink:href', icon)
            .attr('x', -12)
            .attr('y', -35)
            .attr('width', isMobile ? 30 : 24)
            .attr('height', isMobile ? 30 : 24);
        }

        const appendTextLine = (text: string) => {
          g.append('text').attr('x', 0).attr('y', textY).text(text);
          textY += 12;
        };

        const maxCharsPerLine = Math.floor(arcWidth / 6);
        if (arcHeight > 0.42) {
          // Multiline
          let line = '';
          words.forEach((word: string) => {
            const testLine = line ? `${line} ${word}` : word;
            if (testLine.length <= maxCharsPerLine) {
              line = testLine;
            } else {
              appendTextLine(line);
              line = word;
            }
          });
          if (line) appendTextLine(line);
        } else {
          // Single line or truncated
          let displayName = name;
          if (words.length > 1 && name.length > maxCharsPerLine) {
            displayName = name.slice(0, maxCharsPerLine - 1) + '…';
          }
          appendTextLine(displayName);
        }

        // Innovation count
        if (innovationCount > 0) {
          const countText = `(${innovationCount})`;
          if (arcHeight <= 0.08) {
            g.select('text').append('tspan').style('font-weight', 'bold').text(` ${countText}`);
          } else {
            g.append('text')
              .attr('x', 0)
              .attr('y', textY)
              .style('font-size', `${fontSize}px`)
              .style('font-weight', 'bold')
              .text(countText);
          }
        }
      });

    // Append parent circle
    const parent = svg
      .append('circle')
      .datum(root)
      .attr('r', radius)
      .attr('fill', 'none')
      .attr('pointer-events', 'all')
      .on('click', clicked);

    // Append center circle
    // const centerCircle = svg
    //   .append('circle')
    //   .attr('r', radius / (isMobile ? 1.2 : 1))
    //   .attr('fill', '#ffffff')
    //   .attr('pointer-events', 'none');

    // Create a group for center content (text and icon)
    const centerGroup = svg.append('g');

    // Create center icon container (initially hidden)
    // This will only be used for non-root nodes
    const centerIcon = centerGroup.append('g').attr('class', 'center-icon').style('opacity', 0); // Start with opacity 0 (hidden)

    // Append center text
    const centerText = centerGroup
      .append('text')
      .attr('text-anchor', 'middle')
      .attr('dy', '0.35em')
      .style('font-size', `${fontSize}px`)
      .style('font-weight', 'normal');

    centerText.append('tspan').attr('x', 0).text(root.data.name);

    if (root.data.innovationCount > 0) {
      centerText
        .append('tspan')
        .attr('x', 0)
        .attr('dy', '1.2em')
        .style('font-weight', 'bold')
        .text(`(${root.data.innovationCount})`);
    }

    /**
     * Handle click events on chart segments
     * This function manages zooming in/out of the sunburst chart
     * and handles navigation to other pages when clicking on leaf nodes
     *
     * @param {any} event - The click event
     * @param {any} p - The clicked node data
     */
    function clicked(event: any, p: any) {
      // Hide tooltip when clicking
      tooltip.style('opacity', 0);

      // Handle leaf nodes (nodes without children)
      if (!p.children) {
        if (p.data.type === 'class') {
          // Navigate to innovation class page if it's a class node
          // Always prefer slug for navigation, fall back to name if slug is not available
          const navigationSlug =
            p.data.slug ||
            p.data.name
              .toLowerCase()
              .replace(/[^a-z0-9]+/g, '-')
              .replace(/(^-|-$)/g, '');
          window.location.href = `/innovation-class/${encodeURIComponent(navigationSlug)}`;
        }
        return; // Exit early if it's a leaf node
      }

      // Update parent reference - this is for zooming back out
      parent.datum(p.parent || root);

      root.each((d: any) => {
        d.target = {
          x0: Math.max(0, Math.min(1, (d.x0 - p.x0) / (p.x1 - p.x0))) * 2 * Math.PI,
          x1: Math.max(0, Math.min(1, (d.x1 - p.x0) / (p.x1 - p.x0))) * 2 * Math.PI,
          y0: Math.max(0, d.y0 - p.depth),
          y1: Math.max(0, d.y1 - p.depth),
        };
      });

      // Create transition with appropriate duration
      // Hold Alt key for slower transition (useful for presentations)
      const t = svg.transition().duration(event.altKey ? 7500 : 750);

      // Update paths with transition
      path
        .transition(t)
        .tween('data', function (d: any) {
          // Create interpolator between current and target positions
          const i = d3.interpolate(d.current, d.target);
          // Update current position during the transition
          return (t: number) => (d.current = i(t));
        })
        .filter(function (this: SVGPathElement, d: any) {
          // Only transition elements that are visible or will become visible
          return +this.getAttribute('fill-opacity')! || arcVisible(d.target);
        })
        .attr('fill-opacity', (d: any) => (arcVisible(d.target) ? (d.children ? 0.6 : 0.5) : 0))
        .attr('pointer-events', (d: any) => (arcVisible(d.target) ? 'auto' : 'none'))
        .attrTween('d', function (d: any) {
          // Update the arc shape during transition
          return () => arc(d.current)!;
        })
        .on('end', () => updateLabels()); // Update labels when transition ends

      // Clear and update center text with the current segment name
      centerText.text('');
      wrapText(p.data.name, p.data.innovationCount, 80);

      // Handle center icon visibility based on current node
      // First, always remove the previous icon
      centerIcon.select('image').remove();

      // For root node, never show icon
      if (p === root) {
        // Ensure icon is hidden for root node
        centerIcon.transition().duration(300).style('opacity', 0);
      }
      // For non-root nodes, show icon if available
      else if (p.data.icon) {
        // If current node has an icon, update and show it
        centerIcon
          .append('image')
          .attr('xlink:href', p.data.icon)
          .attr('x', -15)
          .attr('y', -40)
          .attr('width', 30)
          .attr('height', 30);

        // Show the icon with animation
        centerIcon.transition().duration(300).style('opacity', 1);
      } else {
        // If current node has no icon, hide the icon container
        centerIcon.transition().duration(300).style('opacity', 0);
      }

      // Update labels with transition
      label
        .filter(function (this: any, d: any) {
          // Only transition labels that are visible or will become visible
          const opacityAttr = this.getAttribute('fill-opacity');
          const opacity = opacityAttr ? +opacityAttr : 0;
          return opacity > 0 || labelVisible(d.target);
        })
        .transition(t as any)
        .attr('fill-opacity', (d: any) => (labelVisible(d.target) ? 1 : 0))
        .attrTween('transform', function (d: any) {
          // Update label position during transition
          return () => labelTransform(d.current)!;
        });

      /**
       * Update labels after transition completes
       * This ensures labels are properly positioned and sized
       * based on the new layout
       */
      function updateLabels() {
        // 🧹 Remove icons from label groups

        // Update all labels based on their current positions
        label
          .attr('fill-opacity', (d: any) => +labelVisible(d.current))
          .attr('transform', (d: any) => labelTransform(d.current))
          .each(function (d: any) {
            // Get the text element
            const textElement = d3.select(this);
            // Clear existing content
            textElement.selectAll('tspan').remove();
            // Calculate dimensions for text fitting
            const arcWidth = (d.y1 - d.y0) * radius;
            const arcHeight = d.x1 - d.x0;

            // Get node name and split into words
            let name = d.data.name;
            const words = name.split(' ');

            // Handle text differently based on available space
            if (arcHeight > 0.42) {
              // Multi-line text for larger segments
              let tspan = textElement.append('tspan').attr('x', 0);
              let line = '';
              words.forEach((word: any) => {
                const testLine = line + (line ? ' ' : '') + word;
                // Check if line fits within arc width
                if (testLine.length * 6 < arcWidth) {
                  line = testLine;
                } else {
                  // Add current line and start a new one
                  tspan.text(line);
                  tspan = textElement.append('tspan').attr('x', 0).attr('dy', '1.2em').text(word);
                  line = word;
                }
              });
              tspan.text(line);
            } else {
              // Single line with truncation for smaller segments
              if (name.length > arcWidth / 5) {
                name = name.substring(0, Math.max(arcWidth / 5 - 2, 0)) + '…';
              }
              textElement.append('tspan').text(name);
            }

            // Add innovation count if available
            if (d.data.innovationCount > 0) {
              if (arcHeight <= 0.08) {
                // Inline count for very small segments
                textElement
                  .append('tspan')
                  .style('font-weight', 'bold')
                  .text(` (${d.data.innovationCount})`);
              } else {
                // Separate line for count in larger segments
                textElement
                  .append('tspan')
                  .attr('x', 0)
                  .style('font-size', `${fontSize}px`)
                  .style('font-weight', 'bold')
                  .attr('dy', '1.2em')
                  .text(`(${d.data.innovationCount})`);
              }
            }
          });
      }

      // Ensure updateLabels is called when transition ends
      t.on('end', null).on('end', updateLabels);
    }

    /**
     * Helper functions for visibility and positioning
     */

    /**
     * Determines if an arc segment should be visible
     * @param {any} d - The segment data
     * @returns {boolean} - Whether the segment should be visible
     */
    function arcVisible(d: any) {
      // Check if segment is within visible range and has non-zero angular width
      return d.y1 <= 3 && d.y0 >= 1 && d.x1 > d.x0;
    }

    /**
     * Determines if a label should be visible
     * @param {any} d - The segment data
     * @returns {boolean} - Whether the label should be visible
     */
    function labelVisible(d: any) {
      // Check if segment is within visible range and has sufficient area for a label
      return d.y1 <= 3 && d.y0 >= 1 && (d.y1 - d.y0) * (d.x1 - d.x0) > 0.03;
    }

    /**
     * Calculates the transform for positioning a label
     * @param {any} d - The segment data
     * @returns {string} - SVG transform string
     */
    function labelTransform(d: any) {
      // Calculate angle in degrees
      const x = (((d.x0 + d.x1) / 2) * 180) / Math.PI;
      // Calculate radial distance
      const y = ((d.y0 + d.y1) / 2) * radius;
      // Create transform string: rotate to segment angle, translate to radius, rotate text to be readable
      return `rotate(${x - 90}) translate(${y},0) rotate(${x < 180 ? 0 : 180})`;
    }

    /**
     * Wraps text to fit within a specified width
     * Used for center text display
     *
     * @param {string} text - The text to wrap
     * @param {number} count - Innovation count to display (if > 0)
     * @param {number} width - Maximum width for text wrapping
     */
    function wrapText(text: string, count: number, width: number) {
      // Split text into words
      const words = text.split(/\s+/);
      let line: any = [];
      const lineHeight = 1.2; // Line spacing

      // Create initial text span
      let tspan = centerText.append('tspan').attr('x', 0).attr('dy', '0em');

      // Process each word
      words.forEach((word) => {
        // Add word to current line
        line.push(word);
        tspan.text(line.join(' '));

        // Check if line exceeds width
        if (
          tspan.node()?.getComputedTextLength() !== undefined &&
          tspan.node()!.getComputedTextLength() > width
        ) {
          // Remove last word and set the line
          line.pop();
          tspan.text(line.join(' '));

          // Start a new line with the current word
          line = [word];
          tspan = centerText.append('tspan').attr('x', 0).attr('dy', `${lineHeight}em`).text(word);
        }
      });

      // Add innovation count if available
      if (count > 0) {
        centerText
          .append('tspan')
          .attr('x', 0)
          .attr('dy', `${lineHeight}em`)
          .style('font-weight', 'bold')
          .text(` (${count})`);
      }
    }

    // Return cleanup function to prevent memory leaks
    return cleanup;
  }, [data, dimensions]);

  const [_data, setData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  /**
   * Effect for demonstrating the skeleton loader
   * In a real application, this would be replaced with actual data fetching
   */
  useEffect(() => {
    // Simulate data loading with a timeout
    const loadingTimer = setTimeout(() => {
      // Generate sample data for demonstration
      setData({
        name: 'root',
        children: [
          {
            name: 'B1',
            children: Array.from({ length: 3 }, (_, i) => ({
              name: `B1-${i + 1}`,
              value: Math.random() * 10,
            })),
          },
          {
            name: 'B2',
            children: Array.from({ length: 4 }, (_, i) => ({
              name: `B2-${i + 1}`,
              value: Math.random() * 10,
            })),
          },
        ],
      });
      setIsLoading(false);
    }, 4000);

    // Clean up timer on unmount
    return () => clearTimeout(loadingTimer);
  }, []);

  /**
   * Render the sunburst chart or skeleton loader
   */
  return (
    <div
      ref={containerRef}
      className="w-full h-full flex justify-center items-center"
      aria-label="Sunburst Chart Visualization"
    >
      {data ? (
        // Render the actual chart when data is available
        <svg
          ref={svgRef}
          style={{
            width: '100%',
            height: 'auto',
            maxWidth: '900px',
            maxHeight: '900px',
          }}
          aria-label="Interactive Sunburst Chart"
          role="img"
        ></svg>
      ) : (
        // Show skeleton loader while waiting for data
        <div
          className="flex justify-center items-center w-full h-full"
          style={{ maxWidth: '400px', maxHeight: '400px' }}
          aria-label="Loading Sunburst Chart"
        >
          <SunburstSkeletonLoader data={_data} isLoading={isLoading} />
        </div>
      )}
    </div>
  );
};

export default SunburstChart;
