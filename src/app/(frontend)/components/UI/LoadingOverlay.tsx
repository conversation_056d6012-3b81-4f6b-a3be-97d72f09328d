'use client';

import { AnimatePresence, motion } from 'framer-motion';
import React from 'react';

interface LoadingOverlayProps {
  isVisible: boolean;
  message?: string;
}

const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  isVisible,
  message = 'Processing your submission...',
}) => {
  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div 
          className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-[200]"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
        >
          <motion.div 
            className="bg-white dark:bg-zinc-900 rounded-2xl p-8 max-w-md w-full shadow-xl flex flex-col items-center"
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            transition={{ 
              type: "spring",
              damping: 25,
              stiffness: 300,
              delay: 0.1
            }}
          >
            {/* Animated Spinner with gradient colors matching the app's theme */}
            <div className="relative w-20 h-20 mb-6">
              {/* Background circle */}
              <motion.div 
                className="absolute inset-0 rounded-full border-4 border-gray-200"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.2 }}
              />

              {/* Animated spinner with gradient colors */}
              <motion.div
                className="absolute inset-0 rounded-full border-4 border-transparent border-t-[#0F06D2] border-r-[#85367A] border-b-[#F86525]"
                animate={{ rotate: 360 }}
                transition={{ 
                  duration: 1.5, 
                  repeat: Infinity, 
                  ease: "linear" 
                }}
              />

              {/* Subtle pulse effect */}
              <motion.div 
                className="absolute inset-0 rounded-full bg-gradient-to-r from-[#0F06D2]/10 via-[#85367A]/10 to-[#F86525]/10"
                animate={{ scale: [1, 1.05, 1] }}
                transition={{ 
                  duration: 2, 
                  repeat: Infinity,
                  repeatType: "reverse"
                }}
              />
            </div>

            {/* Message */}
            <motion.p 
              className="text-lg text-center text-gray-700 dark:text-gray-300 font-medium"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
            >
              {message}
            </motion.p>

            {/* Animated dots with brand colors */}
            <motion.div 
              className="flex space-x-2 mt-3"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.4 }}
            >
              <motion.span
                className="w-2 h-2 bg-[#0F06D2] rounded-full"
                animate={{ y: [0, -5, 0] }}
                transition={{ 
                  duration: 0.6, 
                  repeat: Infinity,
                  repeatDelay: 0.1
                }}
              />
              <motion.span
                className="w-2 h-2 bg-[#85367A] rounded-full"
                animate={{ y: [0, -5, 0] }}
                transition={{ 
                  duration: 0.6, 
                  repeat: Infinity,
                  repeatDelay: 0.1,
                  delay: 0.15
                }}
              />
              <motion.span
                className="w-2 h-2 bg-[#F86525] rounded-full"
                animate={{ y: [0, -5, 0] }}
                transition={{ 
                  duration: 0.6, 
                  repeat: Infinity,
                  repeatDelay: 0.1,
                  delay: 0.3
                }}
              />
            </motion.div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default LoadingOverlay;
