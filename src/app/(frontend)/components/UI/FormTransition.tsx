'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { ReactNode } from 'react';

interface FormTransitionProps {
  children: ReactNode;
  isVisible: boolean;
  direction?: 'left' | 'right';
  className?: string;
}

const FormTransition = ({ 
  children, 
  isVisible, 
  direction = 'right',
  className = '' 
}: FormTransitionProps) => {
  const variants = {
    enter: {
      x: direction === 'right' ? 100 : -100,
      opacity: 0
    },
    center: {
      x: 0,
      opacity: 1
    },
    exit: {
      x: direction === 'right' ? -100 : 100,
      opacity: 0
    }
  };

  return (
    <AnimatePresence mode="wait">
      {isVisible && (
        <motion.div
          key={`form-transition-${direction}`}
          initial="enter"
          animate="center"
          exit="exit"
          variants={variants}
          transition={{
            x: { type: 'spring', stiffness: 300, damping: 30 },
            opacity: { duration: 0.3 }
          }}
          className={className}
        >
          {children}
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default FormTransition;
