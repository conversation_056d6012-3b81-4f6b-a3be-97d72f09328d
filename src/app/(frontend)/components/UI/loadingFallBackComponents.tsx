import { Suspense } from 'react';
import GradientWrapper from '../Cards/GradientWrapper';
import { NavBarUI } from './navBar';

export const FailedToLoadFallBack = () => {
  return (
    <div className="min-h-screen w-full flex items-center justify-center">
      <span className="text-black text-xl">Failed to load data</span>
    </div>
  );
};
export const NavBarUIFallBack = () => {
  return <div className="h-[60px]" />;
};
export const PageLoadingFallBack = ({ pathname }: { pathname: string }) => {
  return (
    <div>
      <GradientWrapper className="-mb-44 sm:-mb-30 h-70 bg-none" gradient={!(pathname === '/')}>
        <Suspense fallback={<div className="h-[60px]" />}>
          <NavBarUI
            color={pathname === '/' ? '' : 'text-Pure-White-FFFFFF'}
            pathname={pathname}
            isAuthenticated={false}
            isWhiteBg={pathname === '/'}
          />
        </Suspense>
      </GradientWrapper>
      <div className="min-h-screen w-full flex items-center justify-center">
        <div className="flex items-center space-x-3">
          <svg className="animate-spin h-12 w-12 text-black" viewBox="0 0 100 100" fill="none">
            <circle
              className="opacity-25"
              cx="50"
              cy="50"
              r="45"
              stroke="currentColor"
              strokeWidth="10"
            />
            <path
              className="text-gradient"
              d="M50 5
         a 45 45 0 0 1 0 90
         a 45 45 0 0 1 0 -90"
              stroke="url(#gradient)"
              strokeWidth="10"
              fill="none"
            />
            <defs>
              <linearGradient id="gradient">
                <stop offset="0%" stopColor="#0F06D2" />
                <stop offset="100%" stopColor="#F86525" />
              </linearGradient>
            </defs>
          </svg>
          <span className="text-black text-2xl animate-pulse">
            Loading<span className="animate-ping">...</span>
          </span>
        </div>
      </div>
    </div>
  );
};
