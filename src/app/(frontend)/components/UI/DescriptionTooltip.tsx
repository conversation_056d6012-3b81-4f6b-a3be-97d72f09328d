'use client';

import React, { ReactNode, useCallback, useEffect, useRef, useState } from 'react';
import { PositionType } from '../../types/data.types';
import TooltipPortal from './TooltipPortal';

interface DescriptionTooltipProps {
  description: string;
  children: ReactNode;
  maxWords?: number;
  position?: PositionType;
  slug?: string;
  shouldShowReadMore?: boolean;
  customOffset?: { x?: number; y?: number };
}

const DescriptionTooltip: React.FC<DescriptionTooltipProps> = ({
  description,
  children,
  maxWords = 180,
  position = 'cursor',
  slug,
  shouldShowReadMore,
  customOffset = { x: 0, y: 0 },
}) => {
  // if (!(description?.length >= 15)) {
  //   return null;
  // }

  const [isVisible, setIsVisible] = useState(false);
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });

  const containerRef = useRef<HTMLDivElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const clearTimeoutIfAny = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  };

  const calculateFixedPosition = (
    rect: DOMRect,
    type: PositionType,
    offset: { x?: number; y?: number },
  ) => {
    const xOff = offset.x ?? 0;
    const yOff = offset.y ?? 0;
    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;

    switch (type) {
      case 'top':
        return { x: rect.left + xOff, y: rect.top - 40 + yOff };
      case 'bottom':
        return { x: rect.left + xOff, y: rect.bottom + 10 + yOff };
      case 'left':
        return { x: rect.left - 310 + xOff, y: rect.top + yOff };
      case 'right':
        return { x: rect.right + 10 + xOff, y: rect.top + yOff };

      // Corners
      case 'top-left':
        return { x: rect.left - 310 + xOff, y: rect.top - 40 + yOff };
      case 'top-right':
        return { x: rect.right + 10 + xOff, y: rect.top - 40 + yOff };
      case 'bottom-left':
        return { x: rect.left - 310 + xOff, y: rect.bottom + 10 + yOff };
      case 'bottom-right':
        return { x: rect.right + 10 + xOff, y: rect.bottom + 10 + yOff };

      // Center relative to element
      case 'center':
        return {
          x: rect.left + rect.width / 2 + xOff,
          y: rect.top + rect.height / 2 + yOff,
        };

      // New: Edges with center alignment
      case 'top-center':
        return {
          x: rect.left + rect.width / 2 + xOff,
          y: rect.top - 40 + yOff,
        };
      case 'bottom-center':
        return {
          x: rect.left + rect.width / 2 + xOff,
          y: rect.bottom + 10 + yOff,
        };
      case 'left-center':
        return {
          x: rect.left - 390 + xOff,
          y: rect.top + rect.height / 2 + yOff,
        };
      case 'right-center':
        return {
          x: rect.right + 10 + xOff,
          y: rect.top + rect.height / 2 + yOff,
        };

      // Static positions
      case 'static-top-left':
        return { x: 10 + xOff, y: 10 + yOff };
      case 'static-top-right':
        return { x: windowWidth - 310 + xOff, y: 10 + yOff };
      case 'static-bottom-left':
        return { x: 10 + xOff, y: windowHeight - 150 + yOff };
      case 'static-bottom-right':
        return { x: windowWidth - 310 + xOff, y: windowHeight - 100 + yOff };
      case 'static-center':
        return {
          x: windowWidth / 2 - 150 + xOff,
          y: windowHeight / 2 - 50 + yOff,
        };

      default:
        return null;
    }
  };

  const calculateCursorPosition = (
    event: MouseEvent,
    type: PositionType,
    offset: { x?: number; y?: number },
  ) => {
    const xOff = offset.x ?? 0;
    const yOff = offset.y ?? 0;

    switch (type) {
      case 'cursor':
        return { x: event.clientX + 15 + xOff, y: event.clientY + 15 + yOff };
      case 'cursor-top':
        return { x: event.clientX + xOff, y: event.clientY - 40 + yOff };
      case 'cursor-bottom':
        return { x: event.clientX + xOff, y: event.clientY + 20 + yOff };
      case 'cursor-left':
        return { x: event.clientX - 220 + xOff, y: event.clientY + yOff };
      case 'cursor-right':
        return { x: event.clientX + 20 + xOff, y: event.clientY + yOff };
      case 'cursor-top-left':
        return { x: event.clientX - 220 + xOff, y: event.clientY - 40 + yOff };
      case 'cursor-top-right':
        return { x: event.clientX + 20 + xOff, y: event.clientY - 40 + yOff };
      case 'cursor-bottom-left':
        return { x: event.clientX - 220 + xOff, y: event.clientY + 20 + yOff };
      case 'cursor-bottom-right':
        return { x: event.clientX + 20 + xOff, y: event.clientY + 20 + yOff };
      case 'cursor-center':
        return { x: event.clientX - 100 + xOff, y: event.clientY - 20 + yOff };
      default:
        return { x: event.clientX + 15 + xOff, y: event.clientY + 15 + yOff };
    }
  };
  const showTooltip = useCallback(
    (event: MouseEvent) => {
      clearTimeoutIfAny();

      if (position.startsWith('cursor')) {
        const cursorPos = calculateCursorPosition(event, position, customOffset);
        if (cursorPos) setTooltipPosition(cursorPos);
      } else if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        const fixedPos = calculateFixedPosition(rect, position, customOffset);
        if (fixedPos) setTooltipPosition(fixedPos);
      }

      setIsVisible(true);
    },
    [position, customOffset],
  );

  const hideTooltip = useCallback(() => {
    timeoutRef.current = setTimeout(() => {
      setIsVisible(false);
    }, 300);
  }, []);

  const handleMouseEnter = useCallback(
    (e: React.MouseEvent) => showTooltip(e.nativeEvent),
    [showTooltip],
  );

  const handleMouseMove = useCallback(
    (e: React.MouseEvent) => {
      if (isVisible && position.startsWith('cursor')) {
        const movePos = calculateCursorPosition(e.nativeEvent, position, customOffset);
        if (movePos) setTooltipPosition(movePos);
      }
    },
    [isVisible, position, customOffset],
  );

  const handleMouseLeave = useCallback(() => hideTooltip(), [hideTooltip]);

  const handleClick = useCallback(
    (e: React.MouseEvent) => {
      clearTimeoutIfAny();
      if (!isVisible) {
        showTooltip(e.nativeEvent);
      } else {
        setIsVisible(false);
      }
    },
    [isVisible, showTooltip],
  );

  const handleTooltipMouseEnter = useCallback(() => clearTimeoutIfAny(), []);
  const handleTooltipMouseLeave = useCallback(() => hideTooltip(), [hideTooltip]);

  useEffect(() => () => clearTimeoutIfAny(), []);
  return (
    <>
      <div
        ref={containerRef}
        className="relative w-full block cursor-pointer"
        onMouseEnter={handleMouseEnter}
        onMouseMove={handleMouseMove}
        onMouseLeave={handleMouseLeave}
        onClick={handleClick}
      >
        {children}
      </div>

      {description?.length >= 15 && (
        <TooltipPortal
          isVisible={isVisible}
          description={description}
          position={tooltipPosition}
          maxWords={maxWords}
          onMouseEnter={handleTooltipMouseEnter}
          onMouseLeave={handleTooltipMouseLeave}
          ref={tooltipRef}
          slug={slug}
          shouldShowReadMore={shouldShowReadMore}
        />
      )}
    </>
  );
};

export default DescriptionTooltip;
