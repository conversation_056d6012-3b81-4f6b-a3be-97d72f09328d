'use client';

import { AnimatePresence, motion } from 'framer-motion';
import dynamic from 'next/dynamic';
import Link from 'next/link';
import React, { useEffect, useMemo, useState } from 'react';
import { createPortal } from 'react-dom';
import { getDocument } from '../../utils/clientUtils';
const ReactQuill = dynamic(() => import('react-quill-new'), {
  ssr: false,
});

interface TooltipPortalProps {
  isVisible: boolean;
  description: string;
  position: { x: number; y: number };
  maxWords?: number;
  onMouseEnter?: () => void;
  onMouseLeave?: () => void;
  slug?: string;
  shouldShowReadMore?: boolean;
}

const countWords = (text: string): number => {
  const stripped = text.replace(/<[^>]+>/g, '');
  return stripped?.split(/\s+/).filter(Boolean).length;
};

const truncateHtml = (html: string, maxWords: number): string => {
  const document = getDocument();
  if (!document) return '';
  if (typeof window === 'undefined') return ''; // SSR-safe
  const parser = new DOMParser();
  const doc = parser?.parseFromString(html, 'text/html');
  let wordCount = 0;

  const processNode = (node: Node): Node | null => {
    if (wordCount >= maxWords) return null;

    if (node.nodeType === Node.TEXT_NODE) {
      const words = node.textContent?.split(/\s+/).filter(Boolean) ?? [];
      const remaining = maxWords - wordCount;

      if (words.length <= remaining) {
        wordCount += words.length;
        return node.cloneNode(true);
      } else {
        const newNode = document?.createTextNode(words.slice(0, remaining).join(' ') + '...');
        wordCount = maxWords;
        return newNode;
      }
    } else if (node.nodeType === Node.ELEMENT_NODE) {
      const clone = node.cloneNode(false) as Element;
      for (const child of Array.from(node.childNodes)) {
        if (wordCount >= maxWords) break;
        const processedChild = processNode(child);
        if (processedChild) clone.appendChild(processedChild);
      }
      return clone;
    }

    return null;
  };

  const body = doc?.body;
  const newBody = document?.createElement('div');
  for (const child of Array.from(body.childNodes)) {
    if (wordCount >= maxWords) break;
    const processed = processNode(child);
    if (processed) newBody.appendChild(processed);
  }

  return newBody.innerHTML;
};

const TooltipPortal = React.forwardRef<HTMLDivElement, TooltipPortalProps>(
  (
    {
      isVisible,
      description,
      position,
      maxWords = 120,
      onMouseEnter,
      onMouseLeave,
      slug,
      shouldShowReadMore,
    },
    ref,
  ) => {
    const [mounted, setMounted] = useState(false);

    useEffect(() => {
      setMounted(true);
      return () => setMounted(false);
    }, []);

    const wordCount = countWords(description);
    const trimmedDescription = useMemo(() => {
      if (typeof window !== 'undefined') {
        return truncateHtml(description, maxWords);
      }
      return '';
    }, [description, maxWords]);
    const showReadMoreLink = shouldShowReadMore && slug && wordCount > maxWords;

    if (!mounted || !isVisible) return null;
    const doc = getDocument();
    if (!doc) return null;

    return (
      mounted &&
      createPortal(
        <AnimatePresence>
          {isVisible && description && (
            <Link
              href={`/taxonomy/${encodeURIComponent(slug ?? '')}`}
              className="block m-2 -mt-5 text-xs font-medium text-blue-600 hover:text-blue-800 cursor-pointer z-10"
              rel="noopener noreferrer"
            >
              <motion.div
                ref={ref}
                onClick={(e) => e.stopPropagation()}
                className="fixed z-[9999]  max-w-[50vw] max-h-[85vh] lg:max-w-[17vw] lg:max-h-[40vh]  overflow-y-auto bg-white border border-gray-200 rounded-lg shadow-lg scrollbar-thin scrollbar-thumb-gray-400 scrollbar-track-gray-100 hover:scrollbar-thumb-gray-500 transition-all p-3"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                transition={{ duration: 0.2 }}
                style={{
                  pointerEvents: 'auto',
                  left: position.x,
                  top: position.y,
                }}
                onMouseEnter={onMouseEnter}
                onMouseLeave={onMouseLeave}
              >
                <ReactQuill
                  value={trimmedDescription}
                  readOnly={true}
                  className="p-0 m-0"
                  theme="bubble"
                  style={{
                    padding: 0,
                    margin: 0,
                    border: 'none',
                  }}
                />
                {showReadMoreLink && (
                  <Link
                    href={`/taxonomy/${encodeURIComponent(slug)}`}
                    className="block -mt-3 lg:-mt-5 text-xs font-medium text-blue-600 hover:text-blue-800  z-10 "
                    rel="noopener noreferrer"
                  >
                    Read more details
                  </Link>
                )}
              </motion.div>
            </Link>
          )}
        </AnimatePresence>,
        doc?.body,
      )
    );
  },
);

TooltipPortal.displayName = 'TooltipPortal';
export default TooltipPortal;
