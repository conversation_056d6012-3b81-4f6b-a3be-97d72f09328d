'use client';

import GlassCard from '@/app/(frontend)/components/Cards/glassCard';
import CircularLoader from '@/app/(frontend)/components/UI/CircularLoader';
import { submitInnovation } from '@/app/(frontend)/utils/apiClient';
import { redirect, useParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import InnovationFormOriginal from '../Forms/InnovationFormOrignal';
import InnovationPreview from '../Forms/InnovationPreview';

export default function EditInnovationForm() {
  const params = useParams();
  const slug = params?.slug as string;
  const [formData, setFormData] = useState<any>(null);
  const [tempData, setTempData] = useState(null);
  const [showPreview, setShowPreview] = useState<boolean>(false);
  const [innovationData, setInnovationData] = useState<FormData | null>(null);
  const handleSuccess = (data: any) => {
    setInnovationData(data);
    setShowPreview(true);
  };
  const onPreviewSubmit = () => {
    redirect(`/browse-innovations?myInnovations=${encodeURIComponent('true')}`);
  };
  const handleBack = (data: any) => {
    data.currentStageOfDevelopment = data?.currentStageOfDevelopment?.id;
    setInnovationData(data);
    setShowPreview(false);
  };

  useEffect(() => {
    if (slug) {
      fetch(`/api/innovations/innovation/${slug}`)
        .then((res) => res.json())
        .then((data) => {
          setFormData({
            ...data,
            currentStageOfDevelopment:
              typeof data.currentStageOfDevelopment === 'object'
                ? data.currentStageOfDevelopment?.id
                : data.currentStageOfDevelopment,
          }),
            setTempData(data);
        })
        .catch((err) => console.error('Failed to fetch data:', err));
    }
  }, [slug]);
  return (
    <div>
      {!showPreview ? (
        <>
          <div>
            <GlassCard
              title={(slug ? 'Edit' : 'Submit') + ' your Innovation'}
              description={formData?.title}
              isAuthenticated={Boolean(localStorage.getItem('atlasInfo'))}
              pathname="/how-to-submit/submit-an-innovation"
            />
            <div className="max-w-[95vw] sm:max-w-[80vw] mx-auto">
              {!formData ? (
                <CircularLoader />
              ) : (
                <InnovationFormOriginal
                  onSuccess={handleSuccess}
                  submitInnovation={submitInnovation}
                  defaultValues={formData || innovationData}
                />
              )}
            </div>
          </div>
        </>
      ) : (
        <InnovationPreview
          onBack={handleBack}
          currentInnovationData={tempData}
          onSubmit={onPreviewSubmit}
        />
      )}
    </div>
  );
}
