'use client';
import Image from 'next/image';
import { useEffect, useRef, useState } from 'react';
import { ImageCarouselProps } from '../../types/data.types';

const ImageCarousel: React.FC<ImageCarouselProps> = ({
  backgroundImages = [],
  interval = 3000,
}) => {
  const [currentIndex, setCurrentIndex] = useState<number>(0);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    timeoutRef.current = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % backgroundImages?.length);
    }, interval);

    return () => {
      if (timeoutRef.current) clearInterval(timeoutRef.current);
    };
  }, [backgroundImages.length, interval]);

  return (
    <div className=" -z-50">
      {backgroundImages?.map((src, index) => {
        return (
          <Image
            key={index}
            src={(process.env._AWS_END_POINT || '') + src}
            alt="Background Image"
            layout="fill"
            objectFit="cover"
            className={`transition-opacity duration-1000 ease-in-out ${
              index === currentIndex ? 'opacity-100' : 'opacity-0'
            }`}
            priority={index === 0}
          />
        );
      })}
    </div>
  );
};

export default ImageCarousel;
