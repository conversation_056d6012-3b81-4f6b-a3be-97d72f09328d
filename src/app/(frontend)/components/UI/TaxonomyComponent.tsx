'use client';
import dynamic from 'next/dynamic';
const ReactQuill = dynamic(() => import('react-quill-new'), {
  ssr: false,
});
interface TaxonomyComponentProps {
  descripition: string;
}

export default function TaxonomyComponent({ descripition }: TaxonomyComponentProps) {
  return (
    <div className="flex-1 py-12">
      <div className="max-w-[95vw] sm:max-w-[80vw] rounded-lg mx-auto p-8">
        <p className="text-gray-600 text-base sm:text-lg">
          <ReactQuill
            value={descripition}
            readOnly={true}
            className=" p-0 m-0"
            theme="bubble"
            style={{
              padding: 0,
              margin: 0,
              border: 'none',
            }}
          />
        </p>
      </div>
    </div>
  );
}
