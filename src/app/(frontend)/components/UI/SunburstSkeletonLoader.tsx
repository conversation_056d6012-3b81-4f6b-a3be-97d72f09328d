import React, { useEffect, useState } from 'react';
import * as d3 from 'd3';

const SunburstSkeletonLoader = ({ data, isLoading }: { data: any; isLoading: boolean }) => {
  const customColors = ['#FFE699', '#FFB3A7', '#B2F7B7', '#A7C7FF'];
  const width = 600,
    height = 600,
    radius = Math.min(width, height) / 2;
  const [arcs, setArcs] = useState<any[]>([]);
  const [rotation, setRotation] = useState(0);

  useEffect(() => {
    if (isLoading) {
      const interval = setInterval(() => {
        setRotation((prev) => prev + 5);
      }, 50);
      return () => clearInterval(interval);
    }
  }, [isLoading]);

  useEffect(() => {
    const partition = (data: any) => {
      const root = d3
        .hierarchy(data)
        .sum((d: any) => d.value || 1)
        .sort((a: any, b: any) => (b.value || 0) - (a.value || 0));

      return d3.partition().size([2 * Math.PI, radius])(root);
    };

    const newArcs = partition(data || skeletonData)
      .descendants()
      .slice(1);
    setArcs(newArcs);
  }, [data]);

  const arcGenerator = d3.arc();

  return (
    <div className="relative w-full h-full flex justify-center items-center">
      <div className="w-full h-full max-w-[300px] max-h-[300px] md:max-h-[400px] md:max-w-[400px] lg:max-h-[600px] lg:max-w-[600px] aspect-square">
        <svg
          viewBox={`-${width / 2} -${height / 2} ${width} ${height}`}
          className="w-full h-full"
          xmlns="http://www.w3.org/2000/svg"
          style={{
            transform: isLoading ? `rotate(${rotation}deg)` : 'rotate(0deg)',
            transition: 'transform 0.2s linear',
          }}
        >
          <circle cx={0} cy={0} r={radius / 6} className="fill-gray-200 animate-pulse" />
          {arcs.map((arc: any, i: number) => {
            const color = customColors[arc.depth % customColors.length];
            return (
              <path
                key={i}
                d={
                  arcGenerator({
                    startAngle: arc.x0,
                    endAngle: arc.x1,
                    innerRadius: arc.y0,
                    outerRadius: arc.y1,
                  }) || ''
                }
                style={{
                  fill: color,
                  transition: 'd 0.6s ease-in-out',
                }}
              />
            );
          })}
        </svg>
      </div>
    </div>
  );
};

// Example skeleton structure for loading state
const skeletonData = {
  name: 'root',
  children: [
    {
      name: 'A1',
      children: Array.from({ length: 5 }, (_, i) => ({ name: `A1-${i + 1}`, value: 1 })),
    },
    {
      name: 'A2',
      children: Array.from({ length: 6 }, (_, i) => ({ name: `A2-${i + 1}`, value: 2 })),
    },
    {
      name: 'A3',
      children: Array.from({ length: 4 }, (_, i) => ({ name: `A3-${i + 1}`, value: 1 })),
    },
    {
      name: 'A4',
      children: Array.from({ length: 2 }, (_, i) => ({ name: `A4-${i + 1}`, value: 2 })),
    },
    {
      name: 'A5',
      children: Array.from({ length: 5 }, (_, i) => ({ name: `A5-${i + 1}`, value: 1 })),
    },
  ],
};

export default SunburstSkeletonLoader;
