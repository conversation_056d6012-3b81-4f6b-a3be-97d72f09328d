'use client';

import Image from 'next/image';
import Link from 'next/link';
import { redirect, useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect, useRef, useState } from 'react';
import { AtlasInfo } from '../../types/data.types';
import { getDocument, getWindow } from '../../utils/clientUtils';
import Button from '../UI/Button';

interface MenuItemsType {
  href: string;
  label: string;
}

type AuthButtonProps = {
  isAuthenticated: boolean;
  color?: string;
  menuItems: MenuItemsType[];
};

const AuthButton: React.FC<AuthButtonProps> = ({ isAuthenticated, color, menuItems }) => {
  const [atlasInfo, setAtlasInfo] = useState<AtlasInfo>({});
  const [isOpen, setIsOpen] = useState(false);
  const searchParams = useSearchParams();
  const router = useRouter();

  const mobileMenuRef = useRef<HTMLDivElement>(null);
  const toggleButtonRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const doc = getDocument();
    if (!doc) return;
    const handleClickOutside = (event: MouseEvent) => {
      const isOutsideMenu =
        mobileMenuRef.current && !mobileMenuRef.current.contains(event.target as Node);
      const isOutsideToggle =
        toggleButtonRef.current && !toggleButtonRef.current.contains(event.target as Node);

      if (isOutsideMenu && isOutsideToggle) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      doc?.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      doc?.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  const openModal = () => {
    const win = getWindow();
    if (!win) return;

    const params = new URLSearchParams(searchParams.toString());
    params.set('authModal', 'true');
    router.replace(`${win.location.pathname}?${params.toString()}`, { scroll: false });
  };

  const openProfileModal = async () => {
    const win = getWindow();
    if (!win) return;
    const res = await fetch(`/api/appUsers/me`);
    if (!res.ok) {
      win.localStorage.removeItem('atlasInfo');
      redirect('/admin/logout');
    }

    const params = new URLSearchParams(searchParams.toString());
    params.set('profile-modal', 'true');
    router.replace(`${win.location.pathname}?${params.toString()}`, { scroll: false });
  };

  const closeModal = () => {
    const win = getWindow();
    if (!win) return;

    const params = new URLSearchParams(searchParams.toString());
    params.delete('authModal');
    router.replace(`${win.location.pathname}?${params.toString()}`, { scroll: false });
  };

  const onToggleMobileMenu = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsOpen((prev) => !prev);
  };

  const handleClick = () => {
    isAuthenticated ? openProfileModal() : openModal();
  };

  useEffect(() => {
    const win = getWindow();
    if (!win) return;

    try {
      const storedInfo = win.localStorage.getItem('atlasInfo');
      if (storedInfo && isAuthenticated) {
        setAtlasInfo(JSON.parse(storedInfo));
      } else if (storedInfo && !isAuthenticated) {
        win.localStorage.removeItem('atlasInfo');
        closeModal();
        win.location.reload();
        console.log('Cleanup ------->', storedInfo);
      }
    } catch (error) {
      console.error('Failed to parse atlasInfo from localStorage', error);
    }
  }, []);

  const formatName = (name: string) =>
    name?.charAt(0)?.toUpperCase() + (name.length > 7 ? name.slice(1, 7) + '..' : name.slice(1));

  const displayName = formatName((atlasInfo?.name as string) || '');

  return (
    <div className="flex items-center justify-between w-full">
      {/* Desktop Auth Button */}
      {isAuthenticated && (
        <div className="px-4 hidden lg:flex">
          <Button
            variant={color ? 'transparent' : 'solid'}
            title="My Innovations"
            size="md"
            solidColor="#F9FAFB"
            onClick={() => router.push('/my-innovations')}
            className={`relative ${color || 'text-Midnight-Royal-Blue-02018B'} whitespace-nowrap`}
          />
        </div>
      )}

      <div className="hidden lg:flex">
        <Button
          variant={color ? 'transparent' : 'solid'}
          title={isAuthenticated ? `Hii ${displayName}` : 'Login'}
          size="md"
          solidColor="#F9FAFB"
          rightIcon={
            <Image
              alt="Login Icon"
              className={`h-5 w-5 ml-2 ${isAuthenticated && color ? 'bg-white rounded-full border-1' : ''}`}
              height={20}
              width={20}
              src={
                isAuthenticated
                  ? '/images/icons/profile.svg'
                  : color
                    ? '/images/icons/lightLoginIcon.svg'
                    : '/images/icons/loginIcon.svg'
              }
            />
          }
          onClick={handleClick}
          className={`relative ${color || 'text-Midnight-Royal-Blue-02018B'} whitespace-nowrap`}
        />
      </div>

      {/* Mobile Menu */}
      {isOpen && (
        <div
          ref={mobileMenuRef}
          className="absolute top-16 right-4 bg-gray-100 text-black shadow-md rounded-md p-4 z-[60] w-48 transition-transform duration-300 ease-in-out"
        >
          <ul className="flex flex-col space-y-2">
            {menuItems.map(({ href, label }) => (
              <li className="rounded-md bg-white" key={href}>
                <Link
                  href={href}
                  onClick={() => {
                    setIsOpen(false);
                    closeModal();
                  }}
                  className="block px-4 py-2 rounded-md hover:bg-gray-200 transition"
                >
                  {label}
                </Link>
              </li>
            ))}
            <li className="rounded-md bg-white">
              <button
                onClick={handleClick}
                className="w-full text-left px-4 py-2 rounded-md hover:bg-gray-200 transition"
              >
                {isAuthenticated ? 'My Profile' : 'Login'}
              </button>
            </li>
          </ul>
        </div>
      )}

      {/* Mobile Toggle Button */}
      <div
        ref={toggleButtonRef}
        className="flex lg:hidden items-center bg-gray-50 p-3 rounded-4xl cursor-pointer"
        onClick={onToggleMobileMenu}
      >
        <Image
          alt="Menu Icon"
          className="h-5 w-5"
          height={20}
          width={20}
          src="/images/icons/menu-burger.svg"
        />
      </div>
    </div>
  );
};

export default AuthButton;
