'use client';

import { motion } from 'framer-motion';
import { ReactNode } from 'react';

interface ScaleInProps {
  children: ReactNode;
  delay?: number;
  duration?: number;
  className?: string;
  once?: boolean;
  amount?: number;
}

export const ScaleIn = ({ 
  children, 
  delay = 0, 
  duration = 0.5, 
  className = '',
  once = true,
  amount = 0.3
}: ScaleInProps) => {
  return (
    <motion.div
      initial={{ 
        opacity: 0,
        scale: 0.95
      }}
      whileInView={{ 
        opacity: 1,
        scale: 1
      }}
      viewport={{ once, amount }}
      transition={{ 
        delay,
        duration,
        ease: "easeOut"
      }}
      className={className}
    >
      {children}
    </motion.div>
  );
};
