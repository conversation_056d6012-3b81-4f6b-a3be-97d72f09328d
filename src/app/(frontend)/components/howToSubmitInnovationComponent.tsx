'use client';
import Image from 'next/image';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { Suspense, useState } from 'react';
import { ApplicationProcessCardProps, SubmitContentType } from '../types/data.types';
import { AuthModal } from './modals/authModal';
import ApplicationProcessSection from './UI/ApplicationProcessSection';
import Button from './UI/Button';
import Toast from './UI/Toast';

interface Props {
  content: SubmitContentType;
  isAuthenticated: Boolean;
  sections: { docs: ApplicationProcessCardProps[] };
}
const HowToSubmitInnovationComponent: React.FC<Props> = ({
  content,
  sections,
  isAuthenticated,
}) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const showSignup = searchParams.get('showSignup') === 'true';

  const [toast, setToast] = useState<{
    message: string;
    type: 'success' | 'error' | 'info';
  } | null>(null);

  return (
    <div>
      {toast && <Toast message={toast.message} type={toast.type} onClose={() => setToast(null)} />}
      <div className="max-w-[95vw] sm:max-w-[80vw] mx-auto py-4 flex-1">
        <div className="py-4">
          <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-6 text-center sm:text-left">
            {content?.cardTitle}
          </h1>
          <div className="mb-4">
            <p className="text-base sm:text-lg md:text-xl leading-relaxed text-center sm:text-left">
              {content?.cardDescription}
            </p>
          </div>

          {/* <Link href={"/how-to-submit/submit-an-innovation"} onClick={}> */}
          <Button
            title="Submit an Innovation"
            onClick={() => {
              if (!isAuthenticated) {
                router.push('?showSignup=true');
              } else {
                router.push('/how-to-submit/submit-an-innovation');
              }
            }}
            className="ms-2"
            variant="gradient"
            rightIcon={
              <Image
                alt="arrowIcon of Innovations"
                className="w-7 h-7 bg-white p-2 rounded-full "
                height={20}
                width={20}
                src={'/images/icons/darkArrowIcon.svg'}
              />
            }
          />
          {/* </Link> */}
        </div>
        <ApplicationProcessSection sections={sections.docs} />
      </div>
      {showSignup && (
        <Suspense fallback={<div />}>
          <AuthModal
            isOpen={true}
            onClose={() => router.push('/how-to-submit')}
            setToast={setToast}
          />
        </Suspense>
      )}
    </div>
  );
};

export default HowToSubmitInnovationComponent;
