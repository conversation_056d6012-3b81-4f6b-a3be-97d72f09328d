import React from 'react';

const FilterSkeleton = () => {
  return (
    <div className="animate-pulse space-y-8">
      {/* Checkbox */}
      <div className="flex items-center space-x-3">
        <div className="w-5 h-7 bg-gray-300 rounded"></div>
        <div className="w-32 h-7 bg-gray-300 rounded"></div>
      </div>

      {/* Filter Buttons */}
      <div className="flex flex-wrap gap-3">
        {[1, 2, 3, 4, 5].map((_, index) => (
          <div key={index} className="w-28 h-8 bg-gray-300 rounded-lg"></div>
        ))}
      </div>
    </div>
  );
};

export default FilterSkeleton;
