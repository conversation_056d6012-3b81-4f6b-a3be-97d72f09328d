import React from 'react';

const InnovationCardSkeleton = () => {
  return (
    <div className="bg-white rounded-2xl py-5 px-4 h-[400px] flex flex-col animate-pulse">
      {/* Header: Organization and Logo */}
      <div className="flex gap-5 justify-between items-start mb-6">
        <div className="w-28 h-6 bg-gray-300 rounded-full"></div>
        <div className="w-12 h-12 bg-gray-300 rounded-full"></div>
      </div>

      {/* Content: Title and Description */}
      <div className="mb-6 flex-grow">
        <h4 className=" bg-gray-300 mb-10 line-clamp-2 w-full h-[40px]"></h4>
        <div className="h-4 bg-gray-300 rounded w-full mb-2"></div>
        <div className="h-4 bg-gray-300 rounded w-5/6 mb-2"></div>
        <div className="h-4 bg-gray-300 rounded w-2/3"></div>
      </div>

      {/* Status and Share */}
      <div className="flex justify-between mb-6">
        <div className="w-24 h-6 bg-gray-300 rounded"></div>
        <div className="w-6 h-6 bg-gray-300 rounded"></div>
      </div>

      {/* CTA Button */}
      <div className="w-full h-12 bg-gray-300 rounded-4xl"></div>
    </div>
  );
};

export default InnovationCardSkeleton;
