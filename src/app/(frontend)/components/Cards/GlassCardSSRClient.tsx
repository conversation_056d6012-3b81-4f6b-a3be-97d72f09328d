'use client';

import { motion } from 'framer-motion';
import React from 'react';
import { GlassCardProps } from '../../types/data.types';
import GlassCardSSR from './GlassCardSSR';

interface GlassCardSSRClientProps extends GlassCardProps {
  isPreview?: boolean;
}

const GlassCardSSRClient: React.FC<GlassCardSSRClientProps> = (props) => {
  // This is a client-side wrapper that adds animations to the server-side GlassCardSSR component
  // We'll use this for the card content animations while keeping the SSR benefits
  
  return (
    <div>
      <GlassCardSSR {...props} />
      
      {/* Overlay animations that will be applied client-side */}
      <div className="relative pointer-events-none">
        <motion.div
          className="absolute top-0 left-0 right-0 mx-auto max-w-[95vw] sm:max-w-[80vw] -mt-[calc(100%-80px)] sm:-mt-[calc(100%-90px)] opacity-0"
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 0 }} // Opacity stays at 0 as this is just for animation
          transition={{ 
            duration: 0.5,
            type: "spring",
            stiffness: 100 
          }}
        >
          <motion.div 
            className="text-2xl sm:text-3xl md:text-4xl font-bold mb-4 opacity-0"
            initial={{ y: 10, opacity: 0 }}
            animate={{ y: 0, opacity: 0 }}
            transition={{ delay: 0.2, duration: 0.4 }}
          />

          <motion.div 
            className="text-base sm:text-lg mb-6 opacity-0"
            initial={{ y: 10, opacity: 0 }}
            animate={{ y: 0, opacity: 0 }}
            transition={{ delay: 0.3, duration: 0.4 }}
          />
        </motion.div>
      </div>
    </div>
  );
};

export default GlassCardSSRClient;
