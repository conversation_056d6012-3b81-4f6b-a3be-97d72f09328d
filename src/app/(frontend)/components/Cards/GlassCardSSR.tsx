import Image from 'next/image';
import React from 'react';
import { GlassCardProps } from '../../types/data.types';
import GradientWrapper from './GradientWrapper';
import { NavBarClientWrapper } from './NavBarClientWrapper';

interface GlassCardSSRProps extends GlassCardProps {
  isPreview?: boolean;
}

const GlassCardSSR: React.FC<GlassCardSSRProps> = ({
  title,
  description,
  pathname,
  isInnerHTML,
  isPreview = false,
  bannerImage,
}) => {
  return (
    <div className="relative">
      {/* Navbar Wrapper with Gradient */}
      <GradientWrapper className="-mb-24 sm:-mb-20 min-h-[270px] relative z-10">
        {!isPreview && <NavBarClientWrapper pathname={pathname} />}
      </GradientWrapper>

      {/* Glassmorphic Card */}
      <div
        className="p-4 shadow-xl rounded-3xl max-w-[95vw] sm:max-w-[80vw] mx-auto border border-white/80 text-white
          backdrop-blur-lg bg-gradient-to-r from-[#0F06D2]/50 via-[#85367A]/50 to-[#F86525]/50 bg-opacity-30 glass-shimmer"
      >
        <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
          <div className="flex flex-col md:w-2/3">
            <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-4">{title}</h1>

            {isInnerHTML ? (
              <div
                className={`inline-block ${pathname === '/innovation' ? 'bg-orange-500 text-white' : 'text-white'} px-4 py-2 rounded-full text-sm font-medium`}
                dangerouslySetInnerHTML={{
                  __html: description,
                }}
              />
            ) : (
              <div
                className={`inline-block ${pathname === '/innovation' ? 'bg-orange-500 text-white' : 'text-white'} px-4 py-2 rounded-full text-sm font-medium`}
              >
                {description}
              </div>
            )}
          </div>

          {bannerImage && (
            <div className="relative h-40 w-40 md:h-52 md:w-52 lg:h-64 lg:w-64 rounded-lg overflow-hidden flex-shrink-0">
              <Image
                src={(process.env._AWS_END_POINT || '') + '/' + bannerImage.name}
                alt="Innovation banner"
                fill
                className="object-cover"
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default GlassCardSSR;
