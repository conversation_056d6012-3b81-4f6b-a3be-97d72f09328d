'use client';

import Image from 'next/image';
import Link from 'next/link';
import { FC } from 'react';
import { MapOfInnovationsProps } from '../../types/data.types';
import Button from '../UI/Button';

const MapOfInnovations: FC<MapOfInnovationsProps> = ({
  title,
  description,
  buttonText,
  ButtonHref,
  onButtonClick,
}) => {
  return (
    <div className="flex-col items-center lg:justify-start font-Poppins">
      <h2 className="text-5xl text-Midnight-Royal-Blue-02018B text-center lg:text-left">{title}</h2>
      <div className="w-full lg:gap-4 my-5 ">
        <p className="w-full text-Grayish-Black-7E7E7F text-lg font-medium">{description}</p>
      </div>
      <Link href={ButtonHref} className="pointer-events-none">
        <Button
          title={buttonText || 'add Title'}
          onClick={onButtonClick}
          variant="gradient"
          className="pointer-events-auto"
          rightIcon={
            <Image
              alt="arrowIcon of Innovations"
              className="w-7 h-7 bg-white p-2 rounded-full "
              height={20}
              width={20}
              src={'/images/icons/darkArrowIcon.svg'}
            />
          }
        />
      </Link>
    </div>
  );
};

export default MapOfInnovations;
