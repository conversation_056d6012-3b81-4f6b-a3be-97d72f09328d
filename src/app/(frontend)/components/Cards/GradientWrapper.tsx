import React from 'react';
import { GradientWrapperProps } from '../../types/data.types';

const GradientWrapper: React.FC<GradientWrapperProps> = ({
  children,
  className = '',
  gradient = true,
}) => {
  return (
    <div
      className={
        (gradient && ' w-full bg-gradient-to-r from-[#0F06D2] via-[#85367A] to-[#F86525]') || ''
      }
    >
      <div className={`mx-auto w-[80vw] ${className}`}>{children}</div>
    </div>
  );
};

export default GradientWrapper;
