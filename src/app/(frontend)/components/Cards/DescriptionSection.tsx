'use client';
import Image from 'next/image';
import React from 'react';

interface DescriptionSectionProps {
  image: string;
  className?: string;
  title: string;
  children: React.ReactNode;
}

const DescriptionSection: React.FC<DescriptionSectionProps> = ({
  image,
  className = '',
  title = '',
  children,
}) => {
  return (
    <div className={(className && className) || 'mt-8'}>
      <div className="flex items-center justify-between pb-2 mb-6 relative after:content-[''] after:absolute after:bottom-0 after:left-0 md:after:w-1/2 after:w-[90%] after:h-[3px] after:bg-orange-500">
        <h3 className="text-2xl font-bold text-blue-900">{title}</h3>
        <Image
          src={image}
          width={35}
          height={35}
          alt="Categories"
          className="absolute bottom-[-15px] md:left-[calc(50%)] left-[calc(90%)]"
        />
      </div>
      {children}
    </div>
  );
};

export default DescriptionSection;
