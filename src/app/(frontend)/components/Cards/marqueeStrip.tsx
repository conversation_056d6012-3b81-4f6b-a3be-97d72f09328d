'use client';

import Image from 'next/image';
import { FC } from 'react';
import { MarqueeStripProps } from '../../types/data.types';

const MarqueeStrip: FC<MarqueeStripProps> = ({ stripContent, itsBlue = false }) => {
  return (
    <div className="w-full absolute left-0 overflow-hidden whitespace-nowrap py-20">
      <div className="flex space-x-8 animate-marquee ">
        <div
          className={`flex space-x-8 min-w-max border-2 p-8 bg-white ${itsBlue ? 'border-Deep-Royal-Blue-07038D' : 'border-gray-300'}`}
        >
          {stripContent.map((v, index) => (
            <div key={index} className="flex items-center space-x-4 ">
              <Image
                alt="stripArrow Innovations"
                className="w-15 h-15"
                height={20}
                width={20}
                src={`/images/icons/${itsBlue ? 'blueStripArrow' : 'stripArrow'}.svg`}
              />
              <span
                className={`text-8xl font-extralight ${itsBlue ? 'text-Deep-Royal-Blue-07038D' : 'text-Gray-888888'} font-Poppins leading-2 ms-5`}
              >
                /
              </span>
              <span
                className={`text-6xl font-extralight ${itsBlue ? 'text-Deep-Royal-Blue-07038D' : 'text-Gray-888888'} font-Poppins leading-2`}
              >
                {!(index >= 9) && '0'}
                {index + 1} {v.text}
              </span>
            </div>
          ))}
        </div>
        v
      </div>
    </div>
  );
};

export default MarqueeStrip;
