'use client';
import { motion } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import React, { Suspense } from 'react';
import { en } from '../../translations';
import { GlassCardProps } from '../../types/data.types';
import Button from '../UI/Button';
import { NavBarUI } from '../UI/navBar';
import GradientWrapper from './GradientWrapper';

const GlassCard: React.FC<GlassCardProps> = ({
  title,
  description,
  isInnerHTML,
  isAuthenticated,
  bannerImage,
}) => {
  const currentPathname = usePathname();
  const isPreview = currentPathname.startsWith('/preview');
  const isInnovation = currentPathname.startsWith('/innovation/');
  return (
    <div className="relative">
      {/* Navbar Wrapper with Gradient */}
      <GradientWrapper className="-mb-24 sm:-mb-20 min-h-[200px] relative z-10">
        {!isPreview && (
          <Suspense fallback={<div className="h-[60px]" />}>
            <NavBarUI
              color="text-Pure-White-FFFFFF"
              pathname={currentPathname}
              isAuthenticated={isAuthenticated}
            />
          </Suspense>
        )}
      </GradientWrapper>

      {/* Glassmorphic Card with Animation */}
      <motion.div
        className="flex flex-col p-4 shadow-xl rounded-3xl max-w-[95vw] sm:max-w-[80vw] mx-auto border border-white/80 text-white
          backdrop-blur-lg bg-gradient-to-r from-[#0F06D2]/50 via-[#85367A]/50 to-[#F86525]/50 bg-opacity-30 glass-shimmer"
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{
          duration: 0.5,
          type: 'spring',
          stiffness: 100,
        }}
      >
        <div
          className={`flex ${isInnovation ? 'flex-col md:flex-row' : ' md:flex-row'} gap-4 items-center justify-between`}
        >
          <div className="flex flex-col md:w-2/3">
            {title && (
              <motion.h1
                className="px-4 text-2xl sm:text-3xl md:text-4xl font-bold mb-4"
                initial={{ y: 10, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.2, duration: 0.4 }}
              >
                {title}
              </motion.h1>
            )}

            <div className="w-fit self-start">
              {isInnerHTML ? (
                <motion.div
                  className={`inline-block ${
                    currentPathname.startsWith('/innovation/')
                      ? 'bg-orange-500 text-white'
                      : 'text-white'
                  } pr-4 py-2 rounded-full text-sm font-medium`}
                  initial={{ y: 10, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.3, duration: 0.4 }}
                  dangerouslySetInnerHTML={{ __html: description }}
                />
              ) : (
                <motion.div
                  className={`inline-block ${
                    currentPathname.startsWith('/innovation/')
                      ? 'bg-orange-500 text-white'
                      : 'text-white'
                  } px-4 py-2 rounded-full text-sm font-medium`}
                  initial={{ y: 10, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.3, duration: 0.4 }}
                >
                  {description}
                </motion.div>
              )}
            </div>
          </div>

          {bannerImage && (
            <motion.div
              className="relative h-80 w-80 md:h-52 md:w-52 lg:h-80 lg:w-80 rounded-lg overflow-hidden flex-shrink-0"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.1, duration: 0.4 }}
            >
              <Image
                src={(process.env._AWS_END_POINT || '') + '/' + bannerImage.name}
                alt="Innovation banner"
                fill
                className="object-cover"
              />
            </motion.div>
          )}
        </div>
        {currentPathname === '/browse-innovations' && (
          <Link href="/how-to-submit" className="flex self-end">
            <Button
              variant="gradient"
              rightIcon={
                <Image
                  alt="arrowIcon of Innovations"
                  className="w-7 h-7 bg-white p-2 rounded-full"
                  height={20}
                  width={20}
                  src="/images/icons/darkArrowIcon.svg"
                />
              }
              className="pointer-events-auto"
              title={en.SUBMIT_YOUR_INNOVATION}
            />
          </Link>
        )}
      </motion.div>
    </div>
  );
};

export default GlassCard;
