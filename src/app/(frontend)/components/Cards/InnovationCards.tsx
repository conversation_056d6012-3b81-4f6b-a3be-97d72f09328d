'use client';
import { en } from '@/app/(frontend)/translations';
import { motion } from 'framer-motion';
import { useSearchParams } from 'next/navigation';
import { FC, Suspense } from 'react';
import { InnovationCardsProps } from '../../types/data.types';
import InnovationCardSkeleton from '../Skeleton/InnovationCardSkeleton';
import { InnovationCard } from './innovationCard';

const InnovationCards: FC<InnovationCardsProps> = ({
  innovations,
  loader,
  isMyInnovations = false,
}) => {
  const searchParams = useSearchParams();
  const isMyInnovation =
    (isMyInnovations || searchParams.get('myInnovations') === 'true') &&
    localStorage.getItem('atlasInfo');

  return (
    <Suspense fallback={<div></div>}>
      <motion.div
        className="bg-[#F6F6F6] rounded-2xl p-5"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <motion.p
          className="text-[#7E7E7F] text-sm font-medium mb-3"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2 }}
        >
          {isMyInnovations ? 'My Innovations' : en.BROWSE_INNOVATIONS}
        </motion.p>
        {loader ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {[...Array(6)].map((_, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <InnovationCardSkeleton />
              </motion.div>
            ))}
          </div>
        ) : innovations && innovations.length > 0 ? (
          <div
            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4"
            // staggerDelay={0.05}
          >
            {innovations.map((item) => (
              <InnovationCard item={item as any} isMyInnovation={Boolean(isMyInnovation)} />
            ))}
          </div>
        ) : (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4"
          >
            <div />
            <div className="flex items-center justify-center h-full">
              <p className="text-[#7E7E7F] text-sm font-medium text-center py-4">
                No innovations found.
              </p>
            </div>
          </motion.div>
        )}
      </motion.div>
    </Suspense>
  );
};

export default InnovationCards;
