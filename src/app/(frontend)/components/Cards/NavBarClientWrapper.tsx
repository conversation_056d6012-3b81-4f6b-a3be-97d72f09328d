'use client';

import React, { Suspense } from 'react';
import { NavBarUI } from '../UI/navBar';

interface NavBarClientWrapperProps {
  pathname: string;
}

export const NavBarClientWrapper: React.FC<NavBarClientWrapperProps> = ({ pathname }) => {
  return (
    <Suspense fallback={<div className="h-[60px]" />}>
      <NavBarUI color="text-Pure-White-FFFFFF" pathname={pathname} isAuthenticated={false} />
    </Suspense>
  );
};
