// components/InnovationCard.tsx
'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';
import Button from '../UI/Button';

interface InnovationCardProps {
  item: {
    id: string;
    slug?: string;
    title: string;
    description: string;
    status?: string;
    organizationName?: string;
  };
  isMyInnovation?: boolean;
  isFuture?: boolean;
}

export const InnovationCard = ({ item, isMyInnovation, isFuture }: InnovationCardProps) => {
  return (
    <Link href={item.slug ? `/innovation/${item.slug}` : '#'}>
      <motion.div
        className={`${isFuture ? 'bg-gray-100 ' : 'bg-white '} rounded-2xl py-5 px-4 h-[400px] flex flex-col transition-shadow duration-300`}
        whileHover={{
          y: -5,
          boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
        }}
        transition={{ type: 'spring', stiffness: 300 }}
      >
        {/* Header: Organization and Logo */}
        <div className="flex gap-5 justify-between items-start mb-6">
          <div className="text-sm border border-[#0B0B0E] rounded-full px-5 py-1 font-semibold">
            {item?.organizationName || 'Unknown Org'}
          </div>
        </div>

        {/* Content: Title and Description */}
        <div className="mb-6 flex-grow">
          <h4 className="font-semibold text-2xl text-[#0B0B0E] mb-4 line-clamp-2 h-[60px]">
            {item?.title}
          </h4>
          <p
            className="text-sm font-semibold -tracking-tighter text-[#BABABA] line-clamp-3 h-[60px]"
            dangerouslySetInnerHTML={{
              __html: item?.description || '',
            }}
          />
        </div>

        {/* Status and Share */}
        <div className="flex justify-between items-center mb-6">
          {item.status === 'pending' && (
            <p className="text-sm font-bold text-[#D29220] capitalize bg-[#FFDB9C] p-3 rounded-3xl">
              Under Review
            </p>
          )}
          {isMyInnovation && (
            <Link href={`/how-to-submit/submit-an-innovation/${item.id}`}>
              <Image
                src="/images/icons/edit.svg"
                width={18}
                height={18}
                alt="Edit"
                className="cursor-pointer"
              />
            </Link>
          )}
        </div>

        {/* CTA Button */}
        <Button
          className="w-full hover:scale-100"
          title="Read more"
          variant="outline"
          rightIcon={
            <Image src="/images/icons/darkArrowIcon.svg" width={6} height={6} alt="Arrow" />
          }
        />
      </motion.div>
    </Link>
  );
};
