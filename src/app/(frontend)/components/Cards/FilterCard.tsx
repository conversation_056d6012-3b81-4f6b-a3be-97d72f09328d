'use client';
import Image from 'next/image';
import React, { Fragment, useEffect, useRef, useState } from 'react';
import { FilterComponentProps, FilterOption, Filters } from '../../types/data.types';
import { getDocument, getWindow } from '../../utils/clientUtils';
import FilterSkeleton from '../Skeleton/FilterSkeleton';

type FilterEntry = [keyof Filters, string[]];
type FilterType = keyof Omit<Filters, 'myInnovations'>;
const FilterCard: React.FC<FilterComponentProps> = ({
  filters,
  setFilters,
  filterOptions,
  setFlowmap,
  searchQuery,
  setSearchQuery,
  // hideMyInnovationsFilter = true,
}) => {
  const [openDropdown, setOpenDropdown] = useState<FilterType | null>(null);
  const wrapperRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [innovationClasses, setInnovationClasses] = useState<FilterOption[]>([]);
  // const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    const window = getWindow();
    if (!window) return;

    if (typeof window !== 'undefined') {
      // setIsAuthenticated(!!localStorage.getItem('atlasInfo'));

      // Parse innovation class from URL
      const params = new URLSearchParams(window?.location.search);
      const innovationClassFromUrl = params.get('innovationClass');

      if (innovationClassFromUrl) {
        setFilters({
          ...defaultFilters,
          innovationClass: [innovationClassFromUrl],
        });

        // Set flowmap if innovation class is found in URL
        const selectedInnovationClass = innovationClasses.find(
          (ic) => ic.value === innovationClassFromUrl,
        );
        if (selectedInnovationClass) {
          setFlowmap({
            ...selectedInnovationClass,
            showChart: true,
            isSmall: true,
          });
        } else {
          setFlowmap(null);
        }
      }
    }
  }, [innovationClasses]);

  useEffect(() => {
    const fetchInnovationClasses = async () => {
      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_BACKEND_URL}/api/innovationClass/getOptions`,
        );
        const data = await response.json();
        setInnovationClasses(data);
      } catch (error) {
        console.error('Failed to fetch innovation classes:', error);
      } finally {
        setIsLoading(false); // Stop loading when data is fetched
      }
    };
    fetchInnovationClasses();
  }, []);

  // Close dropdown when clicking outside
  useEffect(() => {
    const doc = getDocument();
    if (!doc) return;

    const handleClickOutside = (event: MouseEvent) => {
      if (wrapperRef.current && !wrapperRef.current.contains(event.target as Node)) {
        setOpenDropdown(null);
      }
    };
    doc?.addEventListener('mousedown', handleClickOutside);
    return () => {
      doc?.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Toggle dropdown
  const toggleDropdown = (name: FilterType) => {
    setOpenDropdown((prev) => (prev === name ? null : name));
  };

  // Remove a filter
  const removeFilter = (filterType: FilterType, value: string) => {
    const updatedFilters = filters[filterType].filter((item) => item !== value);
    setFilters({ ...filters, [filterType]: updatedFilters });
  };

  // Toggle a filter
  const handleFilterToggle = (filterType: FilterType, value: string) => {
    if (filters?.innovationClass?.length > 0 && filterType !== 'innovationClass') {
      return; // Disable other filters if innovationClass is active
    }

    const currentValues = filters[filterType];
    let newFilters: Filters;

    if (currentValues.includes(value)) {
      newFilters = {
        ...filters,
        [filterType]: currentValues.filter((item) => item !== value),
      };
    } else {
      newFilters = {
        ...filters,
        [filterType]: [...currentValues, value],
      };
    }

    // If innovationClass is selected, clear other filters
    if (filterType === 'innovationClass' && !currentValues.includes(value)) {
      newFilters = {
        ...defaultFilters,
        innovationClass: [value],
      };
    }

    setFilters(newFilters);
  };

  const defaultFilters = {
    category: [],
    interventionType: [],
    currentStageOfDevelopment: [],
    myInnovations: false,
    innovationClass: [], // Add innovationClass to default filters
  };

  return (
    <div className="my-6 sm:my-10 bg-white p-4 sm:p-6 rounded-xl shadow-sm border border-gray-100">
      <div className="flex justify-between items-center mb-4 sm:mb-5">
        <div className="relative w-full">
          <Image
            alt="search icon"
            className="w-6 h-6 sm:w-8 sm:h-8 p-1 rounded-full absolute top-1/2 left-2 sm:left-3 transform -translate-y-1/2 bg-gray-100"
            height={20}
            width={20}
            src="/images/icons/searchIcon.svg"
          />
          <input
            type="text"
            placeholder="Search innovations..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 sm:pl-12 pr-3 sm:pr-4 py-2 sm:py-3 rounded-full border border-gray-200 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 text-xs sm:text-sm md:text-base transition-all duration-300"
          />
        </div>
      </div>
      {isLoading ? (
        <FilterSkeleton />
      ) : (
        <Fragment>
          {/* Active Filters */}
          {Boolean(
            filters?.category?.length ||
              filters?.currentStageOfDevelopment?.length ||
              filters?.innovationClass?.length ||
              filters?.interventionType?.length,
          ) && (
            <div className="my-4 sm:my-6 md:my-8 flex flex-wrap gap-1.5 sm:gap-2">
              {(Object.entries(filters) as FilterEntry[]).map(([key, values]) =>
                key !== 'myInnovations'
                  ? values
                      .filter((v) => v)
                      .map((val) => (
                        <div
                          key={`${key}-${val}`}
                          className="flex items-center bg-blue-100 text-blue-700 px-2 py-1 rounded-full text-xs sm:text-sm"
                        >
                          <span className="truncate max-w-[150px] sm:max-w-none">{val}</span>
                          <button
                            onClick={() => {
                              if (key === 'innovationClass') {
                                setFilters(defaultFilters);
                                setFlowmap(null);
                              } else {
                                removeFilter(key as FilterType, val);
                              }
                            }}
                            className="ml-1 focus:outline-none"
                            aria-label={`Remove ${val} filter`}
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="12"
                              height="12"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              className="sm:w-3.5 sm:h-3.5"
                            >
                              <line x1="18" y1="6" x2="6" y2="18"></line>
                              <line x1="6" y1="6" x2="18" y2="18"></line>
                            </svg>
                          </button>
                        </div>
                      ))
                  : null,
              )}
            </div>
          )}
          <p
            className={`px-2 flex  sm:hidden sm:px-4 py-1.5 sm:py-2 text-sm sm:text-base font-bold lg:font-medium`}
          >
            Filters
          </p>
          {/* Filter Dropdowns */}
          <div ref={wrapperRef} className="flex  flex-wrap gap-2 sm:gap-3">
            {/* Filter Label */}
            <div className="relative">
              <p
                className={`px-2 hidden sm:flex sm:px-4 py-1.5 sm:py-2 text-sm sm:text-base font-bold lg:font-medium`}
              >
                Filters
              </p>
            </div>
            {/* Intervention Type Filter */}
            <div className="relative">
              <button
                onClick={() => toggleDropdown('interventionType')}
                className={`px-2.5 sm:px-4 py-1.5 sm:py-2 rounded-lg ${
                  filters?.interventionType?.length > 0
                    ? 'bg-blue-100 text-blue-700'
                    : 'bg-gray-100 text-gray-700'
                } text-xs sm:text-sm font-medium shadow-sm hover:shadow-md transition-all duration-200 flex items-center gap-1 ${
                  filters?.innovationClass?.length > 0 ? 'opacity-50 cursor-not-allowed' : ''
                }`}
                disabled={filters?.innovationClass?.length > 0}
              >
                <span className="whitespace-nowrap">Intervention Type</span>
                {filters?.interventionType?.length > 0 && (
                  <span className="ml-1 bg-blue-600 text-white text-xs font-bold rounded-full w-4 h-4 sm:w-5 sm:h-5 flex items-center justify-center">
                    {filters?.interventionType?.length}
                  </span>
                )}
              </button>
              {openDropdown === 'interventionType' && filters?.innovationClass?.length === 0 && (
                <div className="absolute bg-white shadow-lg rounded-lg p-2 sm:p-3 mt-2 z-10 w-[200px] sm:min-w-[280px] max-h-48 sm:max-h-64 overflow-y-auto custom-scrollbar left-0 sm:left-auto right-0 sm:right-auto border border-gray-100">
                  <div className="mb-2 pb-2 border-b border-gray-100">
                    <span className="text-xs font-semibold text-gray-500 uppercase">
                      Select Intervention Types
                    </span>
                  </div>
                  {filterOptions?.interventionTypes?.map((type) => (
                    <label
                      key={type?.id}
                      className="flex items-center gap-2 text-xs sm:text-sm text-gray-700 py-1 sm:py-1.5 px-2 rounded hover:bg-gray-50"
                    >
                      <input
                        type="checkbox"
                        checked={filters?.interventionType?.includes(type?.value)}
                        onChange={() => handleFilterToggle('interventionType', type?.value)}
                        className="h-3 w-3 sm:h-4 sm:w-4 text-blue-600 rounded focus:ring-blue-500"
                        disabled={filters?.innovationClass?.length > 0}
                      />
                      <span className="truncate">{type?.value}</span>
                    </label>
                  ))}
                </div>
              )}
            </div>

            {/* Stage Filter */}
            <div className="relative">
              <button
                onClick={() => toggleDropdown('currentStageOfDevelopment')}
                className={`px-2.5 sm:px-4 py-1.5 sm:py-2 rounded-lg ${
                  filters?.currentStageOfDevelopment?.length > 0
                    ? 'bg-blue-100 text-blue-700'
                    : 'bg-gray-100 text-gray-700'
                } text-xs sm:text-sm font-medium shadow-sm hover:shadow-md transition-all duration-200 flex items-center gap-1 ${
                  filters?.innovationClass?.length > 0 ? 'opacity-50 cursor-not-allowed' : ''
                }`}
                disabled={filters?.innovationClass?.length > 0}
              >
                <span>Stage</span>
                {filters?.currentStageOfDevelopment?.length > 0 && (
                  <span className="ml-1 bg-blue-600 text-white text-xs font-bold rounded-full w-4 h-4 sm:w-5 sm:h-5 flex items-center justify-center">
                    {filters?.currentStageOfDevelopment?.length}
                  </span>
                )}
              </button>
              {openDropdown === 'currentStageOfDevelopment' &&
                filters?.innovationClass?.length === 0 && (
                  <div className="absolute bg-white shadow-lg rounded-lg p-2 sm:p-3 mt-2 z-10 w-[200px] sm:min-w-[250px] max-h-48 sm:max-h-64 overflow-y-auto custom-scrollbar left-0 sm:left-auto right-0 sm:right-auto border border-gray-100">
                    <div className="mb-2 pb-2 border-b border-gray-100">
                      <span className="text-xs font-semibold text-gray-500 uppercase">
                        Select Development Stages
                      </span>
                    </div>
                    {filterOptions?.stages?.map((stage) => (
                      <label
                        key={stage?.id}
                        className="flex items-center gap-2 text-xs sm:text-sm text-gray-700 py-1 sm:py-1.5 px-2 rounded hover:bg-gray-50"
                      >
                        <input
                          type="checkbox"
                          checked={filters?.currentStageOfDevelopment?.includes(stage?.value)}
                          onChange={() =>
                            handleFilterToggle('currentStageOfDevelopment', stage?.value)
                          }
                          className="h-3 w-3 sm:h-4 sm:w-4 text-blue-600 rounded focus:ring-blue-500"
                          disabled={filters?.innovationClass?.length > 0}
                        />
                        <span className="truncate">{stage?.value}</span>
                      </label>
                    ))}
                  </div>
                )}
            </div>
            {(filters.category.length > 0 ||
              filters.interventionType.length > 0 ||
              filters.currentStageOfDevelopment.length > 0 ||
              filters.myInnovations) && (
              <div className="relative">
                <button
                  onClick={() => {
                    setFilters(defaultFilters);
                    setFlowmap(null);
                  }}
                  className={`px-2.5 sm:px-4 py-1.5 sm:py-2 rounded-lg ${
                    filters?.innovationClass?.length > 0
                      ? 'bg-blue-100 text-blue-700'
                      : 'bg-gray-100 text-gray-700'
                  } text-xs sm:text-sm font-medium shadow-sm hover:shadow-md transition-all duration-200 flex items-center gap-1`}
                  aria-label="Clear all filters"
                >
                  <span>Clear All</span>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="12"
                    height="12"
                    viewBox="0 0 24 24"
                    fill="blue"
                    stroke="blue"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="sm:w-4 sm:h-4"
                  >
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                  </svg>
                </button>
              </div>
            )}
          </div>
        </Fragment>
      )}
    </div>
  );
};

export default FilterCard;
