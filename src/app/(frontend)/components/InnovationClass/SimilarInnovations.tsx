'use client';

import { useEffect, useState } from 'react';
import { Innovation } from '../../types/data.types';
import { InnovationCard } from '../Cards/innovationCard';
import CircularLoader from '../UI/CircularLoader';

interface SimilarInnovationsProps {
  innovationClass: any;
  currentInnovationId?: string; // Optional ID of the current innovation to exclude from similar innovations
}

export default function SimilarInnovations({
  innovationClass,
  currentInnovationId,
}: SimilarInnovationsProps) {
  const [innovations, setInnovations] = useState<Innovation[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchInnovations = async () => {
      try {
        let innovationsData: Innovation[] = [];

        // If innovations are already included in the innovationClass data
        if (innovationClass.innovations && Array.isArray(innovationClass.innovations)) {
          innovationsData = innovationClass.innovations;
        } else {
          // Otherwise fetch them from the API
          const response = await fetch(
            `${process.env.NEXT_PUBLIC_BACKEND_URL}/api/innovations/innovations?innovationClass=${encodeURIComponent(innovationClass.slug || innovationClass.name)}`,
          );
          if (!response.ok) throw new Error('Failed to fetch innovations');

          const data = await response.json();
          innovationsData = data.docs || [];
        }

        // Filter out the current innovation if currentInnovationId is provided
        if (currentInnovationId) {
          innovationsData = innovationsData.filter(
            (innovation) => innovation.id !== currentInnovationId,
          );
        }

        console.log('Similar innovations found:', innovationsData.length);
        setInnovations(innovationsData);
      } catch (error) {
        console.error('Error fetching innovations:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchInnovations();
  }, [innovationClass, currentInnovationId]);

  if (loading) {
    return <CircularLoader />;
  }

  // Don't render anything if there are no innovations
  if (!innovations || innovations.length === 0) {
    console.log('No similar innovations found, hiding section');
    return null;
  }

  return (
    <div className="mt-8 mb-12">
      <div className="flex items-center justify-between pb-2 mb-6 relative after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-1/2 after:h-[3px] ">
        <h3 className="text-2xl font-bold text-blue-900">Innovations</h3>
        {/* <Image
          src="/images/icons/globe-with-arrow-svgrepo-com (1).svg"
          width={35}
          height={35}
          alt="Similar Innovations"
          className="absolute bottom-[-15px] left-[calc(50%)]"
        /> */}
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 bg-[#F6F6F6] rounded-2xl p-6">
        {innovations.map((innovation) => (
          <InnovationCard key={innovation.id} item={innovation as any} isMyInnovation={false} />
        ))}
      </div>
    </div>
  );
}
