import { Component, ErrorInfo } from 'react';
import { ErrorBoundaryProps, ErrorBoundaryState } from '../types/data.types';

class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  state: ErrorBoundaryState = { hasError: false, error: null };

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, info: ErrorInfo) {
    this.props.onError?.(error, info);
  }

  resetErrorBoundary = () => {
    this.props.onReset?.();
    this.setState({ hasError: false, error: null });
  };

  render() {
    const { hasError, error } = this.state;
    const { children, fallback, FallbackComponent, fallbackRender } = this.props;

    if (hasError && error) {
      if (FallbackComponent)
        return <FallbackComponent error={error} resetErrorBoundary={this.resetErrorBoundary} />;
      if (fallbackRender)
        return fallbackRender({ error, resetErrorBoundary: this.resetErrorBoundary });
      if (fallback) return <>{fallback}</>;
      return <p>Something went wrong.</p>;
    }

    return children;
  }
}

export default ErrorBoundary;
