'use client';
import GlassCard from '@/app/(frontend)/components/Cards/glassCard';
import InsightsLessons from '@/app/(frontend)/components/InovationDetails/InsightsLessons';
import SummaryOfInnovation from '@/app/(frontend)/components/InovationDetails/SummaryOfInovation';
import Button from '@/app/(frontend)/components/UI/Button';
import { useLayoutEffect } from 'react';
import { getWindow } from '../../utils/clientUtils';

const InnovationPreview = ({
  currentInnovationData,
  onBack,
  onSubmit,
}: {
  currentInnovationData: any;
  onBack: (data: any) => void;
  onSubmit: () => void;
}) => {
  const window = getWindow();
  if (!window) return null;
  useLayoutEffect(() => {
    window?.scrollTo(0, 0);
  }, []);
  return (
    <div>
      <GlassCard
        title={`Preview of ${currentInnovationData?.title || ''}`}
        description={currentInnovationData?.organizationName || ''}
        pathname="/innovation"
        isAuthenticated={Boolean(localStorage.getItem('atlasInfo'))}
        bannerImage={currentInnovationData?.bannerImage}
      />
      <div className="mx-auto max-w-[80vw] md:px-0 px-5">
        <div className="mt-8">
          <div className="flex items-center justify-between pb-2 mb-6 relative after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-1/2 after:h-[3px] after:bg-orange-500">
            <h3 className="text-2xl font-bold text-blue-900">About The Innovation</h3>
            <span className="inline-flex items-center justify-center w-8 h-8 bg-orange-100 rounded-full">
              <span className="text-orange-500">ⓘ</span>
            </span>
          </div>
          <SummaryOfInnovation description={currentInnovationData?.description || ''} />
        </div>

        <div className="">
          <div className="flex items-center justify-between pb-2 mb-6 relative after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-1/2 after:h-[3px] after:bg-orange-500">
            <h3 className="text-2xl font-bold text-blue-900">Categories</h3>
            <span className="inline-flex items-center justify-center w-8 h-8 bg-orange-100 rounded-full">
              <span className="text-orange-500">⊕</span>
            </span>
          </div>
          {currentInnovationData && (
            <InsightsLessons innovationDetails={currentInnovationData} className={'w-full'} />
          )}
        </div>

        {currentInnovationData?.supportingDocument &&
          currentInnovationData.supportingDocument.length > 0 && (
            <div className="mt-8">
              <div className="flex items-center justify-between pb-2 mb-6 relative after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-1/2 after:h-[3px] after:bg-orange-500">
                <h3 className="text-2xl font-bold text-blue-900">Attachments</h3>
                <span className="inline-flex items-center justify-center w-8 h-8 bg-orange-100 rounded-full">
                  <span className="text-orange-500">⊕</span>
                </span>
              </div>
              <div className="flex flex-wrap gap-3">
                {currentInnovationData.supportingDocument.map((doc: any) => (
                  <div
                    key={doc?.id}
                    className="flex items-center gap-2 bg-white px-3 py-2 rounded-lg shadow-sm"
                  >
                    <span className="text-red-500">📄</span>
                    <span className="text-sm font-medium text-gray-700">
                      {doc?.filename || doc?.name}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}

        {currentInnovationData?.link && (
          <div className="mt-8 mb-8">
            <div className="flex items-center justify-between pb-2 mb-6 relative after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-1/2 after:h-[3px] after:bg-orange-500">
              <h3 className="text-2xl font-bold text-blue-900">Read More</h3>
              <span className="inline-flex items-center justify-center w-8 h-8 bg-orange-100 rounded-full">
                <span className="text-orange-500">⊕</span>
              </span>
            </div>
            <a className="text-blue-600 hover:text-blue-800 italic">{currentInnovationData.link}</a>
          </div>
        )}
        <div className="flex gap-4 mb-8">
          <Button
            size="md"
            variant="outline"
            title={'Back'}
            onClick={() => onBack(currentInnovationData)}
          />
          <Button
            size="md"
            variant="gradient"
            title={'Submit'}
            onClick={onSubmit}
            //   () =>
            //   router.push(`/browse-innovations?myInnovations=${encodeURIComponent('true')}`)
            // }
          />
        </div>
      </div>
    </div>
  );
};

export default InnovationPreview;
