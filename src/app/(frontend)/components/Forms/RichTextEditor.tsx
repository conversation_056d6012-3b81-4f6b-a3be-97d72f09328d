'use client';
import Quill from 'quill';
import QuillImageDropAndPaste from 'quill-image-drop-and-paste';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import ReactQuill from 'react-quill-new';
import 'react-quill-new/dist/quill.snow.css';
import { container } from '../../utils/constant';

interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
}
const MAX_FILE_SIZE_MB = 6;
const MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024;

const RichTextEditor: React.FC<RichTextEditorProps> = ({
  value,
  onChange,
  placeholder = 'Enter detailed description of your Innovation/Product/Technology',
}) => {
  const quillRef = useRef<any>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [toast, setToast] = useState<{
    message: string;
    type: 'success' | 'error';
  } | null>(null);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      Quill.register('modules/imageDropAndPaste', QuillImageDropAndPaste);
    }
  }, []);

  useEffect(() => {
    if (toast) {
      const timeout = setTimeout(() => setToast(null), 3000);
      return () => clearTimeout(timeout);
    }
  }, [toast]);

  const showToast = useCallback((message: string, type: 'success' | 'error') => {
    setToast({ message, type });
  }, []);

  const insertImage = useCallback((url: string) => {
    const editor = quillRef?.current?.getEditor();
    const range = editor?.getSelection(true);
    if (editor && range) {
      editor.insertEmbed(range.index, 'image', url, 'user');
    }
  }, []);

  const uploadImage = useCallback(
    async (file: File): Promise<string> => {
      if (file.size > MAX_FILE_SIZE_BYTES) {
        showToast(`File ${file.name} exceeds the maximum size of ${MAX_FILE_SIZE_MB}MB.`, 'error');
        throw new Error('File too large');
      }

      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/upload`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        const errorMessage = `Upload failed: ${errorData?.error || 'Unknown error'}`;
        throw new Error(errorMessage);
      }

      const data = await response.json();
      const fileName = data?.doc?.filename || file.name;
      return `${process.env._AWS_END_POINT || ''}/${fileName}`;
    },
    [showToast],
  );

  const handleImageUpload = useCallback(() => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/jpeg,image/png,image/webp';
    input.click();

    input.onchange = async () => {
      const file = input.files?.[0];
      if (!file) return;

      try {
        setIsUploading(true);
        const imageUrl = await uploadImage(file);
        insertImage(imageUrl);
        showToast('Image uploaded successfully.', 'success');
      } catch (error) {
        console.error(error);
        showToast((error as Error).message, 'error');
      } finally {
        setIsUploading(false);
      }
    };
  }, [insertImage, uploadImage, showToast]);

  const modules = useMemo(
    () => ({
      toolbar: {
        container,
        handlers: {
          image: handleImageUpload,
        },
      },
      imageDropAndPaste: {
        handler: async (_dataUrl: string, type: string, imageData: Blob) => {
          try {
            setIsUploading(true);
            const file = new File([imageData], 'pasted-image.png', { type });
            const imageUrl = await uploadImage(file);
            insertImage(imageUrl);
            showToast('Image uploaded successfully.', 'success');
          } catch (error) {
            console.error(error);
            showToast((error as Error).message, 'error');
          } finally {
            setIsUploading(false);
          }
        },
      },
    }),
    [handleImageUpload, insertImage, uploadImage, showToast],
  );

  return (
    <div className="mb-4 relative react-quill-container">
      {isUploading && (
        <div className="absolute inset-0 bg-white bg-opacity-70 flex items-center justify-center z-10">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        </div>
      )}

      <ReactQuill
        ref={quillRef as any}
        theme="snow"
        value={value}
        onChange={onChange}
        modules={modules}
        placeholder={placeholder}
        className="min-h-[80px] max-h-[400px] overflow-auto border-0 shadow-sm bg-white"
      />

      {toast && (
        <div
          className={`mt-2 p-2 rounded text-sm ${
            toast.type === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          }`}
        >
          {toast.message}
        </div>
      )}
    </div>
  );
};

export default RichTextEditor;
