'use client';
import { AnimatePresence, motion } from 'framer-motion';
import type { Config } from 'jodit/esm/config';
import type { DeepPartial } from 'jodit/esm/types';
import dynamic from 'next/dynamic';
import Image from 'next/image';
import React, { ChangeEvent, useEffect, useMemo, useRef, useState } from 'react';
import {
  BaseInputProps,
  FileUploadProps,
  MultiSelectProps,
  NewMultiSelectProps,
  NewOption,
  PositionType,
  RadioInputProps,
  SingleSelectProps,
  TextAreaProps,
  TextInputProps,
} from '../../types/data.types';
import { getDocument } from '../../utils/clientUtils';
import DescriptionTooltip from '../UI/DescriptionTooltip';
const descriptionTooltipPosition: PositionType = 'left-center';
const JoditEditor = dynamic(() => import('jodit-react'), { ssr: false });
const BaseInput: React.FC<BaseInputProps> = ({
  label,
  name,
  required = false,
  errorMessage,
  children,
  className = '',
}) => {
  return (
    <motion.div
      className={`mb-6 ${className}`}
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {/* Label */}
      <motion.label
        htmlFor={name}
        className="block text-md font-semibold mb-1 font-Poppins"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.1 }}
      >
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </motion.label>

      {/* Input Field (children) */}
      <motion.div
        className="mt-2"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.2 }}
      >
        {children}
      </motion.div>

      {/* Error Message */}
      <AnimatePresence>
        {errorMessage && (
          <motion.p
            className="text-red-500 text-xs mt-1"
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
          >
            {errorMessage}
          </motion.p>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

const TextInput: React.FC<TextInputProps> = ({
  label,
  name,
  placeholder,
  value,
  className,
  onChange,
  onBlur,
  onKeyDown,
  required = false,
  touched = false,
  disabled = false,
  type = 'text',
  errorMessage = null,
}) => {
  const hasError = touched && errorMessage;

  return (
    <BaseInput
      label={label}
      name={name}
      required={required}
      errorMessage={hasError ? errorMessage : undefined}
      className={``}
    >
      <motion.input
        id={name}
        type={type}
        disabled={disabled}
        value={value}
        placeholder={placeholder || `Enter ${label}`}
        onChange={(e) => onChange(e.target.value)}
        onBlur={onBlur}
        onKeyDown={onKeyDown}
        className={`block w-full px-4 py-4 border ${
          hasError ? 'border-red-500' : 'border-Light-Gray-F6F6F6'
        } rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-Light-Blue-9999FF ${className} ${disabled ? 'cursor-not-allowed text-Gray-888888' : ''}`}
        whileFocus={{ boxShadow: '0 0 0 3px rgba(99, 102, 241, 0.2)' }}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3 }}
      />
    </BaseInput>
  );
};

const TextArea: React.FC<TextAreaProps> = ({
  label,
  name,
  placeholder,
  value,
  className,
  onChange,
  required = false,
  errorMessage = null,
}) => {
  const [touched, setTouched] = useState(false);
  const hasError = touched && errorMessage;

  return (
    <BaseInput
      label={label}
      name={name}
      required={required}
      errorMessage={errorMessage || undefined}
    >
      <motion.textarea
        id={name}
        value={value}
        placeholder={placeholder || `Enter ${label}`}
        onChange={(e) => onChange(e.target.value)}
        onBlur={() => setTouched(true)}
        className={`block w-full px-4 py-4 border ${
          hasError ? 'border-red-500' : 'border-Light-Gray-F6F6F6'
        } rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-Light-Blue-9999FF ${className}`}
        whileFocus={{ boxShadow: '0 0 0 3px rgba(99, 102, 241, 0.2)' }}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3 }}
      />
    </BaseInput>
  );
};

const RadioInput: React.FC<RadioInputProps> = ({
  label,
  name,
  options,
  value,
  onChange,
  onBlur,
  required = false,
  touched = false,
  errorMessage = 'This field is required',
}) => {
  const hasError = required && touched && !value;

  return (
    <BaseInput
      label={label}
      name={name}
      required={required}
      errorMessage={hasError ? errorMessage : undefined}
    >
      <div className="grid grid-cols-2 gap-4">
        {options.map((option) => (
          <label
            key={option.value}
            className="flex items-center space-x-3 p-3 border rounded-lg cursor-pointer hover:bg-gray-50 transition duration-150 ease-in-out"
          >
            <input
              type="radio"
              name={name}
              value={option.value}
              checked={value === option.value}
              onChange={() => {
                onChange(option.value);
              }}
              onBlur={onBlur}
              className="form-radio h-5 w-5 text-indigo-600 transition duration-150 ease-in-out"
            />
            <span className="text-gray-700">{option.label}</span>
          </label>
        ))}
      </div>
    </BaseInput>
  );
};

const SingleSelect: React.FC<SingleSelectProps> = ({
  label,
  name,
  className,
  placeholder,
  options,
  value,
  onChange,
  required = false,
  errorMessage,
}) => {
  const [touched, setTouched] = useState(false);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const hasError = required && touched && !value;

  // Determine the current value to display
  const currentValue = typeof value === 'object' && value !== null ? value.id : value;
  const selectedOption = options.find(
    (opt) => opt.id === (typeof value === 'object' ? value?.id : value),
  );

  // Toggle dropdown
  const toggleDropdown = () => {
    setDropdownOpen((prev) => !prev);
  };

  // Handle outside click to close dropdown
  useEffect(() => {
    const doc = getDocument();
    if (!doc) return;

    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setDropdownOpen(false);
      }
    };
    doc?.addEventListener('mousedown', handleClickOutside);
    return () => doc?.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Handle option selection
  const handleOptionClick = (option: any) => {
    onChange(option);
    setDropdownOpen(false);
    setTouched(true);
  };

  return (
    <BaseInput label={label} name={name} required={required} errorMessage={errorMessage}>
      <div className="relative w-full" ref={dropdownRef}>
        <motion.div
          className={`flex justify-between items-center w-full px-4 py-4 text-base border ${
            hasError ? 'border-red-500' : 'border-Light-Gray-F6F6F6'
          } rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 ${className} cursor-pointer bg-white`}
          onClick={toggleDropdown}
          whileHover={{ boxShadow: '0 2px 8px rgba(0,0,0,0.1)' }}
          whileTap={{ scale: 0.99 }}
          transition={{ duration: 0.2 }}
        >
          <motion.div className="flex-1 truncate" layout>
            {selectedOption ? (
              <motion.span
                initial={{ opacity: 0, y: 5 }}
                animate={{ opacity: 1, y: 0 }}
                key={selectedOption.id}
              >
                {selectedOption.value}
              </motion.span>
            ) : (
              <span className="text-gray-500">{placeholder || `Select ${label}`}</span>
            )}
          </motion.div>
          <motion.button
            type="button"
            className="text-gray-600 hover:text-gray-800 cursor-pointer"
            onClick={(e) => {
              e.stopPropagation();
              toggleDropdown();
            }}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <div className="w-6 h-6 flex items-center justify-center bg-gray-200 rounded-full">
              <motion.div
                animate={{ rotate: dropdownOpen ? 90 : 0 }}
                transition={{ duration: 0.3 }}
              >
                <Image
                  alt="Dropdown arrow"
                  height={12}
                  width={12}
                  src={'/images/icons/arrowIcon.svg'}
                />
              </motion.div>
            </div>
          </motion.button>
        </motion.div>

        <AnimatePresence>
          {dropdownOpen && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="absolute left-0 w-full mt-1 bg-white border border-gray-300 rounded shadow-md max-h-60 overflow-y-auto z-10"
            >
              {options.length > 0 ? (
                options
                  .sort((a, b) => a?.order - b?.order)
                  .map((option, index) => {
                    if ((option.description || '')?.length > 15) {
                      return (
                        <DescriptionTooltip
                          key={option.id || option.value}
                          description={option?.description || ''}
                          position={descriptionTooltipPosition}
                          slug={option.slug}
                          shouldShowReadMore
                          maxWords={10}
                        >
                          <motion.div
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ duration: 0.1, delay: index * 0.03 }}
                            className={`px-4 py-2 text-sm w-full cursor-pointer hover:text-white hover:bg-blue-500 ${
                              currentValue === option.id ? 'bg-blue-100' : ''
                            }`}
                            onClick={() => handleOptionClick(option)}
                            whileHover={{ scale: 1 }}
                          >
                            {option.value}
                          </motion.div>
                        </DescriptionTooltip>
                      );
                    } else {
                      return (
                        <motion.div
                          key={option.id || option.value}
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          transition={{ duration: 0.1, delay: index * 0.03 }}
                          className={`px-4 py-2 text-sm w-full cursor-pointer hover:text-white hover:bg-blue-500 ${
                            currentValue === option.id ? 'bg-blue-100' : ''
                          }`}
                          onClick={() => handleOptionClick(option)}
                          whileHover={{ scale: 1 }}
                        >
                          {option.value}
                        </motion.div>
                      );
                    }
                  })
              ) : (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="px-4 py-2 text-sm text-gray-500"
                >
                  No options available
                </motion.div>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
      {hasError && <p className="text-red-500 text-sm mt-2">{errorMessage}</p>}
    </BaseInput>
  );
};

const MultiSelect: React.FC<MultiSelectProps> = ({
  label,
  options,
  value,
  onChange,
  required = false,
  errorMessage = 'This field is required',
}) => {
  const [touched, setTouched] = useState(false);
  const hasError = required && touched && value.length === 0;

  const handleChange = (optionValue: string) => {
    const newValue = value.includes(optionValue)
      ? value.filter((v) => v !== optionValue)
      : [...value, optionValue];
    onChange(newValue);
    setTouched(true);
  };

  return (
    <div className="w-full">
      <label className="block text-gray-700 font-medium mb-2">{label}</label>
      <div className="grid grid-cols-2 gap-4 border p-4 rounded-lg">
        {options.map((option) => (
          <label
            key={option.value}
            className="flex text-md items-center space-x-2 p-2 border rounded cursor-pointer hover:bg-gray-100"
          >
            <input
              type="checkbox"
              value={option.value}
              checked={value.includes(option.value)}
              onChange={() => handleChange(option.value)}
              className="h-5 w-5 text-indigo-600 focus:ring-indigo-500"
            />
            <span className="text-gray-700">{option.label}</span>
          </label>
        ))}

        {/* </div> */}
      </div>
      {hasError && <p className="text-red-500 text-sm mt-2">{errorMessage}</p>}
    </div>
  );
};

const MultiSelectDropdown: React.FC<NewMultiSelectProps> = ({
  label,
  name,
  onChange,
  options,
  value = [],
  errorMessage,
  required = false,
  className = '',
  placeholder = 'Select Option',
}) => {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [touched, setTouched] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const hasError = required && touched && !value.length;
  const inputRef = useRef<HTMLInputElement>(null);

  // Toggle dropdown
  const toggleDropdown = () => {
    setDropdownOpen((prev) => !prev);
    // Focus the search input when opening dropdown
    if (!dropdownOpen && inputRef.current) {
      setTimeout(() => {
        inputRef.current?.focus();
      }, 10);
    }
  };

  // Handle outside click to close dropdown
  useEffect(() => {
    const doc = getDocument();
    if (!doc) return;

    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setDropdownOpen(false);
        setSearchTerm('');
      }
    };
    doc?.addEventListener('mousedown', handleClickOutside);
    return () => doc?.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Handle option selection
  const handleOptionClick = (option: NewOption) => {
    const isSelected = value.some((o) => o.value === option.value);
    const updatedOptions = isSelected
      ? value.filter((o) => o.value !== option.value) // Remove if already selected
      : [...value, option]; // Add if not selected
    onChange(updatedOptions);

    // Clear search term after selection
    setSearchTerm('');

    // Keep focus on input after selection
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  // Filter options based on search term
  const filteredOptions = options.filter(
    (option) =>
      option.value.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (option.label && option.label.toLowerCase().includes(searchTerm.toLowerCase())),
  );

  return (
    <BaseInput label={label} name={name} required={required} errorMessage={errorMessage}>
      <div className="w-full" ref={dropdownRef}>
        <div className="relative">
          <motion.div
            className={`p-2 flex border-Light-Gray-F6F6F6 bg-white rounded border ${hasError ? 'border-red-500' : 'border-Light-Gray-F6F6F6'}
                        rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-Light-Blue-9999FF ${className}`}
            whileHover={{ boxShadow: '0 2px 8px rgba(0,0,0,0.1)' }}
            transition={{ duration: 0.2 }}
            layout
          >
            <div className="flex flex-wrap gap-2 flex-auto">
              <AnimatePresence>
                {value.map((item) => (
                  <motion.div
                    key={item.value || item.label || item.id}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.2 }}
                    className="flex items-center p-2 bg-blue-100 text-Midnight-Royal-Blue-02018B rounded-full border border-Midnight-Royal-Blue-02018B"
                    layout
                  >
                    <span className="text-xs">{item.label || item.value}</span>
                    <motion.button
                      type="button"
                      className="ml-2 text-Midnight-Royal-Blue-02018B hover:text-blue-500"
                      onClick={() => handleOptionClick(item)}
                      whileHover={{ scale: 1.2 }}
                      whileTap={{ scale: 1 }}
                    >
                      &times;
                    </motion.button>
                  </motion.div>
                ))}
              </AnimatePresence>
              {((value.length === 0 && !dropdownOpen) || dropdownOpen) && (
                <motion.input
                  ref={inputRef}
                  className="flex-1 bg-transparent outline-none border-none p-2 min-w-[120px]"
                  placeholder={value.length === 0 ? placeholder : 'Type to search...'}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onFocus={() => {
                    setTouched(true);
                    setDropdownOpen(true);
                  }}
                  onClick={() => setDropdownOpen(true)}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.1 }}
                />
              )}
            </div>
            <motion.button
              type="button"
              className="text-gray-600 hover:text-gray-800 cursor-pointer"
              onClick={toggleDropdown}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <div className="w-6 h-6 flex items-center justify-center bg-gray-200 rounded-full">
                <motion.div
                  animate={{ rotate: dropdownOpen ? 90 : 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <Image
                    alt="Dropdown arrow"
                    height={12}
                    width={12}
                    src={'/images/icons/arrowIcon.svg'}
                  />
                </motion.div>
              </div>
            </motion.button>
          </motion.div>
          <AnimatePresence>
            {dropdownOpen && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.2 }}
                className="absolute left-0 w-full mt-1 bg-white border border-gray-300 rounded shadow-md max-h-60 overflow-y-auto z-10"
              >
                {filteredOptions.length > 0 ? (
                  filteredOptions
                    .sort((a, b) => a?.order - b?.order)
                    .map((item, index) =>
                      (item.description || '')?.length >= 15 ? (
                        <DescriptionTooltip
                          key={item.value || item?.label}
                          description={item.description || ''}
                          position={descriptionTooltipPosition}
                          slug={item.slug}
                          maxWords={10}
                          shouldShowReadMore={true}
                        >
                          <motion.div
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ duration: 0.1, delay: index * 0.03 }}
                            className={`px-4 py-2 text-sm w-full cursor-pointer hover:text-white hover:bg-blue-500 ${
                              value.some((o) => o.value === item.value) ? 'bg-blue-100' : ''
                            }`}
                            onClick={() => handleOptionClick(item)}
                            whileHover={{ scale: 0 }}
                          >
                            {item.value}
                          </motion.div>
                        </DescriptionTooltip>
                      ) : (
                        <motion.div
                          key={item.value || item?.label}
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          transition={{ duration: 0.1, delay: index * 0.03 }}
                          className={`px-4 py-2 text-sm w-full cursor-pointer hover:text-white hover:bg-blue-500 ${
                            value.some((o) => o.value === item.value) ? 'bg-blue-100' : ''
                          }`}
                          onClick={() => handleOptionClick(item)}
                          whileHover={{ scale: 1 }}
                        >
                          {item.value}
                        </motion.div>
                      ),
                    )
                ) : (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="px-4 py-2 text-sm text-gray-500"
                  >
                    No options found
                  </motion.div>
                )}
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </BaseInput>
  );
};

const FileUpload: React.FC<FileUploadProps> = ({
  label,
  name,
  loading = false,
  value = [],
  onChange,
  onRemove,
  required = false,
  errorMessage = 'This field is required',
}) => {
  const [touched, setTouched] = useState(false);

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    e.preventDefault();
    e.stopPropagation();
    const files = e.target.files ? Array.from(e.target.files) : [];
    if (files.length > 0) {
      // Directly pass the File objects to the parent component
      onChange(files);
    }
    setTouched(true);
  };

  const hasError = required && touched && (!Array.isArray(value) || value.length === 0);

  return (
    <div>
      <label className="block text-md font-bold ">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>
      <div className="relative mt-1 ">
        <input
          id={name}
          type="file"
          multiple
          accept="application/pdf"
          onChange={handleFileChange}
          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
        />
        <motion.div
          className="px-4 py-3 border-0 rounded-lg text-gray-600 bg-white flex items-center justify-between shadow-sm"
          whileHover={{ boxShadow: '0 2px 8px rgba(0,0,0,0.1)' }}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        >
          <motion.span initial={{ opacity: 0 }} animate={{ opacity: 1 }} key={value.length}>
            {Array.isArray(value) && value.length > 0
              ? `${value.length} file(s) selected`
              : 'Choose files'}
          </motion.span>

          {loading ? (
            <motion.div
              className="flex items-center gap-2"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
            >
              <svg
                className="animate-spin h-5 w-5 text-blue-500"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8H4z"></path>
              </svg>
              <span>Uploading...</span>
            </motion.div>
          ) : (
            <motion.button
              type="button"
              className="text-indigo-600 hover:text-indigo-700 font-medium"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Browse
            </motion.button>
          )}
        </motion.div>
        {hasError && <span className="text-red-500 text-sm mt-1">{errorMessage}</span>}
      </div>

      {/* Display Selected Files */}
      {Array.isArray(value) && value.length > 0 && (
        <motion.ul
          className="mt-3 space-y-2"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <AnimatePresence>
            {value?.map((file, index) => (
              <motion.li
                key={index}
                className="flex items-center justify-between bg-gray-100 px-3 py-2 rounded-md text-sm"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                transition={{ duration: 0.2 }}
                layout
              >
                <span className="truncate">
                  {/* Handle different file object structures */}
                  {typeof file === 'object' && 'name' in file
                    ? file.name
                    : typeof file === 'object' && (('filename' in file) as any)
                      ? (file as any).filename
                      : typeof file === 'string'
                        ? file
                        : 'File'}
                </span>
                <motion.button
                  type="button"
                  onClick={() => onRemove(index)}
                  className="text-red-500 hover:text-red-700"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  Remove
                </motion.button>
              </motion.li>
            ))}
          </AnimatePresence>
        </motion.ul>
      )}
    </div>
  );
};
export default function EditorComponent({ value, onChange }: any) {
  const editor = useRef(null);

  // Memoize the config to prevent re-renders
  const config: DeepPartial<Config> = useMemo(
    () => ({
      readonly: false,
      placeholder: 'Enter detailed description of your Innovation/Product/Technology',
      toolbarButtonSize: 'middle',
      toolbarAdaptive: false,
      showCharsCounter: false,
      showWordsCounter: false,
      showXPathInStatusbar: false,
      buttons: [
        'bold',
        'italic',
        'underline',
        'image',
        '|',
        'ul',
        'ol',
        '|',
        'outdent',
        'indent',
        '|',
        'font',
        'fontsize',
        '|',
        'link',
        '|',
        'align',
        'undo',
        'redo',
        '|',
        'hr',
        'copyformat',
      ],
    }),
    [],
  );

  useEffect(() => {
    if (!value) {
      onChange(''); // Ensure field is initialized to avoid validation issues
    }
  }, []);

  return (
    <div className="mb-4">
      <JoditEditor
        ref={editor}
        value={value || ''} // Ensure empty string if value is undefined
        onBlur={(content) => onChange(content)} // Trigger validation on blur
        onChange={() => {}} // Empty function to avoid cursor jumping issues
        config={config}
        className="min-h-[200px] max-h-[400px] overflow-auto border-0 shadow-sm rounded-2xl"
      />
    </div>
  );
}

// Export all components
export {
  BaseInput,
  FileUpload,
  MultiSelect,
  MultiSelectDropdown,
  RadioInput,
  SingleSelect,
  TextArea,
  TextInput,
};
