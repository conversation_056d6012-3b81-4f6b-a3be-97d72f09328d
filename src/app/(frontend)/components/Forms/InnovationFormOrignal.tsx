'use client';
import {
  FileUpload,
  MultiSelectDropdown,
  SingleSelect,
  TextArea,
  TextInput,
} from '@/app/(frontend)/components/Forms/InputComponent';
import Button from '@/app/(frontend)/components/UI/Button';
import LoadingOverlay from '@/app/(frontend)/components/UI/LoadingOverlay';
import Toast from '@/app/(frontend)/components/UI/Toast';
import { en } from '@/app/(frontend)/translations';
import { yupResolver } from '@hookform/resolvers/yup';
import 'cropperjs/dist/cropper.css';
import { AnimatePresence, motion } from 'framer-motion';
import dynamic from 'next/dynamic';
import Image from 'next/image';
import { ReactNode, useCallback, useEffect, useRef, useState } from 'react';
import Cropper from 'react-cropper';
import { Controller, Resolver, useForm } from 'react-hook-form';
import PhoneInput from 'react-phone-number-input';
import 'react-phone-number-input/style.css';
import * as yup from 'yup';
import { FilteredFormData, FormDataProps } from '../../types/data.types';
import { fetchOptions } from '../../utils/apiClient';
import InnovationPreviewModal from '../modals/InnovationPreviewModal';
import SubmissionSuccessModal from '../modals/SubmissionSuccessModal';
const RichTextEditor = dynamic(() => import('./RichTextEditor'), { ssr: false });
// ===============================
// Types & Interfaces
// ===============================

interface Option {
  value: string;
  label: string;
  id?: string;
  description?: string;
  slug?: string;
  order: number;
}

interface Options {
  currentStageOfDevelopment: Option[];
  interventionType: Option[];
}

interface UploadedFile {
  id: string;
  name: string;
}

interface NewOption {
  id: string;
  value: string;
  label: string;
  order: number;
  description?: string;
  slug?: string;
}

interface FormValues {
  title: string;
  organizationName: string;
  description: string;
  name: string;
  phoneNo: string;
  link: string;
  email: string;
  bannerImage?: { id: string; name: string } | null;
  currentStageOfDevelopment: {
    id: string;
    value: string;
    label: string;
  };
  interventionType: NewOption[];
  supportingDocument: File[];
  declaration: boolean;
}

// ===============================
// Constants
// ===============================

const PHONE_REGEX = /^\+[1-9]\d{7,14}$/;
const MAX_FILE_SIZE_MB = 6;
const MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024;

// ===============================
// Validation Schema
// ===============================
const schema = yup.object().shape({
  title: yup.string().required('Title is required').max(150),
  organizationName: yup.string().required('Organization Name is required').max(150),
  description: yup.string().required('Description is required'),
  name: yup.string().required('Full Name is required').max(150),
  phoneNo: yup
    .string()
    .required('Phone Number is required')
    .matches(PHONE_REGEX, 'Enter a valid phone number')
    .min(12, 'Phone number is too short')
    .max(14, 'Phone number is too long'),
  link: yup.string().url('Invalid URL').required('Link is required').max(150),
  email: yup.string().email('Invalid email address').required('Email is required').max(150),
  // interventionType: yup
  //   .array()
  //   .min(1, 'Please select at least one Categories/ Keywords'),
  currentStageOfDevelopment: yup
    .object()
    .shape({
      id: yup.string().required('Current Stage of Development ID is required'),
    })
    .required('Current Stage of Development is required'),
  supportingDocument: yup
    .array()
    .min(1, 'At least one supporting document is required')
    .required('Supporting document is required'),
  declaration: yup
    .bool()
    .oneOf([true], 'You must accept the declaration')
    .required('You must accept the declaration'),
});

// ===============================
// Helper Components
// ===============================

const FormCard = ({
  number,
  title,
  children,
  index = 0,
}: {
  number: string;
  title: string;
  children: ReactNode;
  index?: number;
}) => {
  const cardVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: (i: number) => ({
      opacity: 1,
      y: 0,
      transition: {
        delay: i * 0.1,
        duration: 0.5,
        ease: 'easeOut',
        when: 'beforeChildren',
        staggerChildren: 0.1,
      },
    }),
  };

  const contentVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3,
        ease: 'easeOut',
      },
    },
  };

  return (
    <motion.div
      className="bg-gray-100 flex items-center justify-center p-6 sm:p-10 rounded-2xl w-full my-6"
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, margin: '-100px' }}
      variants={cardVariants}
      custom={index}
      whileHover={{ boxShadow: '0 10px 25px rgba(0,0,0,0.05)' }}
      transition={{ duration: 0.3 }}
    >
      <div className="flex flex-col lg:flex-row lg:justify-between gap-6 w-full lg:max-w-[85vw]">
        <motion.div className="flex flex-col w-full lg:w-1/4 space-y-5" variants={contentVariants}>
          <motion.span
            className="text-3xl sm:text-4xl font-semibold"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            {number}
          </motion.span>
          <motion.hr
            className="border-gray-400 w-full"
            initial={{ scaleX: 0 }}
            animate={{ scaleX: 1 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          />
          <motion.div
            className="text-Midnight-Royal-Blue-02018B text-2xl sm:text-4xl font-semibold"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            {title}
          </motion.div>
        </motion.div>
        <motion.div className="w-full lg:w-2/3" variants={contentVariants}>
          {children}
        </motion.div>
      </div>
    </motion.div>
  );
};

// ===============================
// Main Component
// ===============================

const InnovationFormOriginal = ({
  submitInnovation,
  defaultValues,
  // onSuccess,
}: {
  submitInnovation: (data: FilteredFormData) => Promise<any>;
  defaultValues?: any;
  onSuccess: (data: any) => void;
  shouldShowReadMore?: boolean;
}) => {
  const {
    register,
    handleSubmit,
    control,
    reset,
    setError,
    formState: { errors, isSubmitting },
    setValue,
    clearErrors,
  } = useForm<FormValues>({
    defaultValues: defaultValues || {
      title: '',
      name: '',
      email: '',
      phoneNo: '',
      organizationName: '',
      description: '',
      interventionType: [],
      currentStageOfDevelopment: {
        id: '',
        value: '',
        label: '',
      },
      // targetedTo: {
      //   id: '',
      //   value: '',
      //   label: '',
      // },
      link: '',
      supportingDocument: [],
      bannerImage: undefined,
      declaration: false,
    },
    mode: 'onBlur',
    resolver: yupResolver(schema) as unknown as Resolver<FormValues>,
  });
  const [previewModalOpen, setPreviewModalOpen] = useState(false);
  const [successModalOpen, setSuccessModalOpen] = useState(false);
  const [formDataToSubmit, setFormDataToSubmit] = useState<FormDataProps | null>(null);
  const [isSubmittingForm, setIsSubmittingForm] = useState(false);
  const [toast, setToast] = useState<{
    message: string;
    type: 'success' | 'error';
  } | null>(null);
  const [options, setOptions] = useState<Options>({
    currentStageOfDevelopment: [],
    interventionType: [],
  });
  const [loadingFiles, setLoadingFiles] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<{ id: string; name: string }[]>(
    defaultValues?.supportingDocument || [],
  );
  const [uploadedBannerImage, setUploadedBannerImage] = useState<{
    id: string;
    name: string;
  } | null>(defaultValues?.bannerImage || null);
  const [showCropper, setShowCropper] = useState(false);
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [isUploadingBanner, setIsUploadingBanner] = useState(false);
  const cropperRef = useRef<any>(null);

  const isMounted = useRef(true);

  const fetchOpts = useCallback(async () => {
    if (!isMounted.current) return;
    try {
      const data = await fetchOptions();
      const groupedOptions = data?.docs?.reduce((acc: any, option: any) => {
        const field = option.fieldName;

        if (!acc[field]) acc[field] = [];
        acc[field].push({
          value: option.value,
          label: option.label,
          id: option.id,
          description: option.description || option.descriptionEditor,
          slug: option.slug,
          order: option.order,
        });
        return acc;
      }, {});
      if (isMounted.current) {
        setOptions({
          currentStageOfDevelopment: groupedOptions.currentStageOfDevelopment || [],
          interventionType: groupedOptions.interventionType || [],
        });
      }
    } catch (err) {
      console.error('Failed to fetch options:', err);
      if (isMounted.current) {
        setToast({ type: 'error', message: 'Failed to load form options.' });
      }
    }
  }, []);

  useEffect(() => {
    isMounted.current = true;
    fetchOpts();

    return () => {
      isMounted.current = false;
    };
  }, [fetchOpts]);

  // Effect to handle form initialization after options are loaded
  useEffect(() => {
    if (!isMounted.current || !options.currentStageOfDevelopment.length) return;

    if (defaultValues) {
      // Find the matching option objects for currentStageOfDevelopment and targetedTo
      const mappedInterventionType =
        defaultValues.interventionType?.map((v: any) => {
          // Find the complete option from options to get all properties
          const fullOption = options.interventionType.find((opt) => opt.id === v.id);
          return {
            value: v.value,
            id: v.id,
            label: v.label || fullOption?.label || v.value,
            order: fullOption?.order || 0,
            description: v.description || fullOption?.description,
            slug: v.slug || fullOption?.slug,
          };
        }) || [];

      // Find the matching option objects for currentStageOfDevelopment and targetedTo
      const currentStageObj = options.currentStageOfDevelopment.find(
        (opt) => opt.id === defaultValues.currentStageOfDevelopment,
      );

      reset({
        ...defaultValues,
        // Use objects with labels instead of just IDs
        currentStageOfDevelopment: currentStageObj || defaultValues.currentStageOfDevelopment,
        interventionType: mappedInterventionType,
        supportingDocument: defaultValues.supportingDocument || [],
      });

      setUploadedFiles(
        defaultValues?.supportingDocument?.map((doc: any) => ({
          id: doc?.id,
          name: doc?.filename,
        })) || [],
      );

      // Initialize banner image if it exists in defaultValues
      if (defaultValues?.bannerImage) {
        setUploadedBannerImage({
          id: defaultValues.bannerImage.id,
          name: defaultValues.bannerImage.name || defaultValues.bannerImage.filename,
        });
      }
    }
  }, [defaultValues, reset, options]);

  const updateInnovationVersion = useCallback(async (id: string, data: FormDataProps) => {
    try {
      const res = await fetch(`/api/project-versions/${id}/update`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });
      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData?.error || 'Failed to update innovation');
      }
      const responseData = await res.json();
      // setSubmissionResult(responseData);
      // setSuccessModalOpen(true);
      return responseData;
    } catch (error) {
      console.error('Error updating innovation:', error);
      setToast({
        type: 'error',
        message: 'Failed to update innovation.',
      });
      return data;
    }
  }, []);

  const handleFormSubmit = useCallback(async (data: FormDataProps | any) => {
    // Show preview modal first
    setFormDataToSubmit(data);
    setPreviewModalOpen(true);
  }, []);

  const handleFinalSubmit = useCallback(async () => {
    if (!formDataToSubmit) return;

    setIsSubmittingForm(true);

    const data = formDataToSubmit;
    const atlasInfo = JSON.parse(localStorage.getItem('atlasInfo') || '{}');
    try {
      const payload: any = {
        title: data.title,
        description: data.description,
        name: data.name,
        email: data.email,
        phoneNo: data.phoneNo,
        organizationName: data.organizationName,
        link: data.link,
        declaration: data.declaration,
        id: data.id,
        createdBy: atlasInfo?.userId,
        currentStageOfDevelopment:
          typeof data.currentStageOfDevelopment === 'object'
            ? (data.currentStageOfDevelopment as any).id
            : data.currentStageOfDevelopment,
        interventionType: data.interventionType?.map((v: any) => v?.id).filter(Boolean) || [],
        supportingDocument: uploadedFiles.map((file) => file.id),
        bannerImage: uploadedBannerImage ? uploadedBannerImage.id : null,
      };

      setPreviewModalOpen(false);

      if (data.id) {
        const response = await updateInnovationVersion(data.id, payload);
        if (response) {
          setSuccessModalOpen(true);
        } else {
          setToast({ type: 'error', message: 'Failed to update innovation.' });
        }
      } else {
        const response: any = await submitInnovation(payload);

        if (response?.code === 400 || response?.code === 500) {
          setToast({
            type: 'error',
            message: response?.message || 'Failed to submit innovation due to validation errors.',
          });

          if (response?.response && Array.isArray(response.response)) {
            response.response.forEach((error: any) => {
              if (error.path && error.message) {
                setError(error.path as keyof FormValues, {
                  type: 'manual',
                  message: error.message,
                });
              }
            });
          }

          setIsSubmittingForm(false);

          if (response?.response?.some((err: any) => err.path === 'title')) {
            setTimeout(() => {
              const titleField = document?.querySelector('[name="title"]');
              if (titleField) {
                titleField.scrollIntoView({ behavior: 'smooth', block: 'center' });
              }
            }, 100);
          }
        } else if (response) {
          setSuccessModalOpen(true);
        } else {
          setToast({ type: 'error', message: 'Failed to submit innovation.' });
        }
      }

      setIsSubmittingForm(false);
    } catch (err: any) {
      if (err instanceof yup.ValidationError) {
        clearErrors();
        err.inner.forEach((error: any) => {
          setError(error.path as keyof FormValues, {
            type: 'manual',
            message: error.message,
          });
        });
        setToast({
          type: 'error',
          message: 'Please correct the form errors.',
        });
      } else {
        console.error('Error submitting form:', err);
        setToast({ type: 'error', message: 'Failed to submit the form.' });
      }

      setIsSubmittingForm(false);
    }
  }, [
    submitInnovation,
    updateInnovationVersion,
    setError,
    uploadedFiles,
    uploadedBannerImage,
    clearErrors,
    formDataToSubmit,
  ]);

  const handleSuccessModalClose = useCallback(() => {
    setSuccessModalOpen(false);
    setIsSubmittingForm(false);
    if (!errors || Object.keys(errors).length === 0) {
      reset();
      setUploadedFiles([]);
      setUploadedBannerImage(null);
    }
  }, [reset, errors]);

  const handlePreviewModalClose = useCallback(() => {
    setPreviewModalOpen(false);
    setFormDataToSubmit(null);
  }, []);
  useEffect(() => {
    if (errors) {
      Object.values(errors).forEach((error: any) => {
        if (error?.message) {
          setToast({
            type: 'error',
            message: error.message,
          });
        }
      });
    }
  }, [errors]);

  const handleFileUploadChange = useCallback(
    async (files: File[]) => {
      if (!isMounted.current) return;
      setLoadingFiles(true);
      const newUploadedFiles: UploadedFile[] = [];
      let hasSizeError = false;

      for (const file of files) {
        if (file.size > MAX_FILE_SIZE_BYTES) {
          if (isMounted.current) {
            setToast({
              type: 'error',
              message: `File ${file.name} exceeds the maximum size of ${MAX_FILE_SIZE_MB}MB.`,
            });
            hasSizeError = true;
          }
          continue; // Skip this file and move to the next
        }

        const formData = new FormData();
        formData.append('file', file);

        try {
          const response = await fetch('/api/upload', {
            method: 'POST',
            body: formData,
          });

          if (!response.ok) {
            let errorMessage = `Failed to upload ${file.name}. Status: ${response.status}`;
            try {
              const errorData = await response.json();
              errorMessage += ` - ${errorData?.error || 'No details provided'}`;
            } catch (jsonError) {
              console.warn('Failed to parse error JSON:', jsonError);
            }
            if (isMounted.current) {
              setToast({ type: 'error', message: errorMessage });
            }
            continue; // Skip to the next file on upload failure
          }

          const data = await response.json();
          if (data?.doc?.id && (data?.doc?.filename || file.name)) {
            newUploadedFiles.push({ id: data.doc?.id, name: data.doc?.filename || file.name });
            // Optional: Success toast for each file
            // if (isMounted.current) {
            //   setToast({ type: 'success', message: `${file.name} uploaded successfully.` });
            // }
          } else {
            console.warn('File upload successful, but ID or filename is missing:', data, file.name);
            if (isMounted.current) {
              setToast({
                type: 'error',
                message: `File ${file.name} uploaded, but missing information.`,
              });
            }
          }
        } catch (fetchError) {
          console.error('Fetch error during upload:', fetchError);
          if (isMounted.current) {
            setToast({
              type: 'error',
              message: `Failed to upload ${file.name} due to a network error.`,
            });
          }
        }
      }

      if (isMounted.current && newUploadedFiles.length > 0 && !hasSizeError) {
        const updatedFiles = [...uploadedFiles, ...newUploadedFiles];
        setUploadedFiles(updatedFiles);
        setValue('supportingDocument', updatedFiles.map((f) => f.id) as any, {
          shouldValidate: true,
        });
      }

      if (isMounted.current) {
        setLoadingFiles(false);
      }
    },
    [setValue, uploadedFiles, setToast, isMounted],
  );

  const handleRemoveFile = useCallback(
    (index: number) => {
      const updatedFiles = uploadedFiles.filter((_, i) => i !== index);
      setUploadedFiles(updatedFiles);
      setValue('supportingDocument', updatedFiles.map((f) => f.id) as any, {
        shouldValidate: true,
      });
    },
    [setValue, uploadedFiles],
  );

  const handleImageUpload = useCallback(
    async (file: File) => {
      if (!isMounted.current) return;
      setIsUploadingBanner(true);

      if (file.size > MAX_FILE_SIZE_BYTES) {
        if (isMounted.current) {
          setToast({
            type: 'error',
            message: `File ${file.name} exceeds the maximum size of ${MAX_FILE_SIZE_MB}MB.`,
          });
        }
        setIsUploadingBanner(false);
        return;
      }

      const formData = new FormData();
      formData.append('file', file);

      try {
        const response = await fetch('/api/upload', {
          method: 'POST',
          body: formData,
        });

        if (!response.ok) {
          let errorMessage = `Failed to upload ${file.name}. Status: ${response.status}`;
          try {
            const errorData = await response.json();
            errorMessage += ` - ${errorData?.error || 'No details provided'}`;
          } catch (jsonError) {
            console.warn('Failed to parse error JSON:', jsonError);
          }
          if (isMounted.current) {
            setToast({ type: 'error', message: errorMessage });
          }
          return;
        }

        const data = await response.json();
        if (data?.doc?.id && (data?.doc?.filename || file.name)) {
          const bannerImage = { id: data.doc?.id, name: data.doc?.filename || file.name };
          setUploadedBannerImage(bannerImage);
          setValue('bannerImage', bannerImage);
          if (isMounted.current) {
            setToast({ type: 'success', message: 'Banner image uploaded successfully.' });
          }
        }
      } catch (error) {
        console.error('Error uploading banner image:', error);
        if (isMounted.current) {
          setToast({ type: 'error', message: 'Failed to upload banner image.' });
        }
      } finally {
        if (isMounted.current) {
          setIsUploadingBanner(false);
        }
      }
    },
    [setValue, setToast, isMounted],
  );

  const handleRemoveBannerImage = useCallback(() => {
    setUploadedBannerImage(null);
    setValue('bannerImage', undefined);
  }, [setValue]);

  const handleCrop = useCallback(() => {
    if (cropperRef.current?.cropper) {
      const canvas = cropperRef.current.cropper.getCroppedCanvas();
      canvas.toBlob((blob: Blob | null) => {
        if (blob) {
          const croppedFile = new File([blob], selectedImage?.name || 'cropped-image.jpg', {
            type: 'image/jpeg',
          });
          handleImageUpload(croppedFile);
          setShowCropper(false);
        }
      }, 'image/jpeg');
    }
  }, [selectedImage, handleImageUpload]);

  return (
    <form
      onSubmit={handleSubmit(handleFormSubmit)}
      className="max-w-[95vw] sm:max-w-[80vw] w-full scroll-smooth"
    >
      <AnimatePresence>
        {isSubmittingForm && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            <LoadingOverlay isVisible={isSubmittingForm} />
          </motion.div>
        )}
      </AnimatePresence>

      <AnimatePresence>
        {successModalOpen && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.3 }}
          >
            <SubmissionSuccessModal isOpen={successModalOpen} onClose={handleSuccessModalClose} />
          </motion.div>
        )}
      </AnimatePresence>

      <AnimatePresence>
        {previewModalOpen && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.3 }}
          >
            <InnovationPreviewModal
              isOpen={previewModalOpen}
              formData={formDataToSubmit}
              onClose={handlePreviewModalClose}
              onSubmit={handleFinalSubmit}
              uploadedFiles={uploadedFiles}
              uploadedBannerImage={uploadedBannerImage}
            />
          </motion.div>
        )}
      </AnimatePresence>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <FormCard number="01" title="Innovation Details" index={0}>
          <motion.div
            initial="hidden"
            animate="visible"
            variants={{
              hidden: { opacity: 0 },
              visible: {
                opacity: 1,
                transition: {
                  staggerChildren: 0.1,
                },
              },
            }}
          >
            <motion.div variants={{ hidden: { opacity: 0, y: 20 }, visible: { opacity: 1, y: 0 } }}>
              <Controller
                name="title"
                control={control}
                render={({ field }) => (
                  <TextArea
                    {...field}
                    required
                    placeholder="Enter a title of the innovation: Max length of 150 Characters"
                    className="bg-white w-full border-0"
                    label="Title/Name of Innovation"
                    errorMessage={errors.title?.message as string}
                  />
                )}
              />
            </motion.div>
            <motion.div variants={{ hidden: { opacity: 0, y: 20 }, visible: { opacity: 1, y: 0 } }}>
              <Controller
                name="organizationName"
                control={control}
                render={({ field }) => (
                  <TextArea
                    {...field}
                    required
                    placeholder="Enter the name of your company or organization that owns the innovation."
                    label="Name of the Company/Organization"
                    className="bg-white border-0"
                    errorMessage={errors.organizationName?.message as string}
                  />
                )}
              />
            </motion.div>
            <motion.div
              variants={{ hidden: { opacity: 0, y: 20 }, visible: { opacity: 1, y: 0 } }}
              className="mb-4"
            >
              <Controller
                name="description"
                control={control}
                render={({ field, fieldState }) => (
                  <>
                    <div className="text-md font-bold mb-2">
                      Detailed Description
                      <span className="text-red-500 ml-1">*</span>
                    </div>
                    <RichTextEditor value={field.value} onChange={field.onChange} />
                    {fieldState.error && (
                      <p className="text-red-500 text-sm mt-1">
                        {fieldState.error.message as string}
                      </p>
                    )}
                  </>
                )}
              />
            </motion.div>
            <motion.div variants={{ hidden: { opacity: 0, y: 20 }, visible: { opacity: 1, y: 0 } }}>
              <Controller
                name="link"
                control={control}
                render={({ field }) => (
                  <TextArea
                    {...field}
                    required
                    className="bg-white border-0"
                    label="Link for further details"
                    placeholder="Enter the URL for accessing further details of the innovation"
                    errorMessage={errors.link?.message as string}
                  />
                )}
              />
            </motion.div>
            <motion.div
              variants={{ hidden: { opacity: 0, y: 20 }, visible: { opacity: 1, y: 0 } }}
              className="mb-4"
            >
              <Controller
                name="bannerImage"
                control={control}
                render={() => (
                  <div className="flex flex-col gap-2">
                    <label className="text-md font-semibold block">Banner Image</label>
                    <div className="flex items-center gap-4">
                      <input
                        type="file"
                        accept="image/jpeg,image/png,image/webp"
                        onChange={(e) => {
                          const file = e.target.files?.[0];
                          if (file) {
                            setSelectedImage(file);
                            setShowCropper(true);
                          }
                        }}
                        className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                        disabled={isUploadingBanner}
                      />
                      {isUploadingBanner ? (
                        <div className="relative h-20 w-20 flex items-center justify-center bg-gray-100 rounded-md">
                          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                        </div>
                      ) : uploadedBannerImage ? (
                        <div className="flex items-center gap-2">
                          <div className="relative h-20 w-20">
                            <Image
                              src={
                                (process.env._AWS_END_POINT || '') + '/' + uploadedBannerImage.name
                              }
                              alt="Banner preview"
                              fill
                              className="rounded-md object-cover"
                            />
                          </div>
                          <motion.button
                            type="button"
                            onClick={handleRemoveBannerImage}
                            className="text-red-500 hover:text-red-700 ml-2"
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.9 }}
                          >
                            Remove
                          </motion.button>
                        </div>
                      ) : null}
                    </div>
                    {errors.bannerImage && (
                      <p className="text-sm text-red-500">
                        {errors.bannerImage.message?.toString()}
                      </p>
                    )}
                    <p className="text-xs text-gray-500">
                      Upload a square image (JPEG, PNG, or WebP) with maximum size of 5MB
                    </p>
                  </div>
                )}
              />
            </motion.div>
          </motion.div>
        </FormCard>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <FormCard number="02" title="Classification" index={1}>
          <motion.div
            initial="hidden"
            animate="visible"
            variants={{
              hidden: { opacity: 0 },
              visible: {
                opacity: 1,
                transition: {
                  staggerChildren: 0.1,
                },
              },
            }}
          >
            <motion.div variants={{ hidden: { opacity: 0, y: 20 }, visible: { opacity: 1, y: 0 } }}>
              <Controller
                name="interventionType"
                control={control}
                render={({ field }) => (
                  <MultiSelectDropdown
                    {...field}
                    required
                    label="Categories/ Keywords"
                    placeholder="Type in a few letters to search the list of categories and select them. Select as many keywords as relevant"
                    className="border-0"
                    options={options.interventionType}
                    errorMessage={errors.interventionType?.message as string}
                    shouldShowReadMore={true}
                  />
                )}
              />
            </motion.div>
            <motion.div variants={{ hidden: { opacity: 0, y: 20 }, visible: { opacity: 1, y: 0 } }}>
              <Controller
                name="currentStageOfDevelopment"
                control={control}
                render={({ field }) => (
                  <SingleSelect
                    {...field}
                    required
                    className="bg-white border-0"
                    label="Current Stage of Development"
                    placeholder="Select from the most appropriate stage of maturity in relation to your observation"
                    options={options.currentStageOfDevelopment as any}
                    errorMessage={errors.currentStageOfDevelopment?.message as string}
                    value={field.value?.id || ''}
                  />
                )}
              />
            </motion.div>
            <motion.div variants={{ hidden: { opacity: 0, y: 20 }, visible: { opacity: 1, y: 0 } }}>
              <Controller
                name="supportingDocument"
                control={control}
                render={({ field }) => (
                  <>
                    <FileUpload
                      label="Supporting Documents"
                      name={field.name}
                      loading={loadingFiles}
                      value={uploadedFiles}
                      onChange={handleFileUploadChange as any}
                      onRemove={handleRemoveFile}
                      required
                    />
                    <h6 className="mt-5">
                      Only PDF format allowed. Max file size: {MAX_FILE_SIZE_MB} MB. Multiple files
                      can be uploaded.
                    </h6>
                    {errors.supportingDocument && (
                      <p className="text-red-500 text-sm mt-1">
                        {errors.supportingDocument.message as string}
                      </p>
                    )}
                  </>
                )}
              />
            </motion.div>
          </motion.div>
        </FormCard>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <FormCard number="03" title="Contact Details" index={2}>
          <motion.div
            initial="hidden"
            animate="visible"
            variants={{
              hidden: { opacity: 0 },
              visible: {
                opacity: 1,
                transition: {
                  staggerChildren: 0.1,
                },
              },
            }}
          >
            <motion.div variants={{ hidden: { opacity: 0, y: 20 }, visible: { opacity: 1, y: 0 } }}>
              <Controller
                name="name"
                control={control}
                render={({ field, fieldState }) => (
                  <TextInput
                    {...field}
                    className="bg-white border-none border-0"
                    label="Contact Person Name"
                    placeholder="Enter your full name"
                    errorMessage={errors.name?.message as string}
                    required
                    touched={!!fieldState.error}
                  />
                )}
              />
            </motion.div>
            <motion.div variants={{ hidden: { opacity: 0, y: 20 }, visible: { opacity: 1, y: 0 } }}>
              <Controller
                name="email"
                control={control}
                render={({ field, fieldState }) => (
                  <TextInput
                    className="bg-white border-none border-0"
                    {...field}
                    label="Contact Person Email Address"
                    placeholder="Enter your email address"
                    touched={!!fieldState.error}
                    errorMessage={errors.email?.message as string}
                    required
                  />
                )}
              />
            </motion.div>

            <motion.div variants={{ hidden: { opacity: 0, y: 20 }, visible: { opacity: 1, y: 0 } }}>
              <Controller
                name="phoneNo"
                control={control}
                render={({ field: { onChange, value } }) => (
                  <>
                    <p className="text-md font-bold mt-1">
                      Contact Person Phone No.
                      <span className="text-red-500 ml-1">*</span>
                    </p>
                    <PhoneInput
                      defaultCountry="IN"
                      international
                      placeholder="Enter phone number"
                      value={value?.includes('+') ? value : ''}
                      onChange={(val) => onChange(val || '')}
                      className="border-gray-400 text-md bg-white !h-[50px] rounded-lg px-3 py-2 w-full [&>input]:focus:outline-none [&>input]:focus:ring-0 [&>input]:focus:border-transparent shadow-sm"
                    />
                  </>
                )}
              />
              {errors.phoneNo && (
                <p className="text-red-500 text-sm mt-1">{errors.phoneNo.message as string}</p>
              )}
            </motion.div>
          </motion.div>
        </FormCard>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
      >
        <FormCard number="04" title="Acknowledgements" index={3}>
          <motion.div
            initial="hidden"
            animate="visible"
            variants={{
              hidden: { opacity: 0 },
              visible: {
                opacity: 1,
                transition: {
                  staggerChildren: 0.1,
                },
              },
            }}
          >
            <motion.div
              className="flex flex-col w-full justify-between"
              variants={{ hidden: { opacity: 0, y: 20 }, visible: { opacity: 1, y: 0 } }}
            >
              <div className="flex items-start w-full space-x-2">
                <motion.input
                  className="cursor-pointer mt-1"
                  type="checkbox"
                  {...register('declaration')}
                  whileTap={{ scale: 1.2 }}
                />
                <motion.label
                  className="text-sm w-full"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.2 }}
                >
                  I confirm that the information provided is accurate and complete, and I agree to
                  the{' '}
                  <a
                    href="/how-to-submit/terms-of-use"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-500"
                  >
                    Terms of Use
                  </a>
                </motion.label>
              </div>
              {errors.declaration && (
                <motion.p
                  className="text-red-500 text-sm mt-1"
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                >
                  {errors.declaration.message as string}
                </motion.p>
              )}
              <motion.div>
                <Button
                  className="w-full mt-4"
                  size="sm"
                  variant="gradient"
                  type="submit"
                  title={isSubmitting ? `${en.SUBMITTING}` : `${en.CONTINUE}`}
                />
              </motion.div>
            </motion.div>
          </motion.div>
        </FormCard>
      </motion.div>

      {/* Image Cropper Modal */}
      {showCropper && selectedImage && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg p-4 max-w-2xl w-full">
            <h3 className="text-lg font-semibold mb-4">Crop Image</h3>
            <div className="h-[400px] w-full">
              <Cropper
                ref={cropperRef}
                src={URL.createObjectURL(selectedImage)}
                style={{ height: '100%', width: '100%' }}
                aspectRatio={1}
                guides={true}
              />
            </div>
            <div className="flex justify-end gap-4 mt-4">
              <Button
                variant="outline"
                onClick={() => setShowCropper(false)}
                title="Cancel"
                disabled={isUploadingBanner}
              />
              <Button
                variant="gradient"
                onClick={handleCrop}
                title={isUploadingBanner ? 'Uploading...' : 'Crop & Upload'}
                disabled={isUploadingBanner}
              />
            </div>
          </div>
        </div>
      )}

      <AnimatePresence>
        {toast && (
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 50 }}
            transition={{ duration: 0.3 }}
          >
            <Toast
              message={toast.message as string}
              type={toast.type}
              onClose={() => setToast(null)}
            />
          </motion.div>
        )}
      </AnimatePresence>
    </form>
  );
};

export default InnovationFormOriginal;
