'use client';

import React, { useEffect, useState } from 'react';
import { getDocument } from '../../utils/clientUtils';

interface PageWrapperProps {
  children: React.ReactNode;
}

const PageWrapper: React.FC<PageWrapperProps> = ({ children }) => {
  const [minHeight, setMinHeight] = useState('100vh');

  useEffect(() => {
    const doc = getDocument();
    if (!doc) return;
    // Function to update the min-height based on viewport and footer
    const updateMinHeight = () => {
      const footer = doc?.querySelector('footer');
      const footerHeight = footer ? footer.offsetHeight : 0;
      const navbarHeight = 60; // Approximate navbar height

      // Set min-height to ensure content pushes footer down
      setMinHeight(`calc(100vh - ${footerHeight + navbarHeight}px)`);
    };

    // Update on mount and window resize
    updateMinHeight();
    doc?.addEventListener('resize', updateMinHeight);

    // Cleanup
    return () => {
      doc?.removeEventListener('resize', updateMinHeight);
    };
  }, []);

  return (
    <div className="page-content" style={{ minHeight }}>
      {children}
    </div>
  );
};

export default PageWrapper;
