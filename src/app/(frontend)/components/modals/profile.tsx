'use client';
import Image from 'next/image';
import React, { useEffect, useRef, useState } from 'react';
import * as yup from 'yup';
import { API_ENDPOINTS } from '../../constants/routes';
import { getDocument, getWindow } from '../../utils/clientUtils';
import { TextInput } from '../Forms/InputComponent';
import Button from '../UI/Button';

const signupSchema = yup.object().shape({
  name: yup.string().required('Name is required').min(1, 'Name is too short'),
});

type ToastType = 'success' | 'error' | 'info';

type AtlasInfoType = { email?: string; name?: string; userId?: string };
type Toast = {
  message: string;
  type: ToastType;
} | null;

type ProfileModalProps = {
  isOpen: boolean;
  atlasInfo: AtlasInfoType;
  onClose: () => void;
  setToast: (toast: Toast) => void;
};

export const ProfileModal: React.FC<ProfileModalProps> = ({
  isOpen,
  onClose,
  setToast,
  atlasInfo,
}) => {
  const [loadingState, setLoadingState] = useState({ submit: false, logout: false });
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [touched, setTouched] = useState<{ [key: string]: boolean }>({});
  const [formData, setFormData] = useState<AtlasInfoType>({
    name: atlasInfo?.name || '',
    email: atlasInfo?.email || '',
  });
  const modalContentRef = useRef<HTMLDivElement>(null);

  // Handle click outside to close modal
  useEffect(() => {
    const doc = getDocument();
    if (!doc) return;

    const handleClickOutside = (event: MouseEvent) => {
      if (modalContentRef.current && !modalContentRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      doc?.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      doc?.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  const handleChange = (key: keyof AtlasInfoType, value: string) => {
    setFormData((prev) => ({ ...prev, [key]: value }));
  };

  const handleBlur = async (key: keyof AtlasInfoType) => {
    setTouched((prev) => ({ ...prev, [key]: true }));
    try {
      await signupSchema.validateAt(key, formData);
      setErrors((prev) => ({ ...prev, [key]: '' }));
    } catch (error: any) {
      setErrors((prev) => ({ ...prev, [key]: error.message }));
    }
  };

  const handleLogout = async () => {
    setLoadingState((prev) => ({ ...prev, logout: true }));
    try {
      await fetch(API_ENDPOINTS.LOGOUT, {
        method: 'POST',
        credentials: 'include',
      });
      localStorage.removeItem('atlasInfo');
      setToast({
        message: 'You have been logged out successfully',
        type: 'success',
      });
      onClose();

      const window = getWindow();
      if (!window) return null;
      window?.location.reload();
    } catch (err) {
      setToast({ message: 'Logout failed!', type: 'error' });
    } finally {
      setLoadingState((prev) => ({ ...prev, logout: false }));
    }
  };

  // Handle Enter key press to submit the form
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !loadingState.submit) {
      e.preventDefault();
      handleSubmit();
    }
  };

  const handleSubmit = async () => {
    setLoadingState((prev) => ({ ...prev, submit: true }));

    try {
      await signupSchema.validate(formData, { abortEarly: false });
      setErrors({});

      const response = await fetch(`/api/appUsers/${atlasInfo?.userId}`, {
        method: 'PATCH',
        credentials: 'include',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: formData?.name }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Something went wrong!');
      }

      const updatedUser = {
        email: data?.doc?.email,
        name: data?.doc?.name,
        userId: data?.doc?.id,
      };

      localStorage.setItem('atlasInfo', JSON.stringify(updatedUser));
      setToast({
        message: 'Profile updated successfully!',
        type: 'success',
      });
      onClose();
    } catch (err: any) {
      if (err.name === 'ValidationError') {
        const errMap: { [key: string]: string } = {};
        err.inner.forEach((e: any) => {
          errMap[e.path] = e.message;
        });
        setErrors(errMap);
        setToast({ message: 'Please fix the errors.', type: 'error' });
      } else {
        setToast({
          message: err.message || 'Something went wrong!',
          type: 'error',
        });
      }
    } finally {
      setLoadingState((prev) => ({ ...prev, submit: false }));
    }
  };

  if (!isOpen) return null;

  return (
    <div
      id="login-popup"
      tabIndex={-1}
      className="bg-black/50 backdrop-blur-md overflow-y-auto overflow-x-hidden fixed inset-0 items-center justify-center flex z-600"
    >
      <div
        ref={modalContentRef}
        className="bg-white rounded-xl p-6 w-[90%] max-w-md relative shadow-lg"
      >
        <button
          onClick={onClose}
          className="absolute top-3 right-3 text-gray-500 hover:text-red-500"
        >
          ✖
        </button>

        <div className="flex flex-col items-center mb-6">
          <div className="w-20 h-20 rounded-full bg-gray-100 overflow-hidden border border-gray-300">
            <Image
              src="/images/icons/profile.svg"
              alt="Profile Avatar"
              width={80}
              height={80}
              className="object-cover w-full h-full"
            />
          </div>
          <h2 className="text-xl font-semibold mt-4">Edit Profile</h2>
        </div>

        <div className="space-y-4">
          <TextInput
            label="Name"
            name="name"
            touched={touched.name}
            placeholder="Your Name"
            value={formData.name || ''}
            onChange={(v) => handleChange('name', v)}
            onBlur={() => handleBlur('name')}
            onKeyDown={handleKeyDown}
            errorMessage={errors.name}
            required
          />
          <TextInput
            label="Email"
            name="email"
            touched={touched.email}
            placeholder="<EMAIL>"
            value={formData.email || ''}
            disabled
            onChange={(v) => handleChange('email', v)}
            onBlur={() => handleBlur('email')}
            onKeyDown={handleKeyDown}
            errorMessage={errors.email}
            required
          />

          <Button
            size="sm"
            variant="gradient"
            disabled={loadingState.submit}
            title={loadingState.submit ? 'Saving...' : 'Save Changes'}
            className="w-full"
            onClick={handleSubmit}
            rightIcon={
              <Image
                alt="arrow icon"
                src="/images/icons/darkArrowIcon.svg"
                width={20}
                height={20}
                className="w-5 h-5 bg-white p-[5px] rounded-full"
              />
            }
          />
          <Button
            size="sm"
            variant="gradient"
            disabled={loadingState.logout}
            title={loadingState.logout ? 'Logging out...' : 'Logout'}
            className="w-full"
            onClick={handleLogout}
          />
        </div>
      </div>
    </div>
  );
};
