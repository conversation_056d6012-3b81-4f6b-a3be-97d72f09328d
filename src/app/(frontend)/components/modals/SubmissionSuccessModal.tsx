'use client';

import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { getDocument } from '../../utils/clientUtils';
import Button from '../UI/Button';

interface SubmissionSuccessModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function SubmissionSuccessModal({ isOpen, onClose }: SubmissionSuccessModalProps) {
  const router = useRouter();
  const [animationComplete, setAnimationComplete] = useState(false);

  useEffect(() => {
    const doc = getDocument();
    if (!doc) return;
    if (isOpen) {
      doc?.body.classList.add('overflow-hidden');
      // Trigger the checkmark animation immediately
      setAnimationComplete(false);
      // Use requestAnimationFrame to ensure the animation resets and then starts
      requestAnimationFrame(() => {
        requestAnimationFrame(() => {
          setAnimationComplete(true);
        });
      });
    } else {
      doc?.body.classList.remove('overflow-hidden');
      setAnimationComplete(false);
    }

    return () => {
      doc?.body.classList.remove('overflow-hidden');
    };
  }, [isOpen]);

  const handleRedirect = () => {
    router.push('/my-innovations');
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div
      id="success-popup"
      tabIndex={-1}
      className="bg-black/50 backdrop-blur-md overflow-y-auto overflow-x-hidden fixed inset-0 items-center justify-center flex z-[100]"
    >
      <div className="relative w-full max-w-md rounded-3xl bg-white dark:bg-zinc-900 shadow-2xl p-8 transition-all animate-fadeIn">
        {/* Checkmark Animation */}
        <div className="flex justify-center mb-6">
          <div className="relative w-24 h-24">
            <svg className="w-full h-full" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
              {/* Circle background */}
              <circle
                cx="50"
                cy="50"
                r="45"
                fill="none"
                stroke="#E6F7EF"
                strokeWidth="8"
                className="transition-all duration-300"
              />

              {/* Animated circle */}
              <circle
                cx="50"
                cy="50"
                r="45"
                fill="none"
                stroke="#34D399"
                strokeWidth="8"
                strokeDasharray="283"
                strokeDashoffset={animationComplete ? '0' : '283'}
                className="transition-all duration-1000 ease-out"
              />

              {/* Checkmark */}
              <path
                d="M30 50 L45 65 L70 35"
                fill="none"
                stroke="#34D399"
                strokeWidth="8"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeDasharray="75"
                strokeDashoffset={animationComplete ? '0' : '75'}
                className="transition-all duration-500 ease-out"
                style={{ transitionDelay: '0.8s' }}
              />
            </svg>
          </div>
        </div>

        {/* Title */}
        <h2 className="text-2xl font-semibold text-center text-green-700 dark:text-white mb-4">
          Innovation Submitted!
        </h2>

        {/* Subtitle */}
        <p className="text-center text-lg text-gray-600 dark:text-gray-400 mb-2">
          Your innovation has been submitted successfully.
        </p>
        <p className="text-center text-lg text-gray-600 dark:text-gray-400 mb-6">
          It will be reviewed by our team and you will receive an email once it is approved.
        </p>

        {/* Action Button */}
        <div className="flex justify-center">
          <Button
            size="md"
            variant="gradient"
            type="button"
            onClick={handleRedirect}
            title="Go to My Innovations"
            className="w-full"
          />
        </div>
      </div>
    </div>
  );
}
