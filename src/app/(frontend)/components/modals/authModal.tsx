'use client';
import { AnimatePresence, motion } from 'framer-motion';
import Image from 'next/image';
import { useSearchParams } from 'next/navigation';
import React, { useEffect, useRef, useState } from 'react';
import * as yup from 'yup';
import { authApiResponse, ToastFunProps } from '../../types/data.types';
import { getDocument, getWindow } from '../../utils/clientUtils';
import { TextInput } from '../Forms/InputComponent';
import Button from '../UI/Button';

const loginSchema = yup.object().shape({
  email: yup
    .string()
    .email('Enter a valid email address.')
    .required('Email is required.')
    .matches(
      /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$/,
      'Enter a valid email format (e.g., <EMAIL>).',
    ),
  password: yup
    .string()
    .min(6, 'Password must be at least 6 characters long.')
    .required('Password is required.'),
});

const signupSchema = yup.object().shape({
  name: yup.string().required('Name is required'),
  email: yup
    .string()
    .email('Enter a valid email address.')
    .required('Email is required.')
    .matches(
      /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$/,
      'Enter a valid email format (e.g., <EMAIL>).',
    ),
  password: yup
    .string()
    .min(6, 'Password must be at least 6 characters long.')
    .required('Password is required.'),
});

const forgotPasswordSchema = yup.object().shape({
  email: yup
    .string()
    .email('Invalid email')
    .required('Email is required')
    .matches(/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$/, 'Invalid email'),
});

type AuthModalProps = {
  isOpen: boolean;
  onClose: () => void;
  setToast: (toast: ToastFunProps) => void;
};

export const AuthModal: React.FC<AuthModalProps> = ({ isOpen, onClose, setToast }) => {
  const searchParams = useSearchParams();
  const [isForm, setForm] = useState<'login' | 'register' | 'forgotPassword'>('login');
  const modalContentRef = useRef<HTMLDivElement>(null);

  // Handle click outside to close modal
  useEffect(() => {
    const doc = getDocument();
    if (!doc) return;

    const handleClickOutside = (event: MouseEvent) => {
      if (modalContentRef.current && !modalContentRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      doc?.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      doc?.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  useEffect(() => {
    if (searchParams.get('showSignup') === 'true') {
      setForm('register');
    }
  }, [searchParams]);
  const [isLoading, setLoading] = useState(false);
  const initialState = { name: '', email: '', password: '' };
  const [formData, setFormData] = useState(initialState);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [touched, setTouched] = useState<{ [key: string]: boolean }>({});
  const showToast = (message: string, type: 'success' | 'error' | 'info') => {
    setToast({ message, type });
  };

  const handleBlur = async (key: string) => {
    setTouched({ ...touched, [key]: true });
    try {
      await metaData[isForm].schema.validateAt(key, formData);
      setErrors((prev) => ({ ...prev, [key]: '' })); // Clear error if valid
    } catch (err) {
      if (err instanceof yup.ValidationError) {
        setErrors((prev) => ({ ...prev, [key]: err.message })); // Set error message
      }
    }
  };
  const handleChange = (key: string, value: string) => {
    setFormData({ ...formData, [key]: value });
  };

  // Handle Enter key press to submit the form
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !isLoading) {
      e.preventDefault();
      handleSubmit(metaData, isForm, formData);
    }
  };

  const isLogin = isForm === 'login';

  const metaData = {
    login: {
      url: '/api/appUsers/login',
      schema: loginSchema,
      btnName: 'Login',
      showInputText: ['email', 'password'],
      labelText: 'Login',
      successMessage: 'Login successful. Welcome back!',
      body: { email: formData.email, password: formData.password },
    },

    register: {
      url: '/api/appUsers',
      schema: signupSchema,
      btnName: 'Sign Up',
      showInputText: ['name', 'email', 'password'],
      labelText: 'Sign Up',
      successMessage:
        'Your account has been created successfully. Please check your email to verify your account.',
      body: formData,
    },

    forgotPassword: {
      url: '/api/appUsers/forgot-password',
      schema: forgotPasswordSchema,
      btnName: 'Reset Password',
      showInputText: ['email'],
      labelText: 'Forgot Password',
      successMessage: 'A password reset link has been sent to your email. Please check your inbox.',
      body: { email: formData.email },
    },
  };

  // Constants
  const HTTP_STATUS_UNAUTHORIZED = 401;
  const HTTP_STATUS_BAD_REQUEST = 400;
  const TOAST_TYPE_ERROR = 'error';
  const TOAST_TYPE_SUCCESS = 'success';
  interface FormData {
    [key: string]: any;
  }
  interface MetaData {
    [key: string]: {
      schema: yup.ObjectSchema<any>;
      url: string;
      body: any;
      successMessage?: string;
    };
  }

  // Helper Functions
  const handleApiErrors = (
    errors: authApiResponse['errors'],
    setTouched: (touched: { [key: string]: boolean }) => void,
    setErrors: (errors: { [key: string]: string }) => void,
  ) => {
    if (errors && errors.length > 0) {
      errors[0].data?.errors?.forEach((error) => {
        setTouched({ [error.path]: true });
        setErrors({ [error.path]: error.message });
      });
    }
  };

  const handleSubmit = async (metaData: MetaData, isForm: string, formData: FormData) => {
    try {
      // Clear previous errors
      setErrors({});

      // Validate form data
      await metaData[isForm]?.schema.validate(formData, { abortEarly: false });

      setLoading(true);

      // API Request
      const res = await fetch(metaData[isForm].url, {
        method: 'POST',
        credentials: 'include',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(metaData[isForm].body),
      });

      // Parse response
      const data: authApiResponse = await res.json();
      if (!res.ok) {
        if (res.status === HTTP_STATUS_UNAUTHORIZED) {
          showToast(data.errors?.[0]?.message || 'Invalid email or password', TOAST_TYPE_ERROR);
        } else if (res.status === HTTP_STATUS_BAD_REQUEST && data.errors) {
          handleApiErrors(data.errors, setTouched, setErrors);
        } else {
          showToast(
            data?.message ||
              data?.errors?.[0]?.message ||
              'Something went wrong. Please try again.',
            TOAST_TYPE_ERROR,
          );
          setErrors({ general: 'Something went wrong. Please try again.' });
        }
        return;
      }
      const isUserCreated = data?.user || data?.doc;
      // Show success message
      if (isUserCreated?.id || data?.message == 'Success') {
        if (isForm == 'login') {
          const userInfo = JSON.stringify({
            email: isUserCreated?.email,
            name: isUserCreated?.name,
            userId: isUserCreated?.id,
          });
          localStorage.setItem('atlasInfo', userInfo);
        }
        showToast(metaData[isForm].successMessage || 'Success', TOAST_TYPE_SUCCESS);
        setFormData(initialState);
        onClose();

        const window = getWindow();
        if (!window) return null;
        setTimeout(() => {
          window?.location.reload();
        }, 3000);
      }
    } catch (err) {
      if (err instanceof yup.ValidationError) {
        // Handle validation errors
        const newErrors: { [key: string]: string } = {};
        err.inner.forEach((error) => {
          if (error.path) newErrors[error.path] = error.message;
        });
        setErrors(newErrors);
      } else {
        // Catch unexpected errors
        showToast('An unexpected error occurred. Please try again.', TOAST_TYPE_ERROR);
        setErrors({
          general: 'An unexpected error occurred. Please try again.',
        });
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          id="login-popup"
          tabIndex={-1}
          className="bg-black/50 backdrop-blur-md overflow-y-auto overflow-x-hidden fixed inset-0 items-center justify-center flex z-550"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
        >
          <motion.div
            ref={modalContentRef}
            className="bg-white p-6 rounded-lg w-96 relative"
            initial={{ scale: 0.9, y: 20, opacity: 0 }}
            animate={{ scale: 1, y: 0, opacity: 1 }}
            exit={{ scale: 0.9, y: 20, opacity: 0 }}
            transition={{
              type: 'spring',
              damping: 25,
              stiffness: 300,
              duration: 0.3,
            }}
          >
            <motion.button
              onClick={onClose}
              className="absolute top-3 right-3 text-gray-600 font-bold cursor-pointer"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              ✖
            </motion.button>

            <motion.h2
              className="text-xl font-semibold mb-5"
              initial={{ y: -10, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.1 }}
            >
              {metaData[isForm].labelText}
            </motion.h2>

            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.2 }}
            >
              <AnimatePresence mode="wait">
                <motion.div
                  key={isForm}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.3 }}
                >
                  {metaData[isForm].showInputText.includes('name') && (
                    <motion.div
                      className="mb-4"
                      initial={{ y: 10, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      transition={{ delay: 0.1 }}
                    >
                      <TextInput
                        label="Name"
                        name="name"
                        touched={touched.name}
                        placeholder="Enter your name"
                        value={formData.name}
                        onChange={(v) => handleChange('name', v)}
                        onBlur={() => handleBlur('name')}
                        onKeyDown={handleKeyDown}
                        errorMessage={errors.name}
                        required
                      />
                    </motion.div>
                  )}

                  {metaData[isForm].showInputText.includes('email') && (
                    <motion.div
                      className="mb-4"
                      initial={{ y: 10, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      transition={{ delay: 0.2 }}
                    >
                      <TextInput
                        label="Applicant email address"
                        name="email"
                        touched={touched.email}
                        placeholder="Enter your email"
                        value={formData.email}
                        onBlur={() => handleBlur('email')}
                        onChange={(v) => handleChange('email', v)}
                        onKeyDown={handleKeyDown}
                        errorMessage={errors.email}
                        required
                      />
                    </motion.div>
                  )}

                  {metaData[isForm].showInputText.includes('password') && (
                    <motion.div
                      className="mb-4"
                      initial={{ y: 10, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      transition={{ delay: 0.3 }}
                    >
                      <TextInput
                        label="Password"
                        name="password"
                        type="password"
                        touched={touched.password}
                        placeholder="Enter your Password"
                        value={formData.password}
                        onBlur={() => handleBlur('password')}
                        onChange={(v) => handleChange('password', v)}
                        onKeyDown={handleKeyDown}
                        errorMessage={errors.password}
                        required
                      />
                    </motion.div>
                  )}
                </motion.div>
              </AnimatePresence>

              {isLogin && (
                <motion.p
                  className="mt-4 text-sm mb-2"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.4 }}
                >
                  Forgot your password?
                  <motion.button
                    onClick={() => {
                      setFormData(initialState);
                      setErrors({});
                      setForm('forgotPassword');
                    }}
                    className="text-blue-400 ml-1 font-medium cursor-pointer"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    Restore access now
                  </motion.button>
                </motion.p>
              )}

              <motion.div
                initial={{ y: 10, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.5 }}
              >
                <Button
                  size="sm"
                  variant="gradient"
                  disabled={isLoading}
                  title={(isLoading && 'Loading...') || metaData[isForm].btnName}
                  className="mt-3 w-full"
                  onClick={() => handleSubmit(metaData, isForm, formData)}
                  rightIcon={
                    <Image
                      alt="arrowIcon of Innovations"
                      className="w-5 h-5 bg-white p-[5px] rounded-full"
                      height={20}
                      width={20}
                      src={'/images/icons/darkArrowIcon.svg'}
                    />
                  }
                />
              </motion.div>
            </motion.div>

            <motion.p
              className="mt-4 text-sm text-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.6 }}
            >
              {isLogin ? "Don't have an account? " : 'Already have an account? '}
              <motion.button
                onClick={() => {
                  setFormData(initialState);
                  setErrors({});
                  setForm((prev) => (prev === 'register' ? 'login' : 'register'));
                }}
                className="text-blue-600 font-semibold"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                {isLogin ? 'Sign Up' : 'Login'}
              </motion.button>
            </motion.p>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
