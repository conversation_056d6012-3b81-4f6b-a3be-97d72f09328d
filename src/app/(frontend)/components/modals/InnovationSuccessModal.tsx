'use client';

import { useEffect } from 'react';
import { getDocument } from '../../utils/clientUtils';
import Button from '../UI/Button';

interface SuccessModalProps {
  isOpen: boolean;
  isUpdated: boolean;
  onClose: () => void;
  onCheckPreview: () => void;
}

export default function SuccessModal({
  isOpen,
  onClose,
  isUpdated,
  onCheckPreview,
}: SuccessModalProps) {
  // const router = useRouter();

  // const handlePreview = () => {
  //   router.push(`/browse-innovations?myInnovations=${encodeURIComponent('true')}`); // Change route if needed
  //   onClose();
  // };

  useEffect(() => {
    const doc = getDocument();
    if (!doc) return;
    if (isOpen) {
      doc?.body.classList.add('overflow-hidden');
    } else {
      doc?.body.classList.remove('overflow-hidden');
    }
    return () => {
      doc?.body.classList.remove('overflow-hidden');
    };
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div
      id="login-popup"
      tabIndex={-1}
      className="bg-black/50 backdrop-blur-md overflow-y-auto overflow-x-hidden fixed inset-0 items-center justify-center flex z-[100]"
    >
      <div className="relative w-full max-w-md rounded-3xl bg-white dark:bg-zinc-900 shadow-2xl p-8 transition-all">
        {/* Checkmark Icon */}

        {/* Title */}
        <h2 className="text-xl font-semibold text-center text-green-700 dark:text-white mb-2">
          Success!
        </h2>

        {/* Subtitle */}
        <p className="text-center text-lg text-gray-600 dark:text-gray-400">
          {isUpdated
            ? 'Your form was Updated successfully.'
            : 'Your form was submitted successfully.'}
        </p>
        <p className="text-center text-lg text-gray-600 dark:text-gray-400 mb-6">
          {'You can now preview your innovation details.'}
        </p>

        {/* Action Buttons */}
        <div className="flex justify-center gap-4">
          <Button
            size="md"
            variant="outline"
            type="button"
            onClick={onClose}
            title="Cancel"
            className="w-full"
          />
          <Button
            size="md"
            variant="gradient"
            type="button"
            onClick={onCheckPreview}
            title="Check Preview"
            className="w-full"
          />
        </div>
      </div>
    </div>
  );
}
