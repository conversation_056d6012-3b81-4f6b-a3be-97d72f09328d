'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import { Suspense, useEffect, useState } from 'react';
import { createPortal } from 'react-dom';
import { AtlasInfo, ToastFunProps } from '../../types/data.types';
import { getDocument, getWindow } from '../../utils/clientUtils';
import Toast from '../UI/Toast';
import { AuthModal } from './authModal';
import { ProfileModal } from './profile';

export const AuthModalPortal = () => {
  const router = useRouter();
  const searchParams = useSearchParams();

  const isLoginPopup = searchParams.get('authModal') === 'true';
  const isProfilePopup = searchParams.get('profile-modal') === 'true';
  const params = new URLSearchParams(searchParams.toString());

  const [mounted, setMounted] = useState(false);
  const [atlasInfo, setAtlasInfo] = useState<AtlasInfo>({});
  const [toast, setToast] = useState<ToastFunProps | null>(null);

  useEffect(() => {
    setMounted(true);

    // Safely access localStorage
    const win = getWindow();
    if (win) {
      try {
        const storedInfo = win.localStorage.getItem('atlasInfo');
        if (storedInfo) {
          setAtlasInfo(JSON.parse(storedInfo));
        }
      } catch (error) {
        console.error('Failed to parse atlasInfo from localStorage', error);
      }
    }

    return () => {
      setMounted(false);
    };
  }, []);

  useEffect(() => {
    const doc = getDocument();
    if (!doc) return;

    if (isLoginPopup || isProfilePopup) {
      doc?.body.classList.add('overflow-hidden');
    } else {
      doc?.body.classList.remove('overflow-hidden');
    }

    return () => {
      doc?.body.classList.remove('overflow-hidden');
    };
  }, [isLoginPopup, isProfilePopup]);

  const closeModal = () => {
    const win = getWindow();
    if (!win) return;

    params.delete('authModal');
    params.delete('profile-modal');
    router.replace(`${win.location.pathname}?${params.toString()}`, {
      scroll: false,
    });
  };

  if (!Boolean(isProfilePopup && atlasInfo?.userId)) {
    params.delete('profile-modal');
  }

  if (!mounted) return null;

  const doc = getDocument();
  if (!doc) return null;

  return createPortal(
    <Suspense fallback={<div />}>
      <AuthModal
        isOpen={Boolean(isLoginPopup && !atlasInfo?.userId)}
        onClose={closeModal}
        setToast={setToast}
      />
      <ProfileModal
        isOpen={Boolean(isProfilePopup && atlasInfo?.userId)}
        onClose={closeModal}
        atlasInfo={atlasInfo}
        setToast={setToast}
      />
      {toast && (
        <Toast
          message={toast?.message || ''}
          type={toast?.type || ''}
          onClose={() => setToast(null)}
        />
      )}
    </Suspense>,
    doc?.body,
  );
};
