'use client';

import { AnimatePresence, motion } from 'framer-motion';
import Image from 'next/image';
import { useEffect } from 'react';
import { getDocument } from '../../utils/clientUtils';
import { serializeRichText } from '../../utils/functions';
import InsightDetails from '../InovationDetails/InsightDetails';
import SummaryOfInnovation from '../InovationDetails/SummaryOfInovation';
import Button from '../UI/Button';

interface PreviewModalProps {
  isOpen: boolean;
  formData: any;
  onClose: () => void;
  onSubmit: () => void;
  uploadedFiles?: { id: string; name: string }[];
  uploadedBannerImage?: { id: string; name: string } | null;
}

export default function InnovationPreviewModal({
  isOpen,
  formData,
  onClose,
  onSubmit,
  uploadedFiles = [],
  uploadedBannerImage = null,
}: PreviewModalProps) {
  useEffect(() => {
    const doc = getDocument();
    if (!doc) return;
    if (isOpen) {
      doc?.body.classList.add('overflow-hidden');
    } else {
      doc?.body.classList.remove('overflow-hidden');
    }
    return () => {
      doc?.body.classList.remove('overflow-hidden');
    };
  }, [isOpen]);

  if (!formData) return null;

  // Format the description if it's HTML content
  const formattedDescription =
    typeof formData.description === 'string'
      ? formData.description
      : serializeRichText(formData.description);

  // Format the form data to ensure option names are displayed correctly
  const formattedData = {
    ...formData,
    // Ensure interventionType has the correct format with value property
    interventionType: formData.interventionType?.map((item: any) => {
      if (typeof item === 'object') {
        return {
          id: item.id,
          value: item.value,
        };
      }
      return { id: item, value: item };
    }),
    // Ensure currentStageOfDevelopment has the correct format
    currentStageOfDevelopment:
      typeof formData.currentStageOfDevelopment === 'object'
        ? {
            id: formData.currentStageOfDevelopment.id || formData.currentStageOfDevelopment.value,
            value:
              formData.currentStageOfDevelopment.label || formData.currentStageOfDevelopment.value,
          }
        : { id: formData.currentStageOfDevelopment, value: formData.currentStageOfDevelopment },
    // Format supportingDocument to include filename for display
    supportingDocument: Array.isArray(formData.supportingDocument)
      ? formData.supportingDocument.map((doc: any) => {
          // If it's already a properly formatted document object with url
          if (doc && typeof doc === 'object' && 'url' in doc) {
            return doc;
          }
          // If it's an ID string, try to find the corresponding file in uploadedFiles
          if (typeof doc === 'string' && uploadedFiles.length > 0) {
            const fileInfo = uploadedFiles.find((f) => f.id === doc);
            if (fileInfo) {
              return {
                id: fileInfo.id,
                filename: fileInfo.name,
                url: `/api/files/${fileInfo.id}`, // Construct a URL for the file
              };
            }
          }
          // If it's an object with id and name properties
          if (doc && typeof doc === 'object' && 'id' in doc) {
            return {
              id: doc?.id,
              filename: doc?.name || doc?.filename || 'Document',
              url: `/api/files/${doc?.id}`, // Construct a URL for the file
            };
          }
          // Fallback
          return {
            id: typeof doc === 'string' ? doc : doc?.id || 'unknown',
            filename: doc?.name || doc?.filename || 'Document',
            url: `/api/files/${typeof doc === 'string' ? doc : doc?.id || 'unknown'}`,
          };
        })
      : [],
    // Add banner image if available
    bannerImage: uploadedBannerImage
      ? {
          id: uploadedBannerImage.id,
          name: uploadedBannerImage.name,
        }
      : formData.bannerImage || null,
  };
  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          id="preview-popup"
          tabIndex={-1}
          className="bg-black/50 backdrop-blur-md overflow-y-auto overflow-x-hidden fixed inset-0 items-center justify-center flex z-[100]"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
        >
          <motion.div
            className="relative w-full min-w-[60vw] max-w-3xl rounded-3xl bg-white dark:bg-zinc-900 shadow-2xl p-8 transition-all max-h-[90vh] overflow-y-auto scrollbar-hide"
            initial={{ scale: 0.95, opacity: 0, y: 20 }}
            animate={{ scale: 1, opacity: 1, y: 0 }}
            exit={{ scale: 0.95, opacity: 0, y: 20 }}
            transition={{
              type: 'spring',
              damping: 25,
              stiffness: 300,
            }}
          >
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1, duration: 0.4 }}
            >
              {/* Glassmorphic Card */}
              <div
                className="p-4 shadow-xl rounded-3xl max-w-[95vw] sm:max-w-[80vw] mx-auto border border-white/80 text-white
                backdrop-blur-lg bg-gradient-to-r from-[#0F06D2]/50 via-[#85367A]/50 to-[#F86525]/50 bg-opacity-30 glass-shimmer"
              >
                <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
                  <div className="flex flex-col md:w-2/3">
                    <motion.h1
                      className="text-2xl sm:text-3xl md:text-4xl font-bold mb-4"
                      initial={{ opacity: 0, y: 5 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.2 }}
                    >
                      {formData?.title}
                    </motion.h1>

                    <motion.div
                      className="inline-block bg-orange-500 text-white px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap"
                      initial={{ opacity: 0, y: 5 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3 }}
                    >
                      {formData?.organizationName}
                    </motion.div>
                  </div>

                  {formattedData.bannerImage && (
                    <motion.div
                      className="relative h-40 w-40 md:h-52 md:w-52 lg:h-64 lg:w-64 rounded-lg overflow-hidden flex-shrink-0"
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: 0.1, duration: 0.4 }}
                    >
                      {!uploadedBannerImage ? (
                        <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                          <span className="text-gray-500">Banner Image Preview</span>
                        </div>
                      ) : (
                        formattedData.bannerImage && (
                          <Image
                            src={
                              (process.env._AWS_END_POINT || '') + '/' + uploadedBannerImage?.name
                            }
                            alt="Innovation banner"
                            fill
                            className="object-cover"
                          />
                        )
                      )}
                    </motion.div>
                  )}
                </div>
              </div>
            </motion.div>

            <motion.div
              className="mx-auto max-w-[95vw] sm:max-w-[80vw] md:px-0 px-5"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.4, duration: 0.4 }}
            >
              <div className="mt-8">
                <div className="flex items-center justify-between pb-2 mb-6 relative after:content-[''] after:absolute after:bottom-0 after:left-0 md:after:w-1/2 after:w-[90%] after:h-[3px] after:bg-orange-500">
                  <h3 className="text-2xl font-bold text-blue-900">About The Innovation</h3>
                  <Image
                    src="/images/icons/about-svgrepo-com (1).svg"
                    width={35}
                    height={35}
                    alt="About"
                    className="absolute bottom-[-15px] md:left-[calc(50%)] left-[calc(90%)]"
                  />
                </div>
                <SummaryOfInnovation description={formattedDescription} />
              </div>

              <div className="mt-8">
                <div className="flex items-center justify-between pb-2 mb-6 relative after:content-[''] after:absolute after:bottom-0 after:left-0 md:after:w-1/2 after:w-[90%] after:h-[3px] after:bg-orange-500">
                  <h3 className="text-2xl font-bold text-blue-900">Categories</h3>
                  <Image
                    src="/images/icons/category-svgrepo-com (1).svg"
                    width={35}
                    height={35}
                    alt="Categories"
                    className="absolute bottom-[-15px] md:left-[calc(50%)] left-[calc(90%)]"
                  />
                </div>
                <InsightDetails innovationDetails={formattedData} />
              </div>

              {uploadedFiles && uploadedFiles.length > 0 && (
                <div className="mt-8">
                  <div className="flex items-center justify-between pb-2 mb-6 relative after:content-[''] after:absolute after:bottom-0 after:left-0 md:after:w-1/2 after:w-[90%] after:h-[3px] after:bg-orange-500">
                    <h3 className="text-2xl font-bold text-blue-900">Attachments</h3>
                    <Image
                      src="/images/icons/attachment-svgrepo-com.svg"
                      width={35}
                      height={35}
                      alt="Attachments"
                      className="absolute bottom-[-15px] md:left-[calc(50%)] left-[calc(90%)]"
                    />
                  </div>
                  <div className="flex flex-wrap gap-3">
                    {uploadedFiles.map((file) => (
                      <div
                        key={file.id}
                        className="flex items-center gap-2 bg-white px-3 py-2 rounded-lg shadow-sm hover:shadow-md transition-shadow"
                      >
                        <span className="text-red-500">📄</span>
                        <span className="text-sm font-medium text-gray-700">{file.name}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {formattedData.link && (
                <div className="mt-8 mb-8">
                  <div className="flex items-center justify-between pb-2 mb-6 relative after:content-[''] after:absolute after:bottom-0 after:left-0 md:after:w-1/2 after:w-[90%] after:h-[3px] after:bg-orange-500">
                    <h3 className="text-2xl font-bold text-blue-900">Read More</h3>
                    <Image
                      src="/images/icons/globe-with-arrow-svgrepo-com (1).svg"
                      width={35}
                      height={35}
                      alt="Read More"
                      className="absolute bottom-[-15px] md:left-[calc(50%)] left-[calc(90%)]"
                    />
                  </div>
                  <a
                    href={
                      formattedData.link.startsWith('http')
                        ? formattedData.link
                        : `https://${formattedData.link}`
                    }
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800 italic"
                  >
                    {formattedData.link}
                  </a>
                </div>
              )}
            </motion.div>

            <motion.div
              className="flex justify-center gap-4 mt-6 sticky bottom-0"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5, duration: 0.4 }}
            >
              <Button
                size="md"
                variant="solid"
                type="button"
                onClick={onClose}
                title="Keep Editing"
                className="w-full"
              />
              <Button
                size="md"
                variant="gradient"
                type="button"
                onClick={onSubmit}
                title="Submit"
                className="w-full"
              />
            </motion.div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
