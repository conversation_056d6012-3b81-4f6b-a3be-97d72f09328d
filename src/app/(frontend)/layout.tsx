import type { Metadata } from 'next';
import NextTopLoader from 'nextjs-toploader';
import React, { Suspense } from 'react';
import Footer from './components/Layout/Footer';
import { AuthModalPortal } from './components/modals/AuthModalPortal';
import './styles.css';

export const metadata: Metadata = {
  title: 'Atlas of Innovations',
  description: 'Welcome to India Innovation Summit - Pioneering solutions to End TB',
  metadataBase: new URL('https://www.atlasoftbinnovations.in'),
  icons: 'images/icons/Atlas-of-Innovators.png',
  keywords: [
    'India Innovation Summit',
    'TB Elimination Innovations',
    'TB Solutions India',
    'Innovation Summit India 2024',
    'Healthcare Innovations',
    'Tuberculosis Innovations',
    'End TB India',
    'TB Research and Development',
    'Innovative TB Solutions',
    'TB Health Summit 2024',
    'Global Health Innovations',
    'Health Policy Makers Summit',
    'Innovator Networking Summit',
    'Innovation Showcase TB',
    'Public Health Innovations',
    'TB Elimination Program',
    'TB Scientific Sessions India',
    'TB Innovations Summit',
  ],
  openGraph: {
    title: 'Atlas of Innovations',
    description: 'Welcome to India Innovation Summit - Pioneering solutions to End TB',
    url: 'https://www.atlasoftbinnovations.in',
    siteName: 'Atlas of Innovations',
    type: 'website',
  },
};

export default async function RootLayout(props: { children: React.ReactNode }) {
  const { children } = props;
  return (
    <html lang="en">
      <head>
        <link rel="preconnect" href={process.env.NEXT_PUBLIC_BACKEND_URL} />
        <link rel="dns-prefetch" href={process.env.NEXT_PUBLIC_BACKEND_URL} />
      </head>

      <body className="flex flex-col min-h-screen">
        <NextTopLoader
          color="linear-gradient(90deg, #0F06D2, #85367A, #F86525, #FFFFFF00)"
          initialPosition={0.08}
          crawlSpeed={120}
          height={6}
          crawl={true}
          showSpinner={true}
          easing="ease-in-out"
          speed={300}
          shadow="0 0 10px #0F06D2, 0 0 15px #85367A, 0 0 20px #F86525"
          zIndex={1600}
          showAtBottom={false}
        />
        <Suspense fallback={<div />}>
          <AuthModalPortal />
        </Suspense>
        <main className="flex-1">{children}</main>
        <Footer />
      </body>
    </html>
  );
}
