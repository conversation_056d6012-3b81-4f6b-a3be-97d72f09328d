// utils/browserHelpers.ts

// Utility to check if code is running in browser
export const isBrowser = (): boolean => typeof window !== 'undefined';

// Safe document access
export const getDocument = (): Document | null => {
  if (isBrowser()) {
    return document;
  }
  return null;
};

// Safe window access
export const getWindow = (): Window | null => {
  if (isBrowser()) {
    return window;
  }
  return null;
};
