// lib/metadata.ts
import type { Metadata } from 'next';

// const siteUrl = 'https://www.atlasoftbinnovations.in';
const siteName = 'Atlas of Innovations';
const defaultKeywords = [
  'India Innovation Summit',
  'TB Elimination Innovations',
  'TB Solutions India',
  'Innovation Summit India 2024',
  'Healthcare Innovations',
  'Tuberculosis Innovations',
  'End TB India',
  'TB Research and Development',
  'Innovative TB Solutions',
  'TB Health Summit 2024',
  'Global Health Innovations',
  'Health Policy Makers Summit',
  'Innovator Networking Summit',
  'Innovation Showcase TB',
  'Public Health Innovations',
  'TB Elimination Program',
  'TB Scientific Sessions India',
  'TB Innovations Summit',
];

export const getPageMeta = (pathname: string): Metadata => {
  switch (pathname) {
    case '/':
      return {
        title: siteName,
        description: 'Welcome to India Innovation Summit - Pioneering solutions to End TB',
        keywords: defaultKeywords,
      };
    case '/browse-innovations':
      return {
        title: 'Browse Innovations - Atlas of Innovations',
        description: 'Explore TB-related innovations submitted by changemakers across India.',
        keywords: ['TB Innovations', 'Browse TB Solutions', ...defaultKeywords],
      };
    case '/how-to-submit':
      return {
        title: 'Submit an Innovation - Atlas of Innovations',
        description:
          'Share your TB-related innovation to help accelerate impact across communities.',
        keywords: ['Submit Innovation TB', 'Innovation Submission', ...defaultKeywords],
      };
    case '/learn-more':
      return {
        title: 'Learn More - Atlas of Innovations',
        description:
          'Learn about the mission, vision, and impact of the India Innovation Summit for TB.',
        keywords: ['About TB Innovations', 'Innovation Overview', ...defaultKeywords],
      };
    case '/about':
      return {
        title: 'About Us - Atlas of Innovations',
        description:
          'Discover the story, values, and vision behind the Atlas of Innovations initiative.',
        keywords: ['About Atlas of Innovations', 'TB Innovation Background', ...defaultKeywords],
      };
    case '/contact-us':
      return {
        title: 'Contact Us - Atlas of Innovations',
        description: 'Reach out to us with questions, partnerships, or innovation opportunities.',
        keywords: ['Contact TB Summit', 'Atlas Support', ...defaultKeywords],
      };
    case '/faq':
      return {
        title: 'Frequently Asked Questions - Atlas of Innovations',
        description:
          'Find answers to common questions about submitting, browsing, or learning about TB innovations.',
        keywords: ['TB FAQ', 'Innovation Summit Questions', ...defaultKeywords],
      };
    case '/learn-more':
      return {
        title: 'Learn More - Atlas of Innovations',
        description:
          'Explore the core pillars of TB Elimination in India — Detect, Treat, Prevent, and Build. Discover how innovations align with the National Strategic Plan (NSP) to reduce TB cases, deaths, and economic burden by 2025.',
        keywords: [
          'TB Elimination Strategy',
          'DTBP Pillars TB',
          'TB Innovations India',
          'National Strategic Plan TB',
          'Public Health Innovations',
          ...defaultKeywords,
        ],
      };
    default:
      return {
        title: siteName,
        description: 'Empowering TB elimination through innovation.',
        keywords: defaultKeywords,
      };
  }
};
