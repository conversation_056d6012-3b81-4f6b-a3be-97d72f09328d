// src/utils/serializeRichText.ts
import { Descendant, Text } from 'slate';

type CustomText = Text & {
  bold?: boolean;
  italic?: boolean;
  underline?: boolean;
};

type CustomElement = {
  type?: string;
  children: (CustomText | CustomElement)[];
};

export const serializeRichText = (nodes: Descendant[]): string => {
  if (!nodes || !Array.isArray(nodes)) return '';

  const serializeNode = (node: CustomText | CustomElement): string => {
    if ('text' in node) {
      let text = node.text;
      if (node.bold) text = `<strong class="font-bold">${text}</strong>`;
      if (node.italic) text = `<em class="italic">${text}</em>`;
      if (node.underline) text = `<u class="underline">${text}</u>`;
      return text;
    }

    if ('children' in node) {
      const children = node.children.map((child) => serializeNode(child)).join('');
      switch (node.type) {
        case 'h1':
          return `<h1>${children}</h1>`;
        case 'h2':
          return `<h2>${children}</h2>`;
        case 'p':
          return `<p>${children}</p>`;
        case 'ul':
          return `<ul>${children}</ul>`;
        case 'ol':
          return `<ol>${children}</ol>`;
        case 'li':
          return `<li>${children}</li>`;
        default:
          return `${children}</br>`;
      }
    }

    return '';
  };

  return nodes.map((node) => serializeNode(node)).join('');
};

export const truncateText = (text: string, wordLimit: number) => {
  const words = text.split(' ');
  if (words.length > wordLimit) {
    return words.slice(0, wordLimit).join(' ') + '...';
  }
  return text;
};
