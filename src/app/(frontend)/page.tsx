import { cookies } from 'next/headers';
import { Suspense } from 'react';
import AtlasOfInnovations from './components/UI/HomeComponent';
import {
  FailedToLoadFallBack,
  NavBarUIFallBack,
  PageLoadingFallBack,
} from './components/UI/loadingFallBackComponents';
import { NavBarUI } from './components/UI/navBar';
import './styles.css';
import {
  fetchLandingPageUi,
  fetchTop4FeaturedInnovations,
  fetchTotalInnovations,
} from './utils/apiClient';
import { serializeRichText } from './utils/functions';

export default async function Home() {
  const cookieStore = await cookies();
  const [data, landingPage, totalInnovations] = await Promise.all([
    fetchTop4FeaturedInnovations(),
    fetchLandingPageUi(),
    fetchTotalInnovations(),
  ]);

  if (!landingPage) {
    return <FailedToLoadFallBack />;
  }
  const landingPageContent = {
    ...landingPage,
    heroSection: {
      heading: landingPage.heroSection.heading,
      description: serializeRichText(landingPage.heroSection.description),
      heroImage: landingPage.heroSection.heroImage,
      sliderImages: landingPage.heroSection.sliderImages?.map(
        ({ image }: { image: { filename: string } }) => image?.filename,
      ),
    },
  };
  return (
    <Suspense fallback={<PageLoadingFallBack pathname="/" />}>
      <Suspense fallback={<NavBarUIFallBack />}>
        <NavBarUI
          color="text-Pure-White-FFFFFF"
          pathname="/"
          isAuthenticated={Boolean(cookieStore.get('payload-token')?.value)}
        />
      </Suspense>
      <AtlasOfInnovations
        featuredInnovations={data}
        landingPageContent={landingPageContent}
        innovationCount={totalInnovations?.totalDocs}
      />
    </Suspense>
  );
}
