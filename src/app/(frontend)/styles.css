@import 'tailwindcss';
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Roboto:wght@400;500;700&display=swap');

@custom-variant dark (&:where(.dark, .dark *));

@theme {
  --color-Dark-Blue-00008B: #00008b;
  /* Dark Blue */
  --color-Bright-Blue-5555FF: #5555ff;
  /* Bright Blue */
  --color-Light-Blue-9999FF: #9999ff;
  /* Light Blue */
  --color-Near-Black-0A0A0A: #0a0a0a;
  /* Near Black */
  --color-Dark-Gray-808080: #808080;
  --color-Gray-888888: #888888;
  /* Dark Gray */
  --color-Light-Gray-C0C0C0: #c0c0c0;
  /* Light Gray */
  --color-Soft-White-F5F7F6: #f5f7f6;
  /* Soft White */
  --color-Pure-White-FFFFFF: #ffffff;
  /* Pure White */
  --color-Midnight-Royal-Blue-02018B: #02018b;
  /* Midnight Royal Blue */
  --color-Deep-Royal-Blue-07038D: #07038d;
  --color-Grayish-Black-7E7E7F: #7e7e7f;
  --color-Light-Gray-F6F6F6: #f6f6f6;
  --color-black-0B0B0E: #0b0b0e;
  --color-gray-BABABA: #bababa;

  /* Fonts */
  --font-body: 'Poppins', sans-serif;
  --font-heading: 'Roboto', sans-serif;
}

/* Apply Theme */

html,
body {
  height: 100%;
  margin: 0;
  padding: 0;
}

body {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

main {
  flex: 1 0 auto;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--font-body);
}

.backdrop-blur-sm {
  -webkit-backdrop-filter: blur(4px);
  backdrop-filter: blur(4px);
}

.backdrop-filter {
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness)
    var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate)
    var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate)
    var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast)
    var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert)
    var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

@keyframes moveStripes {
  from {
    background-position: 0 0;
  }

  to {
    background-position: -100px 0;
    /* Moves left smoothly */
  }
}

@keyframes ultraSoftWave {
  0% {
    background-position: 0 0;
  }

  50% {
    background-position: -10px 0;
    /* Extremely subtle movement */
  }

  100% {
    background-position: 0 0;
  }
}

.animate-stripes {
  animation: ultraSoftWave 15s ease-in-out infinite;
  /* Very slow and smooth */
}

.ql-editor {
  font-family: 'Poppins', sans-serif !important;
  font-size: 1rem !important;
  left: 0 !important;
  padding: 0 !important;
  margin: 0 !important;
}

@keyframes bg-move {
  0% {
    background-position:
      0% 50%,
      100% 50%;
  }

  50% {
    background-position:
      100% 50%,
      0% 50%;
  }

  100% {
    background-position:
      0% 50%,
      100% 50%;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

@keyframes reveal {
  0% {
    opacity: 0;
    transform: scale(1.1) translateY(10px);
    filter: blur(10px);
  }

  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
    filter: blur(0);
  }
}

@keyframes pulse-ring {
  0% {
    width: 20px;
    height: 20px;
    opacity: 0;
  }

  50% {
    opacity: 0.5;
  }

  100% {
    width: 80px;
    height: 80px;
    opacity: 0;
  }
}

@keyframes marquee {
  from {
    transform: translateX(0);
  }

  to {
    transform: translateX(-50%);
  }
}

.loader {
  border-top-color: #3498db;
  -webkit-animation: spinner 1.5s linear infinite;
  animation: spinner 1.5s linear infinite;
}

@-webkit-keyframes spinner {
  0% {
    -webkit-transform: rotate(0deg);
  }

  100% {
    -webkit-transform: rotate(360deg);
  }
}

@keyframes spinner {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@layer utilities {
  .animate-fade-in-up {
    animation: fadeInUp 3s ease-out forwards;
  }

  .animate-marquee {
    display: inline-block;
    animation: marquee 60s linear infinite;
  }

  .animate-gradient {
    background-size: 200% 200%;
    animation: gradientShift 5s ease infinite;
  }

  .animate-gradient-x {
    background-size: 200% 200%;
    animation: gradient 2s ease infinite;
  }

  .animate-reveal {
    animation: reveal 1.5s ease-out forwards;
  }

  .animate-fade-in {
    animation: fadeIn 1.5s ease-out forwards;
  }

  .animate-pulse-ring {
    position: absolute;
    border-radius: 9999px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation: pulse-ring 2s cubic-bezier(0.25, 0.46, 0.45, 0.94) infinite;
  }
}

@keyframes glassShimmer {
  0% {
    background-position: -200%;
    opacity: 1;
  }

  100% {
    background-position: 200%;
    opacity: 0;
    /* Fade out the effect */
  }
}

.glass-shimmer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    120deg,
    rgba(255, 255, 255, 0.1) 20%,
    rgba(255, 255, 255, 0.4) 50%,
    rgba(255, 255, 255, 0.1) 80%
  );
  background-size: 200% 100%;
  animation: glassShimmer 3s ease-in-out forwards;
  border-radius: inherit;
  pointer-events: none;
  /* Ensures it doesn't interfere with user interactions */
}

/* Add this to your global CSS file */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 7px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: #e2e2e2;
  border-radius: 50px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background-color: #f1f1f1;
}

.scrollbar-hide {
  -ms-overflow-style: none;
  /* IE and Edge */
  scrollbar-width: none;
  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
  /* Chrome, Safari, Opera */
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-in-out;
}

.calculated-height-page {
  min-height: calc(100vh - 350px);
}

/* Sticky footer styles */
.page-content {
  flex: 1 0 auto;
  width: 100%;
}

table {
  border-collapse: collapse;
  width: 100%;
}

td {
  border: 1px solid #ccc;
  padding: 8px;
}

.react-quill-container .ql-toolbar.ql-snow {
  position: sticky;
  top: 0;
  z-index: 5;
  background: white;
  border-bottom: 1px solid #e5e7eb;
}
.ql-editor {
  min-height: 50px;
  padding-top: 12px;
}
