'use client';

import CircularLoader from '@/app/(frontend)/components/UI/CircularLoader';
import { fetchOptions } from '@/app/(frontend)/utils/apiClient';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { useEffect, useState } from 'react';

interface Option {
  id: string;
  value: string;
  fieldName: string;
  description?: string;
}

export default function OptionDetailPage() {
  const { slug } = useParams();
  const [option, setOption] = useState<Option | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const getOptionBySlug = async () => {
      try {
        setLoading(true);
        const data = await fetchOptions();

        // Try to find the option that matches the slug
        const decodedSlug = decodeURIComponent(slug as string).toLowerCase();

        // First try to find by the slug field
        let foundOption = data.docs.find(
          (opt: any) => opt.slug && opt.slug.toLowerCase() === decodedSlug,
        );

        // If not found by slug field, fall back to the old method for backward compatibility
        if (!foundOption) {
          foundOption = data.docs.find((opt: any) => {
            if (!opt.description) return false;

            // Extract first few words from the description (without HTML tags)
            const strippedText = opt.description.replace(/<[^>]*>/g, '');
            const words = strippedText
              .split(/\s+/)
              .filter(Boolean)
              .slice(0, 5)
              .join('-')
              .toLowerCase();

            return (
              words === decodedSlug || words.includes(decodedSlug) || decodedSlug.includes(words)
            );
          });
        }

        if (foundOption) {
          setOption(foundOption);
        } else {
          setError('Option not found');
        }
      } catch (err) {
        console.error('Failed to fetch option details:', err);
        setError('Failed to load option details');
      } finally {
        setLoading(false);
      }
    };

    if (slug) {
      getOptionBySlug();
    }
  }, [slug]);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <CircularLoader />
      </div>
    );
  }

  if (error || !option) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          <p>{error || 'Option not found'}</p>
        </div>
      </div>
    );
  }

  // Map fieldName to a more readable format
  const getFieldLabel = (fieldName: string) => {
    const fieldMap: Record<string, string> = {
      currentStageOfDevelopment: 'Current Stage of Development',
      interventionType: 'Intervention Type',
    };

    return fieldMap[fieldName] || fieldName;
  };

  return (
    <div className="container mx-auto px-4 pt-30 pb-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold text-blue-900">Option Details</h1>
          <Link
            href="/"
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors"
          >
            Back to Home
          </Link>
        </div>
        <div className="h-1 w-full bg-gradient-to-r from-blue-500 to-purple-600 mt-2"></div>
      </div>

      {/* Content */}
      <div className="bg-white shadow-md rounded-lg overflow-hidden">
        <div className="p-6">
          <div className="flex items-center mb-4">
            <span className="inline-block bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full uppercase font-semibold tracking-wide">
              {getFieldLabel(option.fieldName)}
            </span>
          </div>

          <h2 className="text-2xl font-bold text-gray-900 mb-4">{option.value}</h2>

          <div className="prose max-w-none">
            {option.description ? (
              <div dangerouslySetInnerHTML={{ __html: option.description }} />
            ) : (
              <p className="text-gray-500 italic">No detailed description available.</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
