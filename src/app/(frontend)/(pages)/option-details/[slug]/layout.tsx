import { NavBarUIFallBack } from '@/app/(frontend)/components/UI/loadingFallBackComponents';
import { NavBarUI } from '@/app/(frontend)/components/UI/navBar';
import { Metadata } from 'next';
import { cookies } from 'next/headers';
import { Suspense } from 'react';

// Generate metadata for SEO
export const generateMetadata = (): Metadata => {
  return {
    title: 'Option Details | Atlas of Innovations',
    description:
      'Detailed information about form options and their descriptions in the Atlas of Innovations platform.',
    openGraph: {
      title: 'Option Details | Atlas of Innovations',
      description:
        'Detailed information about form options and their descriptions in the Atlas of Innovations platform.',
      type: 'article',
      siteName: 'Atlas of Innovations',
    },
    twitter: {
      card: 'summary_large_image',
      title: 'Option Details | Atlas of Innovations',
      description:
        'Detailed information about form options and their descriptions in the Atlas of Innovations platform.',
    },
    // Add additional meta tags
    keywords: 'ICMR, Atlas, options, details, descriptions, form options',
    authors: [{ name: 'Atlas of Innovations Team' }],
    robots: {
      index: true,
      follow: true,
    },
  };
};

const layout = async ({ children }: { children: React.ReactNode }) => {
  const cookieStore = await cookies();

  return (
    <div className="flex flex-col min-h-screen">
      <Suspense fallback={<NavBarUIFallBack />}>
        <NavBarUI
          color="text-Pure-White-FFFFFF"
          pathname="/"
          isAuthenticated={Boolean(cookieStore.get('payload-token')?.value)}
        />
      </Suspense>
      {children}
    </div>
  );
};

export default layout;
