import { Metadata } from 'next';
import { cookies } from 'next/headers';
import { Suspense } from 'react';
import GradientWrapper from '../../components/Cards/GradientWrapper';
import GlassCard from '../../components/Cards/glassCard';
import CircularLoader from '../../components/UI/CircularLoader';
import { NavBarUI } from '../../components/UI/navBar';
import HowToSubmitInnovationComponent from '../../components/howToSubmitInnovationComponent';
import { fetchCriteriaContent, fetchSubmitInnovationContent } from '../../utils/apiClient';
import { getPageMeta } from '../../utils/metadata';

export const generateMetadata = (): Metadata => {
  return {
    ...getPageMeta('/how-to-submit'),
    openGraph: {
      ...getPageMeta('/how-to-submit'),
      url: 'https://www.atlasoftbinnovations.in/how-to-submit',
      siteName: 'Atlas of Innovations',
      type: 'website',
    } as any,
  };
};
const InnovationSubmission = async () => {
  // Fetch content with depth=1 to populate criteria relationship
  const content = await fetchSubmitInnovationContent();
  // Use criteria from content if available, otherwise fetch separately
  let sections;
  if (
    content?.criteria &&
    content.criteria.length > 0 &&
    typeof content.criteria[0] === 'object' &&
    'title' in content.criteria[0]
  ) {
    sections = { docs: content.criteria };
  } else {
    sections = await fetchCriteriaContent();
  }

  const cookieStore = await cookies();
  if (!content)
    return (
      <>
        <GradientWrapper className="-mb-44 sm:-mb-30 h-70">
          <Suspense fallback={<div>Loading...</div>}>
            <NavBarUI
              color="text-Pure-White-FFFFFF"
              pathname="/how-to-submit"
              isAuthenticated={Boolean(cookieStore.get('payload-token')?.value)}
            />
          </Suspense>
        </GradientWrapper>
        <div className="calculated-height-page">
          <CircularLoader />
        </div>
      </>
    );

  return (
    <div className="flex flex-col calculated-height mb-20">
      <div>
        <GlassCard
          title={content?.heroTitle}
          description={content?.heroDescription}
          pathname="/how-to-submit"
          isAuthenticated={Boolean(cookieStore.get('payload-token')?.value)}
        />
      </div>
      <Suspense fallback={<div>...</div>}>
        <HowToSubmitInnovationComponent
          content={content}
          sections={sections}
          isAuthenticated={Boolean(cookieStore.get('payload-token')?.value)}
        />
      </Suspense>
    </div>
  );
};

export default InnovationSubmission;
