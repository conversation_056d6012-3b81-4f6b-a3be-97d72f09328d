import InnovationForm from '@/app/(frontend)/components/UI/InnovationForm';
import { PageLoadingFallBack } from '@/app/(frontend)/components/UI/loadingFallBackComponents';
import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';
import { Suspense } from 'react';

export default async function InnovationFormPage() {
  const cookieStore = await cookies();
  if (!Boolean(cookieStore.get('payload-token')?.value)) {
    redirect('/how-to-submit');
  }
  return (
    <Suspense fallback={<PageLoadingFallBack pathname="submit-an-innovation" />}>
      <InnovationForm />
    </Suspense>
  );
}
