import Link from 'next/link';

export default function NotFound() {
  return (
    <div className="flex flex-col items-center justify-center min-h-[60vh] px-4">
      <h2 className="text-3xl font-bold text-blue-900 mb-4">Innovation Class Not Found</h2>
      <p className="text-gray-600 mb-8 text-center max-w-md">
        The innovation class you are looking for could not be found. It may have been removed or you might have followed an incorrect link.
      </p>
      <Link 
        href="/browse-innovations"
        className="px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-700 text-white rounded-full font-medium hover:from-blue-700 hover:to-indigo-800 transition-colors"
      >
        Browse All Innovations
      </Link>
    </div>
  );
}
