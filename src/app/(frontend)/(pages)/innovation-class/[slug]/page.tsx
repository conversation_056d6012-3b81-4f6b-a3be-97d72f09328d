import GlassCard from '@/app/(frontend)/components/Cards/glassCard';
import { fetchInnovationClassBySlug } from '@/app/(frontend)/utils/apiClient';
import { getPageMeta } from '@/app/(frontend)/utils/metadata';
import { Metadata } from 'next';
import { cookies } from 'next/headers';
import { notFound } from 'next/navigation';
import InnovationClassContent from '../../../components/InnovationClass/InnovationClassContent';
import SimilarInnovations from '../../../components/InnovationClass/SimilarInnovations';

export const dynamic = 'force-dynamic';

function stripHtml(html: string): string {
  return html?.replace(/<[^>]*>/g, '').trim() || '';
}

export async function generateMetadata({ params }: any): Promise<Metadata> {
  const { slug } = await params;
  const decodedSlug = decodeURIComponent(slug);
  const innovationClass = await fetchInnovationClassBySlug(decodedSlug);

  if (!innovationClass) {
    return {
      title: 'Innovation Class Not Found',
      description: 'The requested innovation class could not be found.',
    };
  }

  const cleanDescription = stripHtml(innovationClass.description);

  return {
    ...getPageMeta(`/innovation-class/${slug}`),
    title: `${innovationClass.name} | Atlas of Innovations`,
    description: cleanDescription,
    openGraph: {
      title: innovationClass.name,
      description: cleanDescription,
      type: 'article',
      url: `${process.env.NEXT_PUBLIC_BACKEND_URL}/innovation-class/${slug}`,
    } as any,
  };
}

export default async function InnovationClassPage({ params }: any) {
  const { slug } = await params;
  const decodedSlug = decodeURIComponent(slug);
  const innovationClass = await fetchInnovationClassBySlug(decodedSlug);
  const cookieStore = await cookies();

  if (!innovationClass) {
    notFound();
  }

  return (
    <div>
      <GlassCard
        title={innovationClass.name}
        description=""
        pathname="/innovation-class"
        isAuthenticated={Boolean(cookieStore.get('payload-token')?.value)}
      />
      <div className="mx-auto max-w-[95vw] sm:max-w-[80vw] md:px-0 px-5">
        <InnovationClassContent innovationClass={innovationClass} />
        <SimilarInnovations innovationClass={innovationClass} />
      </div>
    </div>
  );
}
