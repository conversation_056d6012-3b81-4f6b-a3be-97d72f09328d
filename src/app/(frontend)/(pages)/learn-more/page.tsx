import { Metadata } from 'next';
import { cookies } from 'next/headers';
import Image from 'next/image';
import { Suspense } from 'react';
import GlassCard from '../../components/Cards/glassCard';
import GradientWrapper from '../../components/Cards/GradientWrapper';
import { PageLoadingFallBack } from '../../components/UI/loadingFallBackComponents';
import { NavBarUI } from '../../components/UI/navBar';
import SunburstChart from '../../components/UI/SunburstChart';
import { fetchLearnMoreUI, fetchSunburstUi } from '../../utils/apiClient';
import { getPageMeta } from '../../utils/metadata';

export const generateMetadata = (): Metadata => {
  return {
    ...getPageMeta('/learn-more'),
    openGraph: {
      ...getPageMeta('/learn-more'),
      url: 'https://www.atlasoftbinnovations.in/learn-more',
      siteName: 'Atlas of Innovations',
      type: 'website',
    } as any,
  };
};
const LearnMorePage: React.FC = async () => {
  const LearnMoreHeroRaw = await fetchLearnMoreUI();
  const cookieStore = await cookies();
  const data = await fetchSunburstUi();
  const customColors = ['#68c723', '#f25922', '#040B8c', '#feb924'];
  const logos = [
    '/images/icons/treat4.svg',
    '/images/icons/detect-icon.svg',
    '/images/icons/prevent-icon.svg',
    '/images/icons/build-icon.svg',
  ];

  if (!LearnMoreHeroRaw) {
    return (
      <div className="calculated-height">
        <GradientWrapper className="-mb-24 sm:-mb-20 min-h-[270px] relative z-10">
          <Suspense fallback={<div className="h-[60px]" />}>
            <NavBarUI
              color="text-Pure-White-FFFFFF"
              pathname="/learn-more"
              isAuthenticated={Boolean(cookieStore.get('payload-token')?.value)}
            />
          </Suspense>
        </GradientWrapper>
        <div className="mx-auto w-20 h-20 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }
  const LearnMoreHero = {
    ...LearnMoreHeroRaw,
    cardDescription: LearnMoreHeroRaw?.cardDescription,
  };

  return (
    <Suspense fallback={<PageLoadingFallBack pathname="/learn-more" />}>
      <GlassCard
        title={LearnMoreHero?.cardTitle || 'Map Of Innovations'}
        description={
          LearnMoreHero?.cardDescription ||
          "The four pillars of tuberculosis (TB) are Detect, Treat, Prevent, and Build (DTPB). These pillars are part of the National Strategic Plan (NSP) for TB elimination in India, which was in effect from 2017 to 2025. The NSP's goal was to reduce the number of TB cases, deaths, and out-of-pocket costs."
        }
        pathname="/learn-more"
        isAuthenticated={Boolean(cookieStore.get('payload-token')?.value)}
      />
      <div className="mx-auto max-w-[95vw] sm:max-w-[80vw] w-[80vw] mb-20">
        <section className="mb-10">
          <div className="flex justify-center w-full items-center py-6">
            <SunburstChart data={data} />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 mb-10 gap-6 w-full">
            {data?.children?.map((itm: any, index: number) => {
              return (
                <div
                  key={index}
                  className={`border rounded-xl p-4 shadow-md w-full relative`}
                  style={{
                    borderColor: customColors[index % customColors.length],
                  }}
                >
                  <div className="absolute top-2 right-2 text-gray-500 text-sm font-semibold">
                    {itm?.name}
                  </div>
                  <div className="flex items-center gap-4">
                    <div className="flex items-center justify-center w-12 h-12">
                      <Image
                        src={logos[index % logos.length]}
                        alt={`${itm?.name} Logo`}
                        width={48}
                        height={48}
                      />
                    </div>
                    <div>
                      <span className="text-6xl font-normal text-Midnight-Royal-Blue-02018B">
                        {itm?.innovationCount}
                      </span>
                      <span className=" ml-2 text-Midnight-Royal-Blue-02018B">Innovations</span>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </section>
      </div>
    </Suspense>
  );
};

export default LearnMorePage;
