export const dynamic = 'force-dynamic'; // 👈 Add this line

// src/app/(frontend)/(pages)/browse-innovations/page.tsx
import { Metadata } from 'next';
import { cookies } from 'next/headers';
import { Suspense } from 'react';
import GlassCard from '../../components/Cards/glassCard';
import CircularLoader from '../../components/UI/CircularLoader';
import { InnovationsHero, PaginationData } from '../../types/data.types';
import { serializeRichText } from '../../utils/functions';
import { getPageMeta } from '../../utils/metadata';
import Innovations from './Native/innovations';

export const generateMetadata = (): Metadata => {
  return {
    ...getPageMeta('/browse-innovations'),
    openGraph: {
      ...getPageMeta('/browse-innovations'),
      url: 'https://www.atlasoftbinnovations.in/browse-innovations',
      siteName: 'Atlas of Innovations',
      type: 'website',
    } as any,
  };
};
// Fetch hero content
const fetchUIContent = async (): Promise<InnovationsHero | null> => {
  try {
    const res = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/globals/innovations-hero`);
    if (!res.ok) throw new Error('Failed to fetch UI content (browse innovation)');
    const data: InnovationsHero = await res.json();
    return data;
  } catch (error) {
    console.error(error);
    return null;
  }
};

// Fetch innovations with query parameters
const fetchInnovations = async (
  searchParamsPromise: Promise<{ [key: string]: string | string[] | undefined }>,
): Promise<PaginationData> => {
  try {
    const searchParams = await searchParamsPromise; // Await the searchParams promise

    const url = new URL('/api/innovations/innovations', process.env.NEXT_PUBLIC_BACKEND_URL || '');

    const page = (searchParams?.page as string) || '1';
    const limit = '9';
    const searchQuery = (searchParams?.searchQuery as string) || '';
    const filters = {
      category: (searchParams?.category as string)?.split(',') || [],
      interventionType: (searchParams?.interventionType as string)?.split(',') || [],
      currentStageOfDevelopment:
        (searchParams?.currentStageOfDevelopment as string)?.split(',') || [],
      myInnovations: searchParams?.myInnovations === 'true',
      innovationClass: (searchParams?.innovationClass as string)?.split(',') || [],
    };

    url.searchParams?.set('page', page);
    url.searchParams?.set('limit', limit);
    if (filters.category.length) url.searchParams?.set('category', filters.category.join(','));
    if (filters.interventionType.length)
      url.searchParams?.set('interventionType', filters.interventionType.join(','));
    if (filters.currentStageOfDevelopment.length)
      url.searchParams?.set(
        'currentStageOfDevelopment',
        filters.currentStageOfDevelopment.join(','),
      );
    if (filters.myInnovations) url.searchParams?.set('myInnovations', 'true');
    if (searchQuery) url.searchParams?.set('searchQuery', searchQuery);
    if (filters.innovationClass.length)
      url.searchParams?.set('innovationClass', filters.innovationClass.join(','));

    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' },
      cache: 'no-store',
    });
    if (!response.ok) throw new Error(`Failed to fetch innovations: ${response.statusText}`);

    return await response.json();
  } catch (error) {
    console.error('Error fetching innovations:', error);
    return {
      docs: [],
      totalDocs: 0,
      limit: 9,
      page: 1,
      totalPages: 1,
      hasPrevPage: false,
      hasNextPage: false,
      prevPage: null,
      nextPage: null,
    };
  }
};

// Page component
export default async function Page({
  searchParams,
}: {
  searchParams: any; // Temporarily use 'any' to bypass type checking
}) {
  const cookieStore = await cookies();
  const innovationsHeroRaw = await fetchUIContent();
  const initialPaginationData = await fetchInnovations(searchParams);
  if (!innovationsHeroRaw) {
    return (
      <div className=" calculated-height-page">
        <div className="w-20 h-20 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto translate-y-[50%]"></div>
      </div>
    );
  }

  const innovationsHero = {
    ...innovationsHeroRaw,
    description: serializeRichText(innovationsHeroRaw.description),
  };

  return (
    <>
      <GlassCard
        title={innovationsHero.title}
        isInnerHTML
        isAuthenticated={Boolean(cookieStore.get('payload-token')?.value)}
        description={innovationsHero.description}
        pathname="/browse-innovations"
      />
      <div className="calculated-height max-w-[95vw] sm:max-w-[80vw] w-full mx-auto">
        <section>
          <Suspense
            fallback={
              <div>
                <CircularLoader />
              </div>
            }
          >
            <Innovations initialPaginationData={initialPaginationData as any} />
          </Suspense>
        </section>
      </div>
    </>
  );
}
