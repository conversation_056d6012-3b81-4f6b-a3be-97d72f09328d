'use client'; // Mark as Client Component since it uses hooks

import CircularLoader from '@/app/(frontend)/components/UI/CircularLoader';
import Flowchart from '@/app/(frontend)/components/UI/FlowChart';
import {
  FilterOption,
  FilterOptions,
  Innovation,
  PaginationData,
} from '@/app/(frontend)/types/data.types';
import { fetchOptions } from '@/app/(frontend)/utils/apiClient';
import { getWindow } from '@/app/(frontend)/utils/clientUtils';
import { Suspense, useEffect, useState } from 'react';
import FilterCard from '../../../components/Cards/FilterCard';
import InnovationCards from '../../../components/Cards/InnovationCards';

// Define interfaces

const defaultFilters = {
  category: [],
  interventionType: [],
  currentStageOfDevelopment: [],
  myInnovations: false,
  innovationClass: [],
};

// Fetch filter options (client-side only)
const fetchFilterOptions = async (): Promise<FilterOptions> => {
  try {
    const data = await fetchOptions();
    const options = data.docs as FilterOption[];
    return {
      categories: options.filter((opt) => opt.fieldName === 'category' && opt.isActive),
      interventionTypes: options.filter(
        (opt) => opt.fieldName === 'interventionType' && opt.isActive,
      ),
      stages: options.filter(
        (opt) => opt.fieldName === 'currentStageOfDevelopment' && opt.isActive,
      ),
      innovationClass: [], // Add innovationClass to default filters
    };
  } catch (error) {
    console.error('Error fetching filter options:', error);
    return {
      categories: [],
      interventionTypes: [],
      stages: [],
      innovationClass: [],
    };
  }
};

// Fetch innovations (client-side only)
const fetchInnovations = async (
  page: number = 1,
  limit: number = 9,
  filters: any = defaultFilters,
  searchQuery: string = '',
): Promise<PaginationData> => {
  try {
    const url = filters?.innovationClass?.length
      ? new URL('/api/innovationClass/innovations', window?.location.origin)
      : new URL('/api/innovations/innovations', window?.location.origin);
    url.searchParams.set('page', page.toString());
    url.searchParams.set('limit', limit.toString());
    if (filters.category.length) url.searchParams.set('category', filters.category.join(','));
    if (filters.interventionType.length)
      url.searchParams.set('interventionType', filters.interventionType.join(','));
    if (filters.currentStageOfDevelopment.length)
      url.searchParams.set(
        'currentStageOfDevelopment',
        filters.currentStageOfDevelopment.join(','),
      );
    if (filters.myInnovations) url.searchParams.set('myInnovations', 'true');
    if (searchQuery) url.searchParams.set('searchQuery', searchQuery);
    if (filters?.innovationClass)
      url.searchParams.set('innovationClass', filters.innovationClass.join(','));

    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' },
    });
    if (!response.ok) throw new Error(`Failed to fetch innovations: ${response.statusText}`);

    return await response.json();
  } catch (error) {
    console.error('Error fetching innovations:', error);
    return {
      docs: [],
      totalDocs: 0,
      limit,
      page: 1,
      totalPages: 1,
      hasPrevPage: false,
      hasNextPage: false,
      prevPage: null,
      nextPage: null,
    };
  }
};

// Client Component
const Innovations = ({ initialPaginationData }: { initialPaginationData: PaginationData }) => {
  const [innovations, setInnovations] = useState<Innovation[]>(initialPaginationData.docs);
  const [loader, setLoader] = useState(false);
  const [flowMap, setFlowMap] = useState<null | object>(null);
  const [pagination, setPagination] = useState<PaginationData>(initialPaginationData);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterOptions, setFilterOptions] = useState<FilterOptions | null>(null);
  const [filters, setFilters] = useState(defaultFilters);
  useEffect(() => {
    const window = getWindow();
    if (!window) return;
    const params = new URLSearchParams(window?.location.search);
    const initialFilters = {
      category: params.get('category')?.split(',') || [],
      interventionType: params.get('interventionType')?.split(',') || [],
      currentStageOfDevelopment: params.get('currentStageOfDevelopment')?.split(',') || [],
      myInnovations: params.get('myInnovations') === 'true',
      innovationClass: params.get('innovationClass')?.split(',') || [],
    };
    const initialSearchQuery = params.get('searchQuery') || '';
    const initialPage = parseInt(params.get('page') || '1', 10);

    setFilters(initialFilters as any);
    setSearchQuery(initialSearchQuery);
    setPagination((prev) => ({ ...prev, page: initialPage }));

    // Immediately fetch innovations with correct filters
    const fetchData = async () => {
      setLoader(true);
      const data = await fetchInnovations(
        initialPage,
        pagination.limit,
        initialFilters,
        initialSearchQuery,
      );
      setInnovations(data.docs);
      setPagination(data);
      setLoader(false);
    };

    fetchData();
  }, []);

  // Fetch filter options on client-side mount
  useEffect(() => {
    const loadFilterOptions = async () => {
      const options = await fetchFilterOptions();
      setFilterOptions(options);
    };
    loadFilterOptions();
  }, []);

  // Fetch data when filters, search, or pagination change
  useEffect(() => {
    const window = getWindow();
    if (!window) return;
    if (!filterOptions) return; // Wait for filter options to load

    // Check if filters/search/pagination differ from initial state
    const params = new URLSearchParams(window?.location.search);
    const initialFilters = {
      category: params.get('category')?.split(',') || [],
      interventionType: params.get('interventionType')?.split(',') || [],
      currentStageOfDevelopment: params.get('currentStageOfDevelopment')?.split(',') || [],
      myInnovations: params.get('myInnovations') === 'true',
      innovationClass: params.get('innovationClass')?.split(',') || [],
    };
    const initialSearchQuery = params.get('searchQuery') || '';
    const initialPage = parseInt(params.get('page') || '1', 10);

    const filtersChanged = JSON.stringify(filters) !== JSON.stringify(initialFilters);
    const searchChanged = searchQuery !== initialSearchQuery;
    const pageChanged = pagination.page !== initialPage;

    if (!(filtersChanged || searchChanged || pageChanged)) return; // Skip fetch if no changes

    // Reset page to 1 if filters changed
    const pageToFetch = filtersChanged ? 1 : pagination.page;

    const paramsNew = new URLSearchParams();
    if (filters.category.length) paramsNew.set('category', filters.category.join(','));
    if (filters.interventionType.length)
      paramsNew.set('interventionType', filters.interventionType.join(','));
    if (filters.currentStageOfDevelopment.length)
      paramsNew.set('currentStageOfDevelopment', filters.currentStageOfDevelopment.join(','));
    if (filters.myInnovations) paramsNew.set('myInnovations', 'true');
    if (searchQuery) paramsNew.set('searchQuery', searchQuery);
    if (pageToFetch > 1) paramsNew.set('page', pageToFetch.toString());
    if (filters?.innovationClass?.length)
      paramsNew.set('innovationClass', filters.innovationClass.join(','));

    window?.history.pushState({}, '', `?${paramsNew.toString()}`);
    const fetchData = async () => {
      setLoader(true);
      const data = await fetchInnovations(pageToFetch, pagination.limit, filters, searchQuery);
      setInnovations(data.docs);
      setPagination(data);
      setLoader(false);
    };
    fetchData();
  }, [filters, searchQuery, pagination.page, filterOptions]);

  const handlePageChange = (newPage: number) => {
    // Update pagination state
    setPagination((prev) => ({ ...prev, page: newPage }));

    // Scroll to the top of the page with smooth animation
    window?.scrollTo({
      top: 10,
      behavior: 'smooth',
    });
  };

  // Update filters and reset page to 1
  const handleFilterChange = (newFilters: typeof defaultFilters) => {
    setFilters(newFilters);
    setPagination((prev) => ({ ...prev, page: 1 })); // Reset page to 1
  };

  return (
    <Suspense
      fallback={
        <div>
          <CircularLoader />
        </div>
      }
    >
      <div className="py-4 w-full">
        {flowMap && (
          <div className="w-full border-black rounded-md">
            <Flowchart currentInnovation={flowMap} />
          </div>
        )}
        {/* Search Bar */}

        {/* Dynamic Filters */}
        <FilterCard
          filterOptions={filterOptions as any}
          filters={filters as any}
          setFilters={handleFilterChange as any} // Use updated handler
          setFlowmap={setFlowMap as any}
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
        />

        {/* Innovation Cards */}
        <Suspense fallback={<CircularLoader />}>
          <InnovationCards innovations={innovations} loader={loader} />
        </Suspense>

        {/* Pagination Controls */}
        {pagination.totalPages > 1 && (
          <div className="flex justify-center items-center gap-4 mt-6">
            <button
              onClick={() => handlePageChange(pagination.prevPage!)}
              disabled={!pagination.hasPrevPage}
              className="px-4 py-2 rounded-full bg-gray-100 text-gray-700 text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-200 transition-colors duration-200"
            >
              Previous
            </button>
            <span className="text-sm text-gray-700">
              Page {pagination.page} of {pagination.totalPages}
            </span>
            <button
              onClick={() => handlePageChange(pagination.nextPage!)}
              disabled={!pagination.hasNextPage}
              className="px-4 py-2 rounded-full bg-gray-100 text-gray-700 text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-200 transition-colors duration-200"
            >
              Next
            </button>
          </div>
        )}
      </div>
    </Suspense>
  );
};

export default Innovations;
