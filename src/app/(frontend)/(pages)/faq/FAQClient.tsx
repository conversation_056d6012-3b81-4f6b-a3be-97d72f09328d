'use client'; // ✅ Mark as client component

import Image from 'next/image';
import { useState } from 'react';
import { FAQProps } from '../../types/data.types';

export default function FAQClient({ faqData }: FAQProps) {
  const [openIndex, setOpenIndex] = useState<number | null>(null);

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <div className="max-w-[95vw] sm:max-w-[80vw] mx-auto p-4">
      <div className="bg-gray-100 p-5 rounded-2xl my-8">
        {/* FAQ Items */}
        <div className="space-y-4">
          {faqData.faqs.map((faq, index) => (
            <div
              key={index}
              className="bg-white p-4 rounded-lg shadow transition-all duration-300 ease-in-out"
            >
              <div
                className="flex justify-between items-center cursor-pointer"
                onClick={() => toggleFAQ(index)}
              >
                <h2 className="font-semibold">{faq.question}</h2>
                <div className="bg-gray-200 rounded-xl p-1">
                  <Image
                    alt="arrowIcon"
                    className={`w-3 h-3 transform transition-transform duration-300 ${
                      openIndex === index ? 'rotate-270' : 'rotate-0'
                    }`}
                    height={20}
                    width={20}
                    src="/images/icons/darkArrowIcon.svg"
                  />
                </div>
              </div>
              <div
                className={`overflow-hidden transition-[max-height] duration-300 ease-in-out ${
                  openIndex === index ? 'max-h-full opacity-100' : 'max-h-0 opacity-0'
                }`}
              >
                <p className="text-gray-600 mt-2 ">{faq.answer}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
