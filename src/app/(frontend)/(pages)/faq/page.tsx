import { Metadata } from 'next';
import { cookies } from 'next/headers';
import { Suspense } from 'react';
import GlassCard from '../../components/Cards/glassCard';
import { PageLoadingFallBack } from '../../components/UI/loadingFallBackComponents';
import { fetchFaqUi } from '../../utils/apiClient';
import { getPageMeta } from '../../utils/metadata';
import FAQClient from './FAQClient';

export const generateMetadata = (): Metadata => {
  return {
    ...getPageMeta('/faq'),
    openGraph: {
      ...getPageMeta('/faq'),
      url: 'https://www.atlasoftbinnovations.in/faq',
      siteName: 'Atlas of Innovations',
      type: 'website',
    } as any,
  };
};
export default async function FAQPage() {
  // Fetch data on the server
  const cookieStore = await cookies();
  const faqData = await fetchFaqUi();
  if (!faqData) return <p>Loading preview...</p>;

  return (
    <Suspense fallback={<PageLoadingFallBack pathname="/faq" />}>
      <div>
        <GlassCard
          title={faqData.title || 'Frequently Asked Questions'}
          description={faqData.subtitle || "Have questions? We've got answers!"}
          pathname="/faq"
          isAuthenticated={Boolean(cookieStore?.get('payload-token')?.value)}
        />
        {/* Pass fetched data to the Client Component */}
        <FAQClient faqData={faqData} />
      </div>
    </Suspense>
  );
}
