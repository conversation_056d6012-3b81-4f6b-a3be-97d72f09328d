export const dynamic = 'force-dynamic'; // Force dynamic rendering

import { en } from '@/app/(frontend)/translations';
import { Metadata } from 'next';
import { cookies } from 'next/headers';
import Image from 'next/image';
import Link from 'next/link';
import { Suspense } from 'react';
import GlassCard from '../../components/Cards/glassCard';
import Button from '../../components/UI/Button';
import CircularLoader from '../../components/UI/CircularLoader';
import { PaginationData } from '../../types/data.types';
import { getPageMeta } from '../../utils/metadata';
import Innovations from './Native/innovations';

export const generateMetadata = (): Metadata => {
  return {
    ...getPageMeta('/my-innovations'),
    title: 'My Innovations | Atlas of Innovations',
    description: 'View and manage your submitted innovations',
    openGraph: {
      ...getPageMeta('/my-innovations'),
      url: 'https://www.atlasoftbinnovations.in/my-innovations',
      siteName: 'Atlas of Innovations',
      type: 'website',
    } as any,
  };
};

// Fetch innovations with query parameters
const fetchInnovations = async (): Promise<PaginationData> => {
  try {
    const url = new URL('/api/innovations/innovations', process.env.NEXT_PUBLIC_BACKEND_URL || '');

    // Always set myInnovations to true for this page
    url.searchParams?.set('page', '1');
    url.searchParams?.set('limit', '9');
    url.searchParams?.set('myInnovations', 'true');

    // Get authentication token from cookies
    const cookieStore = await cookies();
    const authToken = await cookieStore.get('payload-token')?.value;

    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(authToken ? { Authorization: `JWT ${authToken}` } : {}),
      },
      credentials: 'include', // Include cookies for authentication
      cache: 'no-store',
    });

    if (!response.ok) throw new Error(`Failed to fetch innovations: ${response.statusText}`);

    return await response.json();
  } catch (error) {
    console.error('Error fetching innovations:', error);
    return {
      docs: [],
      totalDocs: 0,
      limit: 9,
      page: 1,
      totalPages: 1,
      hasPrevPage: false,
      hasNextPage: false,
      prevPage: null,
      nextPage: null,
    };
  }
};

// Page component
export default async function Page() {
  const initialPaginationData = await fetchInnovations();
  const cookieStore = await cookies();

  return (
    <>
      <GlassCard
        title="My Innovations"
        description="View and manage all your submitted innovations"
        pathname="/my-innovations"
        isAuthenticated={Boolean(cookieStore.get('payload-token')?.value)}
      />
      <div className="calculated-height max-w-[95vw] sm:max-w-[80vw] w-full mx-auto">
        <section className="flex-1 mt-4 w-full mb-20">
          <div className="py-4 pointer-events-none">
            <Link href="/how-to-submit">
              <Button
                variant="gradient"
                rightIcon={
                  <Image
                    alt="arrowIcon of Innovations"
                    className="w-7 h-7 bg-white p-2 rounded-full"
                    height={20}
                    width={20}
                    src="/images/icons/darkArrowIcon.svg"
                  />
                }
                className="pointer-events-auto"
                title={en.SUBMIT_YOUR_INNOVATION}
              />
            </Link>
          </div>
          <Suspense
            fallback={
              <div>
                <CircularLoader />
              </div>
            }
          >
            <Innovations initialPaginationData={initialPaginationData} />
          </Suspense>
        </section>
      </div>
    </>
  );
}
