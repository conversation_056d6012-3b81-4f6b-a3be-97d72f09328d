'use client'; // Mark as Client Component since it uses hooks

import CircularLoader from '@/app/(frontend)/components/UI/CircularLoader';
import { FilterOptions, Innovation, PaginationData } from '@/app/(frontend)/types/data.types';
import { fetchOptions } from '@/app/(frontend)/utils/apiClient';
import { getWindow } from '@/app/(frontend)/utils/clientUtils';
import { useRouter } from 'next/navigation';
import { Suspense, useEffect, useState } from 'react';
import FilterCard from '../../../components/Cards/FilterCard';
import InnovationCards from '../../../components/Cards/InnovationCards';

// Define default filters
const defaultFilters = {
  category: [],
  interventionType: [],
  currentStageOfDevelopment: [],
  myInnovations: true, // Always true for this page
  innovationClass: [],
};

// Fetch filter options
const fetchFilterOptions = async (): Promise<FilterOptions> => {
  try {
    const options = await fetchOptions();
    const groupedOptions = options.docs.reduce((acc: any, option: any) => {
      const field = option.fieldName;
      if (!acc[field]) acc[field] = [];
      acc[field].push({
        id: option.id,
        value: option.value,
        fieldName: option.fieldName,
      });
      return acc;
    }, {});

    return {
      categories: groupedOptions.category || [],
      interventionTypes: groupedOptions.interventionType || [],
      stages: groupedOptions.currentStageOfDevelopment || [],
      innovationClass: groupedOptions.innovationClass || [],
    };
  } catch (error) {
    console.error('Error fetching filter options:', error);
    return {
      categories: [],
      interventionTypes: [],
      stages: [],
      innovationClass: [],
    };
  }
};

// Fetch innovations (client-side only)
const fetchInnovations = async (
  page: number = 1,
  limit: number = 9,
  filters: any = defaultFilters,
  searchQuery: string = '',
): Promise<PaginationData> => {
  try {
    const url = new URL('/api/innovations/innovations', window?.location.origin);
    url.searchParams.set('page', page.toString());
    url.searchParams.set('limit', limit.toString());
    url.searchParams.set('myInnovations', 'true'); // Always true for this page

    if (filters.category.length) url.searchParams.set('category', filters.category.join(','));
    if (filters.interventionType.length)
      url.searchParams.set('interventionType', filters.interventionType.join(','));
    if (filters.currentStageOfDevelopment.length)
      url.searchParams.set(
        'currentStageOfDevelopment',
        filters.currentStageOfDevelopment.join(','),
      );
    if (searchQuery) url.searchParams.set('searchQuery', searchQuery);

    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' },
      credentials: 'include', // Include cookies for authentication
    });
    if (!response.ok) throw new Error(`Failed to fetch innovations: ${response.statusText}`);

    return await response.json();
  } catch (error) {
    console.error('Error fetching innovations:', error);
    return {
      docs: [],
      totalDocs: 0,
      limit,
      page: 1,
      totalPages: 1,
      hasPrevPage: false,
      hasNextPage: false,
      prevPage: null,
      nextPage: null,
    };
  }
};

// Client Component
const Innovations = ({ initialPaginationData }: { initialPaginationData: PaginationData }) => {
  const router = useRouter();
  const [innovations, setInnovations] = useState<Innovation[]>(initialPaginationData.docs);
  const [loader, setLoader] = useState(false);
  const [pagination, setPagination] = useState<PaginationData>(initialPaginationData);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterOptions, setFilterOptions] = useState<FilterOptions | null>(null);
  const [filters, setFilters] = useState(defaultFilters);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Check authentication on mount
  useEffect(() => {
    const checkAuth = () => {
      const atlasInfo = localStorage.getItem('atlasInfo');
      if (!atlasInfo) {
        // Redirect to login if not authenticated
        router.push('/?authModal=true');
        return false;
      }
      setIsAuthenticated(true);
      return true;
    };

    checkAuth();
  }, [router]);

  // Initialize from URL params on mount
  useEffect(() => {
    const window = getWindow();
    if (!window) return;
    const params = new URLSearchParams(window?.location.search);
    const initialFilters = {
      ...defaultFilters,
      category: params.get('category')?.split(',') || [],
      interventionType: params.get('interventionType')?.split(',') || [],
      currentStageOfDevelopment: params.get('currentStageOfDevelopment')?.split(',') || [],
      myInnovations: true, // Always true for this page
    };
    const initialSearchQuery = params.get('searchQuery') || '';
    const initialPage = parseInt(params.get('page') || '1', 10);

    setFilters(initialFilters as any);
    setSearchQuery(initialSearchQuery);
    setPagination((prev) => ({ ...prev, page: initialPage }));

    // Immediately fetch innovations with correct filters
    const fetchData = async () => {
      setLoader(true);
      const data = await fetchInnovations(
        initialPage,
        pagination.limit,
        initialFilters,
        initialSearchQuery,
      );
      setInnovations(data.docs);
      setPagination(data);
      setLoader(false);
    };

    fetchData();
  }, []);

  // Fetch filter options on client-side mount
  useEffect(() => {
    const loadFilterOptions = async () => {
      const options = await fetchFilterOptions();
      setFilterOptions(options);
    };
    loadFilterOptions();
  }, []);

  // Fetch data when filters, search, or pagination change
  useEffect(() => {
    const window = getWindow();
    if (!window) return;

    if (!filterOptions || !isAuthenticated) return; // Wait for filter options to load and authentication

    // Check if filters/search/pagination differ from initial state
    const params = new URLSearchParams(window?.location.search);
    const initialFilters = {
      ...defaultFilters,
      category: params.get('category')?.split(',') || [],
      interventionType: params.get('interventionType')?.split(',') || [],
      currentStageOfDevelopment: params.get('currentStageOfDevelopment')?.split(',') || [],
    };
    const initialSearchQuery = params.get('searchQuery') || '';
    const initialPage = parseInt(params.get('page') || '1', 10);

    const filtersChanged =
      JSON.stringify({ ...filters, myInnovations: true }) !==
      JSON.stringify({ ...initialFilters, myInnovations: true });
    const searchChanged = searchQuery !== initialSearchQuery;
    const pageChanged = pagination.page !== initialPage;

    if (!(filtersChanged || searchChanged || pageChanged)) return; // Skip fetch if no changes

    // Reset page to 1 if filters changed
    const pageToFetch = filtersChanged ? 1 : pagination.page;

    // Update URL with new params
    const paramsNew = new URLSearchParams();
    if (filters.category.length) paramsNew.set('category', filters.category.join(','));
    if (filters.interventionType.length)
      paramsNew.set('interventionType', filters.interventionType.join(','));
    if (filters.currentStageOfDevelopment.length)
      paramsNew.set('currentStageOfDevelopment', filters.currentStageOfDevelopment.join(','));
    if (searchQuery) paramsNew.set('searchQuery', searchQuery);
    if (pageToFetch > 1) paramsNew.set('page', pageToFetch.toString());
    // Always set myInnovations to true for this page
    paramsNew.set('myInnovations', 'true');

    window?.history.pushState({}, '', `?${paramsNew.toString()}`);
    const fetchData = async () => {
      setLoader(true);
      const data = await fetchInnovations(pageToFetch, pagination.limit, filters, searchQuery);
      setInnovations(data.docs);
      setPagination(data);
      setLoader(false);
    };
    fetchData();
  }, [filters, searchQuery, pagination.page, filterOptions, isAuthenticated]);

  const handlePageChange = (newPage: number) => {
    setPagination((prev) => ({ ...prev, page: newPage }));
  };

  // Update filters and reset page to 1
  const handleFilterChange = (newFilters: typeof defaultFilters) => {
    // Always ensure myInnovations is true
    setFilters({ ...newFilters, myInnovations: true });
    setPagination((prev) => ({ ...prev, page: 1 })); // Reset page to 1
  };

  if (!isAuthenticated) {
    return (
      <div className="flex justify-center items-center h-40">
        <CircularLoader />
      </div>
    );
  }

  return (
    <Suspense
      fallback={
        <div>
          <CircularLoader />
        </div>
      }
    >
      <div className="flex flex-col space-y-6">
        {/* Search Bar */}
        <div className="flex flex-col md:flex-row gap-4">
          {/* <div className="flex-1 relative">
            <input
              type="text"
              placeholder="Search innovations..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full p-3 pl-10 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <span className="absolute left-3 top-3">🔍</span>
          </div> */}
        </div>

        {/* Dynamic Filters */}
        <FilterCard
          filterOptions={filterOptions as any}
          filters={filters as any}
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
          setFilters={handleFilterChange as any}
          hideMyInnovationsFilter={true}
        />

        {/* Innovation Cards */}
        <Suspense
          fallback={
            <div>
              <CircularLoader />
            </div>
          }
        >
          <InnovationCards innovations={innovations} loader={loader} isMyInnovations={true} />
        </Suspense>

        {/* Pagination */}
        {pagination.totalPages > 1 && (
          <div className="flex justify-center items-center gap-4 mt-6">
            <button
              onClick={() => handlePageChange(pagination.prevPage || 1)}
              disabled={!pagination.hasPrevPage}
              className="px-4 py-2 rounded-full bg-gray-100 text-gray-700 text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-200 transition-colors duration-200"
            >
              Previous
            </button>
            <span className="text-sm text-gray-700">
              Page {pagination.page} of {pagination.totalPages}
            </span>
            <button
              onClick={() => handlePageChange(pagination.nextPage || 1)}
              disabled={!pagination.hasNextPage}
              className="px-4 py-2 rounded-full bg-gray-100 text-gray-700 text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-200 transition-colors duration-200"
            >
              Next
            </button>
          </div>
        )}
      </div>
    </Suspense>
  );
};

export default Innovations;
