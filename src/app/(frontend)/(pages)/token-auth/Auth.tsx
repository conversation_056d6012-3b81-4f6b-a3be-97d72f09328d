'use client';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { authenticateUser } from './authenticate';

export default function AuthComponent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const token = searchParams.get('token');
  const [authStatus, setAuthStatus] = useState<'loading' | 'success' | 'error'>('loading');

  useEffect(() => {
    if (authStatus === 'success') {
      setTimeout(() => {
        router.push('/?authModal=true');
      }, 8000);
    }
  }, [authStatus]);

  useEffect(() => {
    if (token) {
      authenticateUser(token).then((isValid) => {
        setAuthStatus(isValid ? 'success' : 'error');
      });
    } else {
      setAuthStatus('error');
    }
  }, []);

  return (
    <div className="flex h-screen items-center justify-center bg-gray-100">
      <div className="w-96 p-6 rounded-lg shadow-lg bg-white text-center">
        {authStatus === 'loading' && <p className="text-gray-600">Authenticating...</p>}
        {authStatus === 'success' && (
          <p className="text-green-600 font-semibold">Authentication Successful!</p>
        )}
        {authStatus === 'error' && <p className="text-red-600 font-semibold">Invalid Token!</p>}
      </div>
    </div>
  );
}
