'use server';

export async function authenticateUser(token: string): Promise<any> {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_BACKEND_URL}/api/appUsers/verify/${token}`,
      {
        method: 'POST',
      },
    );
    return response.ok; // Return true if response is successful (status 200-299)
  } catch (error) {
    return false; // Return false if API call fails
  }
}
