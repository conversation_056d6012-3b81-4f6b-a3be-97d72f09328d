import GlassCard from '@/app/(frontend)/components/Cards/glassCard';
import { PageLoadingFallBack } from '@/app/(frontend)/components/UI/loadingFallBackComponents';
import TaxonomyComponent from '@/app/(frontend)/components/UI/TaxonomyComponent';
import { getTaxonomy } from '@/app/(frontend)/utils/apiClient';
import { getPageMeta } from '@/app/(frontend)/utils/metadata';
import { Metadata } from 'next';
import { cookies } from 'next/headers';
import { Suspense } from 'react';

export const generateMetadata = async ({ params }: any): Promise<Metadata> => {
  const { slug } = await params;

  return {
    ...getPageMeta(`/taxonomy/${slug}`),
    openGraph: {
      ...getPageMeta(`/taxonomy/${slug}`),
      url: 'https://www.atlasoftbinnovations.in/taxonomy',
      siteName: 'Atlas of Innovations',
      type: 'website',
    } as any,
  };
};

export default async function TaxonomyPage({ params }: any) {
  const cookieStore = await cookies();
  const { slug } = await params;
  const formattedTitle = await getTaxonomy(slug);
  return (
    <Suspense fallback={<PageLoadingFallBack pathname={`/taxonomy/${slug}`} />}>
      <GlassCard
        title={formattedTitle?.value ?? 'NOT FOUND'}
        description=""
        pathname={`/taxonomy/${slug}`}
        isAuthenticated={Boolean(cookieStore?.get('payload-token')?.value)}
      />
      <TaxonomyComponent descripition={`${formattedTitle?.description}`} />
    </Suspense>
  );
}
