import DescriptionSection from '@/app/(frontend)/components/Cards/DescriptionSection';
import GlassCard from '@/app/(frontend)/components/Cards/glassCard';
import DescriptionTooltip from '@/app/(frontend)/components/UI/DescriptionTooltip';
import { fetchInnovation } from '@/app/(frontend)/utils/apiClient';
import { cookies } from 'next/headers';
import Image from 'next/image';
import Link from 'next/link';
import InsightsLessons from '../../../components/InovationDetails/InsightsLessons';
import SummaryOfInovation from '../../../components/InovationDetails/SummaryOfInovation';

function stripHtml(html: string): string {
  return html.replace(/<[^>]*>/g, '').trim();
}

export async function generateMetadata({ params }: any) {
  const { slug } = await params;
  const innovation = await fetchInnovation(slug);
  const cleanDescription = stripHtml(innovation?.description ?? '');

  return {
    title: `${innovation.title} | Atlas of Innovations`,
    description: cleanDescription,
    openGraph: {
      title: innovation?.title,
      description: cleanDescription,
      type: 'article',
      url: `${process.env.NEXT_PUBLIC_BACKEND_URL}/innovation/${slug}`,
    },
  };
}

export default async function InovationDetails({ params }: any) {
  const { slug } = await params;
  const currentInnovation = await fetchInnovation(slug);
  const cookieStore = await cookies();

  if (!(currentInnovation?.status === 'approved')) {
    return (
      <div className="flex items-center justify-center h-screen">
        <p className="text-2xl font-bold">Innovation is not approved yet.</p>
      </div>
    );
  }

  return (
    <div>
      <GlassCard
        title={currentInnovation.title}
        description={currentInnovation.organizationName ?? ''}
        pathname="/innovation"
        isAuthenticated={Boolean(cookieStore.get('payload-token')?.value)}
        bannerImage={currentInnovation.bannerImage ?? undefined}
      />
      <div className="mx-auto max-w-[95vw] sm:max-w-[80vw] md:px-0 px-5">
        {/* Development Stage Section */}
        {currentInnovation.currentStageOfDevelopment && (
          <div className="mt-8 mb-8 flex items-center justify-end">
            <div className="flex items-center gap-2">
              <Image
                src="/images/icons/development-marketing-outline-svgrepo-com.svg"
                alt="Development Stage"
                width={40}
                height={40}
                className="mr-2"
              />
              <DescriptionTooltip
                description={currentInnovation.currentStageOfDevelopmentTaxonomy ?? ' '}
                position="cursor-bottom-left"
                maxWords={10}
                shouldShowReadMore={true}
                slug={currentInnovation?.currentStageOfDevelopmentSlug ?? ''}
              >
                <span className="text-orange-500 font-medium">
                  {currentInnovation?.currentStageOfDevelopment}
                </span>
              </DescriptionTooltip>
            </div>
          </div>
        )}
        <DescriptionSection
          title="About The Innovation"
          image="/images/icons/about-svgrepo-com (1).svg"
        >
          <SummaryOfInovation description={currentInnovation?.description ?? ''} />
        </DescriptionSection>
        <DescriptionSection title="Categories" image="/images/icons/category-svgrepo-com (1).svg">
          <InsightsLessons
            innovationDetails={currentInnovation as any}
            className={'w-full flex-1'} // Pass className conditionally
          />
        </DescriptionSection>
        {currentInnovation?.innovationClass?.slug && (
          <DescriptionSection
            title="Innovation Class"
            image="/images/icons/about-svgrepo-com (1).svg"
          >
            <Link
              href={`/innovation-class/${currentInnovation?.innovationClass?.slug}`}
              className="text-blue-600 hover:text-blue-800 italic wrap-anywhere"
            >
              {currentInnovation?.innovationClass?.name}
            </Link>
          </DescriptionSection>
        )}
        {currentInnovation.supportingDocument &&
          currentInnovation.supportingDocument.length > 0 && (
            <DescriptionSection title="Attachments" image="/images/icons/about-svgrepo-com (1).svg">
              <div className="flex flex-wrap gap-3">
                {currentInnovation.supportingDocument.map((doc: any) => (
                  <a
                    key={doc?.id}
                    href={encodeURI(process.env._AWS_END_POINT + '/' + doc?.filename)}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-2 bg-white px-3 py-2 rounded-lg shadow-sm hover:shadow-md transition-shadow"
                  >
                    <span className="text-red-500">📄</span>
                    <span className="text-sm font-medium text-gray-700">{doc?.filename}</span>
                  </a>
                ))}
              </div>
            </DescriptionSection>
          )}
        <DescriptionSection
          title="Read More"
          image="/images/icons/globe-with-arrow-svgrepo-com (1).svg"
        >
          <div className="flex flex-wrap gap-3">
            {currentInnovation.link && (
              <a
                href={
                  currentInnovation.link.startsWith('http')
                    ? currentInnovation.link
                    : `https://${currentInnovation.link}`
                }
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:text-blue-800 italic wrap-anywhere"
              >
                {currentInnovation.link}
              </a>
            )}
          </div>
        </DescriptionSection>
      </div>
    </div>
  );
}
