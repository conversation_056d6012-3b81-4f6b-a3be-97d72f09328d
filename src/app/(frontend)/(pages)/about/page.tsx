import { Metadata } from 'next';
import { cookies } from 'next/headers';
import { Suspense } from 'react';
import GlassCard from '../../components/Cards/glassCard';
import AboutUsComponent from '../../components/UI/aboutUsComponent';
import { PageLoadingFallBack } from '../../components/UI/loadingFallBackComponents';
import { fetchAboutUsContent } from '../../utils/apiClient';
import { serializeRichText } from '../../utils/functions';
import { getPageMeta } from '../../utils/metadata';

export const generateMetadata = (): Metadata => {
  return {
    ...getPageMeta('/about'),
    openGraph: {
      ...getPageMeta('/about'),
      url: 'https://www.atlasoftbinnovations.in/about',
      siteName: 'Atlas of Innovations',
      type: 'website',
    } as any,
  };
};
export default async function AboutUs() {
  const data = await fetchAboutUsContent();
  const cookieStore = await cookies();
  const content = {
    ...data,
    description: serializeRichText(data?.description),
  };

  return (
    <Suspense fallback={<PageLoadingFallBack pathname="/about" />}>
      <GlassCard
        title={content?.heroTitle || ''}
        description={content?.heroDescription || ''}
        pathname="/about"
        isAuthenticated={Boolean(cookieStore.get('payload-token')?.value)}
      />
      <AboutUsComponent content={content} />
    </Suspense>
  );
}
