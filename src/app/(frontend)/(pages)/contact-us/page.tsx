// src/app/(frontend)/(pages)/contact-us/page.tsx

import { Metadata } from 'next';
import { cookies } from 'next/headers';
import GlassCard from '../../components/Cards/glassCard';
import { fetchContsctUsUi } from '../../utils/apiClient';
import { getPageMeta } from '../../utils/metadata';
import ContactForm from './ContactForm';

export const generateMetadata = (): Metadata => {
  return {
    ...getPageMeta('/contact-us'),
    openGraph: {
      ...getPageMeta('/contact-us'),
      url: 'https://www.atlasoftbinnovations.in/contact-us',
      siteName: 'Atlas of Innovations',
      type: 'website',
    } as any,
  };
};
export default async function ContactUs() {
  const contactData = await fetchContsctUsUi();
  const cookieStore = await cookies();
  if (!contactData) {
    return <div>Error loading contact details. Please try again later.</div>;
  }

  return (
    <div>
      <GlassCard
        title={contactData.contactTitle}
        description={contactData.contactSubtitle}
        pathname="/contact-us"
        isAuthenticated={Boolean(cookieStore.get('payload-token')?.value)}
      />
      <div className="flex items-center max-w-[95vw] sm:max-w-[80vw] mx-auto justify-center">
        <div className="bg-gray-100 items-center justify-center  p-8 rounded-2xl my-8 w-full">
          <div className="max-w-[95vw] sm:max-w-[80vw] mx-auto p-6">
            <div className="flex flex-col md:flex-row justify-between">
              <div className="md:w-1/2 mb-8 md:mb-0">
                <div className="mb-6">
                  <h2 className="text-2xl font-bold">Email</h2>
                  <p className="text-lg mt-2">{contactData.email}</p>
                </div>
                <div className="mb-6">
                  <h2 className="text-2xl font-bold">Phone</h2>
                  <p className="text-lg mt-2">{contactData.phone}</p>
                </div>
                <div className="mb-6">
                  <h2 className="text-2xl font-bold">Address</h2>
                  <p className="text-lg mt-2" style={{ whiteSpace: 'pre-line' }}>
                    {contactData.address}
                  </p>
                </div>
              </div>
              {/* Render the client component */}
              <ContactForm />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
