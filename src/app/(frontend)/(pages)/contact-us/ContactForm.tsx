'use client'; // This makes it a client component

import { useState } from 'react';
import { TextArea, TextInput } from '../../components/Forms/InputComponent';
import Button from '../../components/UI/Button';
import Toast from '../../components/UI/Toast';

export default function ContactForm() {
  const [formData, setFormData] = useState({ email: '', message: '' });
  const [errors, setErrors] = useState({ email: '', message: '' });
  const [touched, setTouched] = useState<{ [key: string]: boolean }>({});
  const [toast, setToast] = useState<{
    message: string;
    type: 'success' | 'error';
  } | null>(null);
  const handleChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
    setErrors((prev) => ({ ...prev, [name]: '' }));
  };

  const handleBlur = (key: string) => {
    setTouched({ ...touched, [key]: true });
  };

  const validateForm = () => {
    let valid = true;
    const newErrors = { email: '', message: '' };

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required.';
      valid = false;
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Enter a valid email.';
      valid = false;
    }

    if (!formData.message.trim()) {
      newErrors.message = 'Message cannot be empty.';
      valid = false;
    }

    setErrors(newErrors);
    return valid;
  };

  const handleSubmit = () => {
    if (!validateForm()) return;
    setToast({
      type: 'success',
      message: 'Message sent successfully!',
    });
    setFormData({ email: '', message: '' });
  };

  return (
    <div className="md:w-1/2">
      <form onSubmit={handleSubmit} className="space-y-4">
        <TextInput
          label="Email"
          name="email"
          errorMessage={errors.email}
          value={formData.email}
          onBlur={() => handleBlur('email')}
          touched={touched.email}
          type="email"
          className="bg-Pure-White-FFFFFF"
          placeholder="Enter your Email"
          onChange={(v) => handleChange('email', v)}
          required
        />
        <TextArea
          label="Message"
          name="message"
          onBlur={() => handleBlur('message')}
          touched={touched.message}
          errorMessage={errors.message}
          value={formData.message}
          placeholder="Enter your Message"
          onChange={(v) => handleChange('message', v)}
          className="bg-Pure-White-FFFFFF"
          required
        />
        <div className="flex flex-col">
          <Button title="Submit" onClick={handleSubmit} variant="gradient" />
        </div>
      </form>
      {toast && <Toast message={toast.message} type={toast.type} onClose={() => setToast(null)} />}
    </div>
  );
}
