'use client';

import Image from 'next/image';
import { useSearchParams } from 'next/navigation';
import { Suspense, useState } from 'react';
import * as yup from 'yup';
import Button from '../../components/UI/Button';
import CircularLoader from '../../components/UI/CircularLoader';

// ✅ Yup Schema Validation
const schema = yup.object().shape({
  password: yup
    .string()
    .min(6, 'Password must be at least 6 characters')
    .required('Password is required'),
  confirmPassword: yup
    .string()
    .oneOf([yup.ref('password')], 'Passwords do not match')
    .required('Confirm Password is required'),
});

export default function ResetPasswordComponent() {
  const searchParams = useSearchParams();
  const token = searchParams.get('token');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [errors, setErrors] = useState<{ password?: string; confirmPassword?: string }>({});
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  const handleSubmit = async () => {
    setErrors({}); // Clear previous errors
    setMessage('');

    // ✅ Validate inputs manually with Yup
    try {
      schema.validateSync({ password, confirmPassword }, { abortEarly: false });
    } catch (validationError) {
      const errorObj: { password?: string; confirmPassword?: string } = {};
      (validationError as yup.ValidationError).inner.forEach((err) => {
        if (err.path) errorObj[err.path as 'password' | 'confirmPassword'] = err.message;
      });
      setErrors(errorObj);
      return;
    }

    if (!token) {
      setMessage('❌ Invalid or missing token.');
      return;
    }

    setLoading(true);

    try {
      const res = await fetch(
        `${process.env.NEXT_PUBLIC_BACKEND_URL}/api/appUsers/reset-password`,
        {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ token, password }),
        },
      );

      const responseData = await res.json();

      if (res.ok) {
        setMessage('✅ Password reset successful! You can now log in.');
        setTimeout(() => {
          window.location.href = '/?authModal=true';
        }, 2000);
      } else {
        setMessage(
          `❌ ${responseData.error || responseData.errors?.[0]?.message || 'Something went wrong.'}`,
        );
      }
    } catch (error) {
      setMessage('❌ Failed to reset password. Try again.');
    }

    setLoading(false);
  };

  return (
    <Suspense fallback={<CircularLoader />}>
      <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200">
        <div className="w-full max-w-md rounded-lg bg-white p-6 shadow-xl">
          <h2 className="mb-4 text-center text-2xl font-bold text-gray-700">Reset Password</h2>
          <div className="space-y-4">
            <div>
              <input
                type="password"
                placeholder="New Password"
                className="w-full rounded-lg border px-4 py-2 outline-none focus:ring-2 focus:ring-indigo-400"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
              {errors.password && <p className="text-sm text-red-600">{errors.password}</p>}
            </div>

            <div>
              <input
                type="password"
                placeholder="Confirm Password"
                className="w-full rounded-lg border px-4 py-2 outline-none focus:ring-2 focus:ring-indigo-400"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
              />
              {errors.confirmPassword && (
                <p className="text-sm text-red-600">{errors.confirmPassword}</p>
              )}
            </div>

            <Button
              size="sm"
              variant="gradient"
              disabled={loading}
              onClick={handleSubmit}
              title={loading ? 'Resetting...' : 'Reset Password'}
              className="mt-3 w-full"
              rightIcon={
                <Image
                  alt="arrowIcon of Innovations"
                  className="w-5 h-5 bg-white p-[5px] rounded-full"
                  height={20}
                  width={20}
                  src={'/images/icons/darkArrowIcon.svg'}
                />
              }
            />
          </div>
          {message && <p className="mt-4 text-center text-sm text-red-600">{message}</p>}
        </div>
      </div>
    </Suspense>
  );
}
