const baseurl = process.env.NEXT_PUBLIC_BACKEND_URL;

export const ROUTES = {
  HOME: '/',
  BROWSE_INNOVATIONS: '/browse-innovations',
  MY_INNOVATIONS: '/my-innovations',
  ABOUT: '/about',
  HOW_TO_SUBMIT_INNOVATION: '/how-to-submit',
  INNOVATION: '/innovation',
  INNOVATION_CLASS: '/innovation-class',
  LEARN_MORE: '/learn-more',
  HOW_TO_SUB_AN_INNOVATION: '/how-to-submit/submit-an-innovation',
  FAQ: '/faq',
  CONTACT_US: '/contact-us',
  SUBMIT_AN_INNOVATION: '/submit-an-innovation',
};
export const API_ENDPOINTS = {
  ME: `${baseurl}/api/appUsers/me`,
  SUBMIT_INNOVATION_CONTENT: `${baseurl}/api/globals/submit-innovation-content`,
  SUBMIT_CRITERIA: `${baseurl}/api/submit-criteria`,
  ABOUT_CONTENT: `${baseurl}/api/globals/aboutUs-content`,
  FLOW: `${baseurl}/api/innovationClass/flow`,
  TOP_4: `${baseurl}/api/innovations/top4`,
  LOGOUT: `${baseurl}/api/appUsers/logout`,
  TOTAL_INNOVATION: `${baseurl}/api/innovations/total`,
};
