import { Edge, Node } from '@xyflow/react';
import { ErrorInfo, ReactNode } from 'react';

export type InnovationDetails = {
  id: string;
  title: string;
  description: string;
  currentStageOfDevelopment: string | null;
  category: string[];
  interventionType: string[];
  status: string;
  name: string;
  email: string;
  supportingDocument: any | null;
  phoneNo: string;
  link: string;
  organizationName: string;
  declaration: boolean;
};

export type SubmitContentType = {
  heroTitle: string;
  heroDescription: string;
  cardTitle: string;
  cardDescription: string;
  criteria?: ApplicationProcessCardProps[] | string[];
};

export type aboutUsContent = {
  heroTitle: string;
  heroDescription: string;
  description: any;
};

export interface FlowItem {
  id: string;
  type: string;
  name: string;
  processFrom?: { id: string; name: string };
  processTo?: { id: string; name: string };
  parent: FlowItem;
  processName: string;
  innovationClass?: string;
}
export interface ApplicationProcessCardProps {
  number: number;
  title: string;
  items: Array<{
    text: string;
  }>;
}
export type ValidationError = {
  data: {
    collection: string;
    errors: {
      label: string;
      message: string;
      path: string;
    }[];
  };
  isOperational: boolean;
  isPublic: boolean;
  status: number;
  name: string;
};

export interface Innovation {
  declaration?: boolean;
  mapId?: string;
  id: string;
  title: string;
  description?: string;
  currentStageOfDevelopment?: string | null;
  currentStageOfDevelopmentSlug?: string | null;
  category?: string[];
  status?: string | 'pending' | 'approved' | 'rejected';
  name?: string;
  email?: string;
  currentStageOfDevelopmentTaxonomy?: string;
  phoneNo?: string;
  link?: string;
  supportingDocument?: any;
  organizationName?: string;
  interventionType?: string[];
  bannerImage?: { id: string; name: string } | null;
  slug: string;
  innovationClass?: { name?: string; slug?: string };
}

export interface FilterOption {
  id: string;
  value: string;
  fieldName: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface FilterOptions {
  categories: FilterOption[];
  interventionTypes: FilterOption[];
  stages: FilterOption[];
  innovationClass: FilterOption[];
}

export interface PaginationData {
  docs: Innovation[];
  totalDocs: number;
  limit: number;
  page: number;
  totalPages: number;
  hasPrevPage: boolean;
  hasNextPage: boolean;
  prevPage: number | null;
  nextPage: number | null;
}

export interface InnovationsHero {
  title: string;
  description: any;
  backgroundImage: { url: string };
}

export interface FAQProps {
  faqData: {
    title: string;
    subtitle: string;
    faqs: { question: string; answer: string }[];
  };
}

export interface LearnMoreHero {
  cardTitle: string;
  cardDescription: any; // Raw Slate JSON from Payload
  backgroundImage: {
    url: string;
  };
}

// Appended types from 'src/app/(frontend)/components' directory

// Extracted from: components/ErrorBoundary.tsx (Match 1/3)
export type FallbackProps = {
  error: Error;
  resetErrorBoundary: () => void;
};

// Extracted from: components/ErrorBoundary.tsx (Match 2/3)
export type ErrorBoundaryProps = {
  fallback?: ReactNode;
  FallbackComponent?: React.ComponentType<FallbackProps>;
  fallbackRender?: (props: FallbackProps) => ReactNode;
  onError?: (error: Error, info: ErrorInfo) => void;
  onReset?: () => void;
  children: any;
};

// Extracted from: components/ErrorBoundary.tsx (Match 3/3)
export type ErrorBoundaryState = { hasError: boolean; error: Error | null };

// Extracted from: components/UI/navBar.tsx (Match 1/1)
export interface HeaderProps {
  color: string;
  pathname: string;
  isWhiteBg?: boolean;
  isAuthenticated: boolean;
}

// Extracted from: components/UI/atlasofinnovators.tsx (Match 1/3)
export interface InnovationCard {
  id: string;
  title: string;
  organizationName: string;
  description: string;
  slug: string;
}

// Extracted from: components/UI/atlasofinnovators.tsx (Match 2/3)
export interface LandingPageContent {
  heroSection: {
    heading: string;
    description: string;
    heroImage: string;
    sliderImages: string[];
  };
  mapSection: {
    title: string;
    description: string;
  };
  featuredInnovationsSection: {
    title: string;
    description: string;
  };
  marqueeContent: Array<{ id?: string; text: string }>;
}

// Extracted from: components/UI/atlasofinnovators.tsx (Match 3/3)
export interface AtlasOfInnovationsProps {
  featuredInnovations: Innovation[];
  innovationCount: number;
  landingPageContent: LandingPageContent;
}

// Extracted from: components/UI/Toast.tsx (Match 1/1)
export interface ToastProps {
  message: string;
  type?: 'success' | 'error' | 'info';
  duration?: number;
  onClose: () => void;
}

// Extracted from: components/UI/FlowChart.tsx (Match 1/2)

// Extracted from: components/UI/FlowChart.tsx (Match 2/2)
export type LayoutElements = {
  nodes: Node[];
  edges: Edge[];
};

// Extracted from: components/UI/SunburstChart.tsx (Match 1/1)
export type SunburstNode = {
  name: string;
  value?: number;
  children?: SunburstNode[];
  innovationCount?: number;
  icon?: string;
};

// Extracted from: components/UI/ImageCarousel.tsx (Match 1/1)
export interface ImageCarouselProps {
  backgroundImages: string[];
  interval?: number;
}

// Extracted from: components/UI/Button.tsx (Match 1/1)
export type ButtonProps = {
  title: string;
  onClick?: () => void;
  className?: string;
  variant?: 'gradient' | 'outline' | 'transparent' | 'solid';
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  solidColor?: string;
  disabled?: boolean;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  loading?: boolean;
  loadingText?: string;
  type?: 'button' | 'submit' | 'reset';
};

// Extracted from: components/Cards/InnovationSectionCard.tsx (Match 1/1)
export interface MapOfInnovationsProps {
  title: string;
  description: string;
  buttonText: string;
  ButtonHref: string;
  onButtonClick?: () => void;
  imageUrl: string;
}

// Extracted from: components/Cards/Card.tsx (Match 1/1)
export type CardProps = {
  children: React.ReactNode;
  className: string;
};
export interface Filters {
  category: any[];
  interventionType: any[];
  currentStageOfDevelopment: any[];
  myInnovations: boolean;
  innovationClass: any[]; // Add innovationClass to default filters
}
export interface FilterComponentProps {
  filters: Filters;
  setFilters: React.Dispatch<React.SetStateAction<Filters>>;
  filterOptions: FilterOptions;
  setFlowmap?: any;
  hideMyInnovationsFilter?: boolean;
  setSearchQuery: (value: string) => void;
  searchQuery: string;
}

// Extracted from: components/Cards/glassCard.tsx (Match 1/1)
export interface GlassCardProps {
  title: string;
  description: string;
  pathname: string;
  isInnerHTML?: boolean;
  isAuthenticated: boolean;
  bannerImage?: {
    id: string;
    name: string;
  };
}

// Extracted from: components/Cards/GradientWrapper.tsx (Match 1/1)
export type GradientWrapperProps = {
  contentWidth?: string;
  gradient?: boolean;
  className?: string; // Optional additional classes for the outer container
  children: React.ReactNode; // The content to be rendered above the gradient
};
export type PositionType =
  | 'cursor'
  | 'cursor-top'
  | 'cursor-bottom'
  | 'left'
  | 'right'
  | 'top'
  | 'cursor-center'
  | 'bottom'
  | 'cursor-left'
  | 'cursor-right'
  | 'cursor-top-left'
  | 'cursor-top-right'
  | 'cursor-bottom-left'
  | 'cursor-bottom-right'
  | 'top-left'
  | 'top-right'
  | 'bottom-left'
  | 'bottom-right'
  | 'static-top-left'
  | 'static-top-right'
  | 'static-bottom-left'
  | 'static-bottom-right'
  | 'static-center'
  | 'right-center'
  | 'top-center'
  | 'bottom-center'
  | 'left-center'
  | 'center';

// Extracted from: components/Cards/marqueeStrip.tsx (Match 1/1)
export interface MarqueeStripProps {
  stripContent: Array<{ id?: string; text: string }>;
  itsBlue?: boolean;
}

// Extracted from: components/Cards/InnovationCards.tsx (Match 1/1)
export interface InnovationCardsProps {
  innovations: Innovation[];
  loader: boolean;
  isMyInnovations?: boolean;
}

// Extracted from: components/Forms/InputComponent.tsx (Match 1/9)
export interface BaseInputProps {
  label: string;
  name: string;
  required?: boolean;
  errorMessage?: string; // Customizable error message
  children: React.ReactNode; // The actual input element (e.g., text, radio, select)
  className?: string; // For Tailwind CSS customization
}

// Extracted from: components/Forms/InputComponent.tsx (Match 2/9)
export interface TextInputProps {
  label: string;
  name: string;
  className?: string;
  value?: string;
  type?: React.HTMLInputTypeAttribute | undefined;
  placeholder?: string;
  onChange: (value: string) => void;
  onBlur?: (event: React.FocusEvent<HTMLInputElement>) => void;
  onKeyDown?: (event: React.KeyboardEvent) => void;
  touched?: boolean;
  required?: boolean;
  disabled?: boolean;
  errorMessage?: string | undefined | null;
}

// Extracted from: components/Forms/InputComponent.tsx (Match 3/9)
export interface TextAreaProps {
  label: string;
  name: string;
  className?: string;
  value?: string;
  placeholder?: string;
  onChange: (value: string) => void;
  required?: boolean;
  errorMessage?: string;
  onBlur: (event: React.FocusEvent<HTMLInputElement>) => void;
  touched?: boolean;
}

// Extracted from: components/Forms/InputComponent.tsx (Match 4/9)
export interface RadioInputProps {
  label: string;
  name: string;
  options: { label: string; value: string }[];
  value: string;
  onChange: (value: string) => void;
  required?: boolean;
  errorMessage?: string;
  onBlur: (event: React.FocusEvent<HTMLInputElement>) => void;
  touched?: boolean;
}

// Extracted from: components/Forms/InputComponent.tsx (Match 5/9)
export interface SingleSelectProps {
  label: string;
  name: string;
  placeholder?: string;
  className?: string;
  options: {
    id: string;
    value: string;
    order: number;
    label?: string;
    description?: string;
    slug?: string;
  }[];
  value:
    | string
    | { id: string; value: string; label?: string; description?: string; slug?: string };
  onChange: (value: any) => void;
  required?: boolean;
  errorMessage?: string;
}

// Extracted from: components/Forms/InputComponent.tsx (Match 6/9)
export interface MultiSelectProps {
  label: string;
  className?: string;
  name: string;
  options: { label: string; value: string }[];
  value: string[];
  onChange: (value: string[]) => void;
  required?: boolean;
  errorMessage?: string;
}

// Extracted from: components/Forms/InputComponent.tsx (Match 7/9)
export interface NewOption {
  value: string;
  label: string;
  id?: string;
  description?: string;
  slug?: string;
  order: number;
}

// Extracted from: components/Forms/InputComponent.tsx (Match 8/9)
export interface NewMultiSelectProps {
  label: string;
  name: string;
  onChange: (selected: NewOption[]) => void;
  options: NewOption[];
  value: NewOption[];
  errorMessage?: string;
  required?: boolean;
  className?: string;
  placeholder?: string;
  shouldShowReadMore?: boolean;
}

// Extracted from: components/Forms/InputComponent.tsx (Match 9/9)
export interface FileUploadProps {
  label: string;
  name: string;
  loading?: boolean;
  value?: (File | { id: string; name: string })[];
  onChange: (value: (File | { id: string; name: string })[]) => void;
  onRemove: (index: number) => void;
  required?: boolean;
  errorMessage?: string;
}

// Extracted from: components/Forms/InnovationPreview.tsx (Match 1/1)
export interface ParsedData {
  options: any;
  [key: string]: FormDataProps | any;
}

// Extracted from: components/Forms/InnovationFormOrignal.tsx (Match 1/3)
export interface NewOption {
  value: string;
  label: string;
}

// Extracted from: components/Forms/InnovationFormOrignal.tsx (Match 2/3)
export interface FormDataProps {
  id?: string;
  title: string;
  description: string;
  name: string;
  email: string;
  phoneNo: string;
  organizationName: string;
  currentStageOfDevelopment: {
    id: string;
  };
  interventionType: NewOption[];
  link: string;
  supportingDocument: (File | undefined)[];
  declaration: boolean;
}

// Extracted from: components/Forms/InnovationFormOrignal.tsx (Match 3/3)
export interface FilteredFormData {
  interventionType: string[];
  title: string;
  description: string;
  name: string;
  email: string;
  phoneNo: string;
  organizationName: string;
  currentStageOfDevelopment: string;
  link: string;
  supportingDocument: (File | undefined)[];
  declaration: boolean;
}

// Extracted from: components/Forms/RichTextRenderer.tsx (Match 1/1)
export interface RichTextInputProps {
  label: string;
  name: string;
  value?: string;
  placeholder?: string;
  onChange: (value: string) => void;
  required?: boolean;
  touched?: boolean;
  errorMessage?: string | null;
  className?: string;
}

// Extracted from: components/InovationDetails/InsightDetails.tsx (Match 1/4)
export interface DetailItemProps {
  label: string;
  value?: any;
  textColor?: string;
  isLink?: boolean;
}

// Extracted from: components/InovationDetails/InsightDetails.tsx (Match 2/4)
export interface SupportingDocument {
  id: string;
  filename: string;
  filesize: number;
  mimeType: string;
  url: string;
  thumbnailURL: string | null;
  createdAt: string;
  updatedAt: string;
}

// Extracted from: components/InovationDetails/InsightDetails.tsx (Match 3/4)
export interface InnovationDetailsProps {
  id: string;
  title: string;
  description: string;
  currentStageOfDevelopment: string | null;
  category: string[];
  interventionType: string[];
  status: string;
  name: string;
  email: string;
  phoneNo: string;
  link: string;
  supportingDocument: SupportingDocument[] | null;
  organizationName: string;
  declaration: boolean;
}

// Extracted from: components/InovationDetails/InsightDetails.tsx (Match 4/4)
export interface InsightsDetailsProps {
  innovationDetails: InnovationDetailsProps;
}

// Extracted from: components/InovationDetails/InsightsLessons.tsx (Match 1/1)
export interface InsightsLessonsProps {
  innovationDetails: InnovationDetails;
  className?: string; // Add className prop
}

// Extracted from: components/InovationDetails/KeywordsInsights.tsx (Match 1/1)
export interface KeywordsInsightsProps {
  innovationDetails: InnovationDetails;
}

// Extracted from: components/InovationDetails/SummaryOfInovation.tsx (Match 1/1)
export interface SummaryOfInnovationProps {
  description: string;
}

// Extracted from: components/modals/authModal.tsx (Match 1/2)
export type ToastType = 'success' | 'error' | 'info';
export type ToastFunProps = {
  message: string;
  type: ToastType;
};

// Extracted from: components/modals/authModal.tsx (Match 2/2)
export type AuthModalProps = {
  isOpen: boolean;
  onClose: () => void;
  setToast: (toast: ToastProps) => void;
};

// Extracted from: components/Layout/Header.tsx (Match 1/1)

export interface authApiResponse {
  user?: {
    id: string;
    email: string;
    name: string;
  };
  doc?: {
    id: string;
    email: string;
    name: string;
  };
  errors?: Array<{
    message: string;
    data?: {
      errors?: Array<{
        path: string;
        message: string;
      }>;
    };
  }>;
  message?: string;
}

export type AtlasInfo = {
  email?: string;
  name?: string;
  userId?: string;
};

export type Option = {
  value: string;
  label: string;
  id: string;
  description?: string;
  slug?: string;
};
