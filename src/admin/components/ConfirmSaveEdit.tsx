'use client';
import { useDocumentInfo, useWatchForm } from '@payloadcms/ui';
import dynamic from 'next/dynamic';
import { usePathname } from 'next/navigation';
import React, { useCallback, useEffect, useState } from 'react';
import { usePayloadTheme } from './VersionCompare';
const ModalPortal = dynamic(() => import('./ModalPortal'), { ssr: false });

const VersionCreateModal = ({
  isOpen,
  onClose,
  onConfirm,
  onSkip,
  innovationTitle,
}: {
  isOpen: boolean;
  onSkip: () => void;
  onClose: () => void;
  onConfirm: (remark: string, shouldApprove: boolean) => void;
  innovationTitle: string;
}) => {
  const [remark, setRemark] = useState('');
  const [shouldApprove, setShouldApprove] = useState(false);
  const theme = usePayloadTheme();
  const isDark = theme === 'dark';

  useEffect(() => {
    if (isOpen) {
      setRemark('');
      setShouldApprove(false);
    }
  }, [isOpen]);

  const styles = {
    modalOverlay: {
      position: 'fixed' as const,
      inset: 0,
      backgroundColor: 'rgba(0,0,0,0.75)',
      backdropFilter: 'blur(4px)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
    },
    modalContent: {
      backgroundColor: isDark ? '#111827' : 'white',
      color: isDark ? '#e5e7eb' : '#374151',
      padding: 24,
      borderRadius: 8,
      boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.3)',
      width: '100%',
      maxWidth: 500,
      position: 'relative' as const,
    },
    closeButton: {
      position: 'absolute' as const,
      top: 8,
      right: 12,
      background: 'transparent',
      border: 'none',
      fontSize: 16,
      color: isDark ? '#9ca3af' : '#6b7280',
      cursor: 'pointer',
    },
    title: { fontSize: 18, fontWeight: 'bold', marginBottom: 16 },
    description: { marginBottom: 20, lineHeight: 1.5 },
    label: { display: 'block', marginBottom: 8, fontWeight: 500 },
    textarea: {
      width: '100%',
      minHeight: 100,
      padding: 10,
      resize: 'vertical' as const,
      borderRadius: 4,
      border: `1px solid ${isDark ? '#4b5563' : '#d1d5db'}`,
      backgroundColor: isDark ? '#1f2937' : 'white',
      color: isDark ? '#e5e7eb' : '#374151',
      marginBottom: 20,
    },
    buttonContainer: { display: 'flex', justifyContent: 'flex-end', gap: 12 },
    skipButton: {
      padding: '8px 16px',
      borderRadius: 4,
      border: `1px solid ${isDark ? '#4b5563' : '#d1d5db'}`,
      background: 'transparent',
      color: isDark ? '#e5e7eb' : '#374151',
      cursor: 'pointer',
    },
    confirmButton: {
      padding: '8px 16px',
      borderRadius: 4,
      border: 'none',
      backgroundColor: '#3b82f6',
      color: 'white',
      cursor: 'pointer',
    },
    disabledButton: { opacity: 0.5, cursor: 'not-allowed' },
    checkboxContainer: {
      display: 'flex',
      alignItems: 'center',
      gap: '8px',
      marginBottom: '20px',
    },
    checkbox: {
      cursor: 'pointer',
    },
    checkboxLabel: {
      cursor: 'pointer',
      color: isDark ? '#e5e7eb' : '#374151',
    },
  };

  return (
    <ModalPortal isOpen={isOpen}>
      <div style={styles.modalOverlay}>
        <div style={styles.modalContent}>
          <button type="button" style={styles.closeButton} onClick={onClose}>
            ✕
          </button>
          <h2 style={styles.title}>Create a Version?</h2>
          <p style={styles.description}>
            Do you want to create a new version for <strong>{innovationTitle}</strong>?<br />
            Add a remark to create one, or skip to just save your changes.
          </p>
          <label style={styles.label}>Change Remark:</label>
          <textarea
            placeholder="Describe what changes you made..."
            style={styles.textarea}
            value={remark}
            onChange={(e) => setRemark(e.target.value)}
          />
          <div style={styles.checkboxContainer}>
            <input
              type="checkbox"
              id="approveVersion"
              checked={shouldApprove}
              onChange={(e) => setShouldApprove(e.target.checked)}
              style={styles.checkbox}
            />
            <label htmlFor="approveVersion" style={styles.checkboxLabel}>
              Approve this version
            </label>
          </div>
          <div style={styles.buttonContainer}>
            <button type="submit" style={styles.skipButton} onClick={onSkip}>
              Skip
            </button>
            <button
              style={{
                ...styles.confirmButton,
                ...(remark.trim() === '' ? styles.disabledButton : {}),
              }}
              disabled={remark.trim() === ''}
              onClick={() => onConfirm(remark, shouldApprove)}
            >
              Create Version
            </button>
          </div>
        </div>
      </div>
    </ModalPortal>
  );
};

export default function ConfirmSaveEdit(props: any) {
  const form = useWatchForm();
  const pathname = usePathname();
  const id = pathname?.split('/').pop();
  const data = form?.getData() || {};
  const [showModal, setShowModal] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const initialData = useDocumentInfo()?.initialData;

  const handleClick = useCallback(
    (e: React.MouseEvent) => {
      if (!id) {
        e.currentTarget.setAttribute('type', 'submit');
        props.onClick?.(e);
        return;
      }
      setShowModal(true);
    },
    [id, props],
  );

  const handleSkip = useCallback(() => {
    setShowModal(false);
    form.submit(); // Save without version
  }, [props]);

  const handleCloseModal = useCallback(() => {
    setShowModal(false);
  }, [props]);

  const handleCreateVersion = async (remark: string, shouldApprove: boolean) => {
    setIsCreating(true);

    try {
      const query = new URLSearchParams({
        'where[innovation][equals]': id as string,
        limit: '1',
        sort: '-versionNumber',
      });

      const res = await fetch(
        `${process.env.NEXT_PUBLIC_BACKEND_URL}/api/project-versions?${query}`,
      );
      const json = await res.json();
      const latest = json.docs[0]?.versionNumber || 0;

      const createRes = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/project-versions`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...data,
          innovation: id,
          remark,
          // innovationClass: null,
          versionNumber: latest + 1,
          status: shouldApprove ? 'approved' : 'pending',
        }),
      });

      if (!createRes.ok) {
        console.error('Failed to create version:', await createRes.text());
      } else {
        form.reset(shouldApprove ? data : initialData);
      }
    } catch (err) {
      console.error('Error saving or creating version:', err);
    } finally {
      setIsCreating(false);
      setShowModal(false);
      window?.location?.reload();
    }
  };

  const title = data?.title || 'this innovation';

  return (
    <>
      <button type="button" onClick={handleClick} disabled={props.disabled || isCreating}>
        {isCreating ? 'Creating Version...' : 'Save'}
      </button>
      <VersionCreateModal
        isOpen={showModal}
        onClose={handleCloseModal}
        onSkip={handleSkip}
        onConfirm={handleCreateVersion}
        innovationTitle={title}
      />
    </>
  );
}
