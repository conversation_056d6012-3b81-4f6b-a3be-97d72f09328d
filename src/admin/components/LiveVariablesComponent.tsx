'use client';
import { useWatchForm } from '@payloadcms/ui';
import React from 'react';

const variableMap: Record<string, string[]> = {
  'verify-email': ['verifyUrl'],
  'welcome-email': ['name', 'dashboardLink'],
  'forgot-password': ['resetUrl'],
  'submitted-innovation': ['name', 'title', 'submissionDate'],
  'innovation-approved': ['name', 'title', 'submissionDate', 'link'],
  'innovation-rejected': ['name', 'title', 'submissionDate', 'feedback', 'editLink'],
};

const LiveVariablesComponent: React.FC = () => {
  const form = useWatchForm();
  const trigger = form?.fields?.trigger?.value;

  const variables = variableMap[trigger as string] || [];

  return (
    <div className="p-2 border rounded bg-gray-50">
      <strong className="block mb-2">Available Variables:</strong>
      {variables.length > 0 ? (
        <ul className="list-disc list-inside">
          {variables.map((v) => (
            <li key={v}>
              <code className="bg-white px-1 py-0.5 border rounded text-sm text-blue-700">
                {'{{' + v + '}}'}
              </code>
            </li>
          ))}
        </ul>
      ) : (
        <p className="text-sm text-gray-500">Select a trigger to see variables.</p>
      )}
    </div>
  );
};

export default LiveVariablesComponent;
