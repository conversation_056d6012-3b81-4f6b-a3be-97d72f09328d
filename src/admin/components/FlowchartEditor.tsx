'use client';
import { useField } from '@payloadcms/ui';
import React, { useCallback, useEffect, useState } from 'react';
import ReactFlow, {
  addEdge,
  Background,
  Connection,
  Controls,
  Edge,
  Handle,
  Position,
  ReactFlowProvider,
  useEdgesState,
  useNodesState,
} from 'reactflow';
import 'reactflow/dist/style.css';

// Editable Custom node component
const CustomNode = ({ data, id }: { data: any; id: string }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(data.label);
  const [isTypeEditing, setIsTypeEditing] = useState(false);

  // Update editValue when data.label changes
  useEffect(() => {
    setEditValue(data.label);
  }, []);

  const handleLabelEdit = () => {
    setIsEditing(true);
  };

  const handleLabelSave = () => {
    if (editValue.trim()) {
      data.onUpdate(id, { label: editValue.trim() });
    }
    setIsEditing(false);
  };

  const handleLabelCancel = () => {
    setEditValue(data.label);
    setIsEditing(false);
  };

  const handleTypeChange = (newType: string) => {
    data.onUpdate(id, { type: newType });
    setIsTypeEditing(false);
  };

  const handleDelete = () => {
    if (confirm('Are you sure you want to delete this node?')) {
      data.onDelete(id);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleLabelSave();
    } else if (e.key === 'Escape') {
      handleLabelCancel();
    }
  };

  return (
    <div className="p-3 border border-gray-200 rounded-lg bg-white shadow-md min-w-[150px] relative group">
      {/* Delete button - shows on hover */}
      <button
        onClick={handleDelete}
        className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center hover:bg-red-600"
        title="Delete node"
      >
        ×
      </button>

      {/* Node label - editable */}
      <div className="text-sm font-medium mb-1">
        {isEditing ? (
          <input
            type="text"
            value={editValue}
            onChange={(e) => setEditValue(e.target.value)}
            onBlur={handleLabelSave}
            onKeyDown={handleKeyPress}
            className="w-full px-1 py-0.5 border border-blue-300 rounded text-sm focus:outline-none focus:border-blue-500"
            autoFocus
          />
        ) : (
          <div
            onClick={handleLabelEdit}
            className="cursor-pointer hover:bg-gray-50 px-1 py-0.5 rounded"
            title="Click to edit"
          >
            {data.label}
          </div>
        )}
      </div>

      {/* Node type - editable dropdown */}
      <div className="text-xs text-gray-500">
        {isTypeEditing ? (
          <select
            value={data.type}
            onChange={(e) => handleTypeChange(e.target.value)}
            onBlur={() => setIsTypeEditing(false)}
            className="w-full text-xs border border-gray-300 rounded px-1 py-0.5 focus:outline-none focus:border-blue-500"
            autoFocus
          >
            <option value="Stage">Stage</option>
            <option value="Process">Process</option>
            <option value="Innovation">Innovation</option>
          </select>
        ) : (
          <div
            onClick={() => setIsTypeEditing(true)}
            className="cursor-pointer hover:bg-gray-50 px-1 py-0.5 rounded"
            title="Click to change type"
          >
            {data.type}
          </div>
        )}
      </div>

      <Handle
        type="source"
        position={Position.Bottom}
        id="process-source"
        className="w-2 h-2 bg-blue-500"
      />
      <Handle
        type="target"
        position={Position.Top}
        id="process-target"
        className="w-2 h-2 bg-blue-500"
      />
    </div>
  );
};

const nodeTypes = {
  custom: CustomNode,
};

const FlowchartEditorComponent: React.FC<{ path: string; name: string }> = () => {
  const { value, setValue } = useField<any>({ path: 'flowchart.nodes' });
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [isInitialized, setIsInitialized] = useState(false);

  // Update the field value with current nodes and edges
  const updateFieldValue = useCallback(() => {
    console.log('💾 updateFieldValue called');
    console.log('📊 Current nodes:', nodes);
    console.log('🔗 Current edges:', edges);

    // Map nodes to the format expected by the field
    const updatedNodes = nodes.map((node) => {
      const nodeEdges = edges.filter((edge) => edge.source === node.id || edge.target === node.id);

      return {
        id: node.id,
        name: node.data.label,
        type: node.data.type || 'Stage',
        position: node.position, // Save position!
        // For Process nodes, add connection information
        ...(node.data.type === 'Process' && {
          processFromId: nodeEdges.find((e) => e.target === node.id)?.source || null,
          processToId: nodeEdges.find((e) => e.source === node.id)?.target || null,
        }),
        // For Stage nodes, add parent information
        ...(node.data.type === 'Stage' && {
          parentId: nodeEdges.find((e) => e.target === node.id)?.source || null,
        }),
      };
    });

    console.log('💾 Saving nodes to field:', updatedNodes);
    setValue(updatedNodes);
  }, [nodes, edges, setValue]);

  // Handle node updates (label, type changes)
  const handleNodeUpdate = useCallback(
    (nodeId: string, updates: { label?: string; type?: string }) => {
      setNodes((nds) =>
        nds.map((node) => {
          if (node.id === nodeId) {
            return {
              ...node,
              data: {
                ...node.data,
                ...(updates.label && { label: updates.label }),
                ...(updates.type && { type: updates.type }),
              },
            };
          }
          return node;
        }),
      );

      // Update field value after a short delay to ensure state is updated
      setTimeout(() => {
        updateFieldValue();
      }, 0);
    },
    [setNodes, updateFieldValue],
  );

  // Handle node deletion
  const handleNodeDelete = useCallback(
    (nodeId: string) => {
      setNodes((nds) => nds.filter((node) => node.id !== nodeId));
      setEdges((eds) => eds.filter((edge) => edge.source !== nodeId && edge.target !== nodeId));

      // Update field value after a short delay to ensure state is updated
      setTimeout(() => {
        updateFieldValue();
      }, 0);
    },
    [setNodes, setEdges, updateFieldValue],
  );

  // Initialize from existing data or add default node
  useEffect(() => {
    // Only run once when value first becomes available
    if (isInitialized || !value) {
      return;
    }

    console.log('🔄 Initializing FlowchartEditor...');
    console.log('📊 Full value from useField (nodes array):', value);
    console.log('🔍 Value type:', typeof value, 'Is array:', Array.isArray(value));

    // Create stable callback functions for this initialization
    const stableHandleNodeUpdate = (nodeId: string, updates: { label?: string; type?: string }) => {
      setNodes((nds) =>
        nds.map((node) => {
          if (node.id === nodeId) {
            return {
              ...node,
              data: {
                ...node.data,
                ...(updates.label && { label: updates.label }),
                ...(updates.type && { type: updates.type }),
              },
            };
          }
          return node;
        }),
      );
      setTimeout(() => updateFieldValue(), 0);
    };

    const stableHandleNodeDelete = (nodeId: string) => {
      setNodes((nds) => nds.filter((node) => node.id !== nodeId));
      setEdges((eds) => eds.filter((edge) => edge.source !== nodeId && edge.target !== nodeId));
      setTimeout(() => updateFieldValue(), 0);
    };

    // Check if we have existing nodes data (value is now the nodes array directly)
    if (Array.isArray(value) && value.length > 0) {
      console.log('✅ Loading existing flowchart data:', value);

      // Convert nodes array to ReactFlow format
      const flowNodes = value.map((node: any, index: number) => ({
        id: node.id,
        type: 'custom',
        data: {
          label: node.name,
          type: node.type,
          onUpdate: stableHandleNodeUpdate,
          onDelete: stableHandleNodeDelete,
        },
        position: node.position ?? { x: 100 + index * 200, y: 100 + index * 100 },
      }));

      // Create edges based on parent/child relationships
      const flowEdges: Edge[] = [];
      value.forEach((node: any) => {
        // Handle Process nodes with processFromId and processToId
        if (node.type === 'Process' && node.processFromId && node.processToId) {
          flowEdges.push({
            id: `${node.processFromId}-${node.processToId}`,
            source: node.processFromId,
            target: node.processToId,
            label: node.name,
            type: 'default',
          });
        }
        // Handle Stage nodes with parentId
        if (node.type === 'Stage' && node.parentId && node.parentId.trim() !== '') {
          flowEdges.push({
            id: `${node.parentId}-${node.id}`,
            source: node.parentId,
            target: node.id,
            type: 'default',
          });
        }
      });

      console.log('🎯 Setting nodes:', flowNodes);
      console.log('🔗 Setting edges:', flowEdges);
      setNodes(flowNodes);
      setEdges(flowEdges);
    } else if (Array.isArray(value) && value.length === 0) {
      // Only add default node if value is an empty array
      console.log('➕ No existing nodes found, adding default node...');
      const defaultNode = {
        id: 'default-stage',
        type: 'custom',
        data: {
          label: 'Start Stage',
          type: 'Stage',
          onUpdate: stableHandleNodeUpdate,
          onDelete: stableHandleNodeDelete,
        },
        position: { x: 250, y: 150 },
      };
      setNodes([defaultNode]);
    }

    setIsInitialized(true);
  }, [value, isInitialized, updateFieldValue]);

  // Save changes back to the field
  const handleNodesChange = (changes: any) => {
    onNodesChange(changes);
    // Use setTimeout to ensure state is updated before saving
    setTimeout(() => {
      updateFieldValue();
    }, 0);
  };

  const handleEdgesChange = (changes: any) => {
    onEdgesChange(changes);
    // Use setTimeout to ensure state is updated before saving
    setTimeout(() => {
      updateFieldValue();
    }, 0);
  };

  // Handle new connections between nodes
  const onConnect = useCallback(
    (connection: Connection) => {
      // Add the new edge
      setEdges((eds) => addEdge(connection, eds));

      // Update the field value after a short delay to ensure edges state is updated
      setTimeout(() => {
        updateFieldValue();
      }, 0);
    },
    [setEdges],
  );

  // Add node button handler
  const addNode = useCallback(
    (type: string) => {
      const newId = `node-${Date.now()}`;
      const newNode = {
        id: newId,
        type: 'custom',
        data: {
          label: `New ${type}`,
          type,
          onUpdate: handleNodeUpdate,
          onDelete: handleNodeDelete,
        },
        position: { x: Math.random() * 300 + 100, y: Math.random() * 200 + 100 },
      };

      // Update the nodes and also update field value in the callback
      setNodes((prevNodes) => {
        const updatedNodes = [...prevNodes, newNode];
        console.log('Adding node:', newNode);
        return updatedNodes;
      });

      // Update field value after a short delay to ensure state is updated
      setTimeout(() => {
        updateFieldValue();
      }, 0);
    },
    [handleNodeUpdate, handleNodeDelete, setNodes, updateFieldValue],
  );

  // Clear all nodes
  const clearAllNodes = useCallback(() => {
    if (confirm('Are you sure you want to clear all nodes? This action cannot be undone.')) {
      setNodes([]);
      setEdges([]);
      setValue([]);
    }
  }, [setNodes, setEdges, setValue]);

  return (
    <div className="border rounded-md p-4 bg-gray-50">
      <div className="mb-4">
        <h3 className="text-lg font-medium mb-2">Flowchart Editor</h3>

        {/* Debug info */}
        <div className="mb-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-xs text-yellow-800">
          <div className="flex justify-between items-start">
            <div>
              <strong>Debug Info:</strong>
              <div className="mt-1">
                <div>• Value exists: {value ? '✅' : '❌'}</div>
                <div>• Is array: {Array.isArray(value) ? '✅' : '❌'}</div>
                <div>• Nodes count: {Array.isArray(value) ? value.length : 0}</div>
                <div>• Initialized: {isInitialized ? '✅' : '❌'}</div>
                <div>• Current nodes in editor: {nodes.length}</div>
                {Array.isArray(value) && value.length > 0 && (
                  <div>• Node IDs: {value.map((n: any) => n.id).join(', ')}</div>
                )}
              </div>
            </div>
            <div className="flex gap-1">
              <button
                type="button"
                onClick={() => {
                  setIsInitialized(false);
                  console.log('🔄 Force refresh triggered');
                }}
                className="px-2 py-1 bg-yellow-600 text-white rounded text-xs hover:bg-yellow-700"
              >
                Force Refresh
              </button>
              <button
                type="button"
                onClick={() => {
                  console.log('💾 Manual save triggered');
                  updateFieldValue();
                }}
                className="px-2 py-1 bg-green-600 text-white rounded text-xs hover:bg-green-700"
              >
                Save Now
              </button>
            </div>
          </div>
          {value && (
            <details className="mt-2">
              <summary className="cursor-pointer text-yellow-900 font-medium">Raw Data</summary>
              <pre className="mt-1 text-xs bg-yellow-100 p-2 rounded overflow-auto max-h-32">
                {JSON.stringify(value, null, 2)}
              </pre>
            </details>
          )}
        </div>

        <div className="mb-3 p-3 bg-blue-50 border border-blue-200 rounded text-sm text-blue-800">
          <strong>Instructions:</strong>
          <ul className="mt-1 ml-4 list-disc">
            <li>Click on node names to edit them</li>
            <li>Click on node types to change them (Stage/Process/Innovation)</li>
            <li>Drag nodes to reposition them</li>
            <li>Connect nodes by dragging from one handle to another</li>
            <li>Hover over nodes to see the delete button</li>
          </ul>
        </div>
        <div className="flex gap-2 mb-4 flex-wrap">
          <button
            type="button"
            className="px-3 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
            onClick={() => addNode('Stage')}
          >
            + Add Stage
          </button>
          <button
            type="button"
            className="px-3 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
            onClick={() => addNode('Process')}
          >
            + Add Process
          </button>
          <button
            type="button"
            className="px-3 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 transition-colors"
            onClick={() => addNode('Innovation')}
          >
            + Add Innovation
          </button>
          <button
            type="button"
            className="px-3 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors ml-auto"
            onClick={clearAllNodes}
          >
            Clear All
          </button>
        </div>
      </div>

      <div
        style={{ height: 500, width: '100%' }}
        className="border border-gray-300 rounded bg-white"
      >
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={handleNodesChange}
          onEdgesChange={handleEdgesChange}
          onConnect={onConnect}
          nodeTypes={nodeTypes}
          fitView
          fitViewOptions={{ padding: 0.1 }}
        >
          <Background color="#f1f5f9" gap={20} />
          <Controls className="bg-white shadow-md" />
        </ReactFlow>
      </div>

      <div className="mt-3 text-xs text-gray-600">
        <strong>Node Count:</strong> {nodes.length} | <strong>Connection Count:</strong>{' '}
        {edges.length}
      </div>
    </div>
  );
};

// Export wrapped in provider
const FlowchartEditor = (props: { path: string; name: string }) => {
  return (
    <ReactFlowProvider>
      <FlowchartEditorComponent {...props} />
    </ReactFlowProvider>
  );
};

export default FlowchartEditor;
