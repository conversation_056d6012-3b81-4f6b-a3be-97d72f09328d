'use client';
import { useField } from '@payloadcms/ui';
import React, { useCallback, useEffect } from 'react';
import React<PERSON>low, {
  addEdge,
  Background,
  Connection,
  Controls,
  Edge,
  Handle,
  Position,
  ReactFlowProvider,
  useEdgesState,
  useNodesState,
} from 'reactflow';
import 'reactflow/dist/style.css';

// Custom node component similar to the frontend one
const CustomNode = ({ data }: { data: any }) => {
  return (
    <div className="p-3 border border-gray-200 rounded-lg bg-white shadow-md">
      <div className="text-sm font-medium">{data.label}</div>
      <div className="text-xs text-gray-500">{data.type}</div>

      <Handle
        type="source"
        position={Position.Bottom}
        id="process-source"
        className="w-2 h-2 bg-blue-500"
      />
      <Handle
        type="target"
        position={Position.Top}
        id="process-target"
        className="w-2 h-2 bg-blue-500"
      />
    </div>
  );
};

const nodeTypes = {
  custom: CustomNode,
};

const FlowchartEditorComponent: React.FC<{ path: string; name: string }> = (props) => {
  const { value, setValue } = useField<any>({ path: 'flowchart' });
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);

  // Add a default node if there are no nodes
  useEffect(() => {
    if (!value?.nodes || value.nodes.length === 0) {
      console.log('Injecting default test node...');
      const defaultNode = {
        id: 'test-node',
        type: 'custom',
        data: { label: 'Default Stage', type: 'Stage' },
        position: { x: 150, y: 150 },
      };
      setNodes([defaultNode]);

      // Also update the field value
      setValue({
        ...value,
        nodes: [
          {
            id: defaultNode.id,
            name: defaultNode.data.label,
            type: defaultNode.data.type,
            position: defaultNode.position,
          },
        ],
      });
    }
  }, [value?.nodes]);

  // Initialize from existing data
  useEffect(() => {
    console.log('value.nodes from useField', value?.nodes);

    if (Array.isArray(value?.nodes) && value.nodes.length > 0) {
      // Convert nodes array to ReactFlow format
      const flowNodes = value.nodes.map((node: any, index: number) => ({
        id: node.id,
        type: 'custom',
        data: {
          label: node.name,
          type: node.type,
        },
        position: node.position ?? { x: 100 + index * 200, y: 100 + index * 100 },
      }));

      // Create edges based on parent/child relationships
      const flowEdges: Edge[] = [];
      value.nodes.forEach((node: any) => {
        if (node.type === 'Process' && node.processFromId && node.processToId) {
          flowEdges.push({
            id: `${node.processFromId}-${node.processToId}`,
            source: node.processFromId,
            target: node.processToId,
            label: node.name,
            type: 'default',
          });
        }
      });

      setNodes(flowNodes);
      setEdges(flowEdges);
    }
  }, []);

  // Save changes back to the field
  const handleNodesChange = (changes: any) => {
    onNodesChange(changes);
    updateFieldValue();
  };

  const handleEdgesChange = (changes: any) => {
    onEdgesChange(changes);
    updateFieldValue();
  };

  // Handle new connections between nodes
  const onConnect = useCallback(
    (connection: Connection) => {
      // Add the new edge
      setEdges((eds) => addEdge(connection, eds));

      // Update the field value after a short delay to ensure edges state is updated
      setTimeout(() => {
        updateFieldValue();
      }, 0);
    },
    [setEdges],
  );

  // Update the field value with current nodes and edges
  const updateFieldValue = () => {
    // Map nodes to the format expected by the field
    const updatedNodes = nodes.map((node) => {
      const nodeEdges = edges.filter((edge) => edge.source === node.id || edge.target === node.id);

      return {
        id: node.id,
        name: node.data.label,
        type: node.data.type || 'Stage',
        position: node.position, // Save position!
        // For Process nodes, add connection information
        ...(node.data.type === 'Process' && {
          processFromId: nodeEdges.find((e) => e.target === node.id)?.source || null,
          processToId: nodeEdges.find((e) => e.source === node.id)?.target || null,
        }),
      };
    });

    setValue({
      ...value,
      nodes: updatedNodes,
    });
  };

  // Add node button handler
  const addNode = (type: string) => {
    const newId = `node-${Date.now()}`;
    const newNode = {
      id: newId,
      type: 'custom',
      data: { label: `New ${type}`, type },
      position: { x: 100, y: 100 },
    };

    // Update the nodes and also update field value in the callback
    setNodes((prevNodes) => {
      const updatedNodes = [...prevNodes, newNode];

      // After nodes are added, update the field with the new list
      const updatedFieldNodes = updatedNodes.map((node) => ({
        id: node.id,
        name: node.data.label,
        type: node.data.type || 'Stage',
        position: node.position, // Save position!
      }));

      setValue({
        ...value,
        nodes: updatedFieldNodes,
      });
      console.log('Adding node:', newNode);

      return updatedNodes;
    });
    console.log('Rendered nodes:', nodes);
  };

  return (
    <div className="border rounded-md p-4">
      <div className="mb-4">
        <h3 className="text-lg font-medium mb-2">Flowchart Editor</h3>
        <div className="flex gap-2 mb-4">
          <button
            type="button"
            className="px-3 py-1 bg-blue-500 text-white rounded"
            onClick={() => addNode('Stage')}
          >
            Add Stage
          </button>
          <button
            type="button"
            className="px-3 py-1 bg-green-500 text-white rounded"
            onClick={() => addNode('Process')}
          >
            Add Process
          </button>
        </div>
      </div>

      <div style={{ height: 500, width: '100%' }}>
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={handleNodesChange}
          onEdgesChange={handleEdgesChange}
          onConnect={onConnect}
          nodeTypes={nodeTypes}
        >
          <Background />
          <Controls />
        </ReactFlow>
      </div>
    </div>
  );
};

// Export wrapped in provider
const FlowchartEditor = (props: { path: string; name: string }) => {
  return (
    <ReactFlowProvider>
      <FlowchartEditorComponent {...props} />
    </ReactFlowProvider>
  );
};

export default FlowchartEditor;
