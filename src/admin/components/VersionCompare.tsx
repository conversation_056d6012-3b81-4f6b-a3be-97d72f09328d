'use client';

import { getDocument } from '@/app/(frontend)/utils/clientUtils';
import { diffChars, diffWords } from 'diff';
import React, { useEffect, useMemo, useState } from 'react';

// Custom hook to detect Payload theme
export function usePayloadTheme() {
  const doc = getDocument();
  if (!doc) return undefined;

  const [theme, setTheme] = useState<string | undefined>(
    typeof window !== 'undefined' ? doc?.documentElement.dataset.theme : undefined,
  );

  useEffect(() => {
    const observer = new MutationObserver(() => {
      setTheme(doc?.documentElement.dataset.theme);
    });

    observer.observe(doc?.documentElement, {
      attributes: true,
      attributeFilter: ['data-theme'],
    });

    return () => observer.disconnect();
  }, []);

  return theme;
}

// Base styles that will be modified based on theme
const baseStyles = {
  container: {
    fontFamily: 'system-ui, -apple-system, sans-serif',
  },
  gridContainer: {
    display: 'grid',
    gridTemplateColumns: '1fr 1fr 1fr',
    gap: '16px',
    marginBottom: '16px',
  },
  headerRow: {
    fontWeight: 'bold',
    borderBottom: '1px solid #e5e7eb',
    paddingBottom: '8px',
  },
  fieldRow: {
    display: 'grid',
    gridTemplateColumns: '1fr 1fr 1fr',
    gap: '16px',
    padding: '12px 0',
  },
  highlightedRow: {
    backgroundColor: '#f9fafb',
    borderLeft: '4px solid #facc15',
    paddingLeft: '12px',
  },
  fieldLabel: {
    fontWeight: '500',
  },
  addedContent: {
    backgroundColor: '#dcfce7',
    padding: '4px',
    borderRadius: '4px',
  },
  removedContent: {
    backgroundColor: '#fee2e2',
    padding: '4px',
    borderRadius: '4px',
  },
  scrollContainer: {
    maxHeight: '240px',
    overflowY: 'auto' as const,
    border: '1px solid #e5e7eb',
    padding: '8px',
    borderRadius: '4px',
    scrollbarWidth: 'none' as const /* Firefox */,
    msOverflowStyle: 'none' as const /* IE and Edge */,
    '&::-webkit-scrollbar': {
      display: 'none',
    },
  },
  // Add global styles for webkit scrollbar
  globalStyles: `
    .scroll-container::-webkit-scrollbar {
      display: none;
    }
    .scroll-container {
      -ms-overflow-style: none;
      scrollbar-width: none;
    }
  `,
  legend: {
    marginTop: '24px',
    borderTop: '1px solid #e5e7eb',
    paddingTop: '16px',
  },
  legendItem: {
    display: 'flex',
    alignItems: 'center',
    marginRight: '16px',
  },
  legendColor: {
    display: 'inline-block',
    width: '16px',
    height: '16px',
    marginRight: '8px',
  },
  legendRed: {
    backgroundColor: '#fee2e2',
  },
  legendGreen: {
    backgroundColor: '#dcfce7',
  },
  legendContainer: {
    display: 'flex',
    alignItems: 'center',
    gap: '16px',
  },
  grayText: {
    color: '#9ca3af',
  },
};

interface VersionCompareProps {
  currentVersion: any;
  compareVersion: any;
}

const formatDate = (dateString: string) => {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  return date.toLocaleString();
};

const stripHtml = (html: string) => {
  if (!html) return '';
  return html.replace(/<[^>]+>/g, '');
};

// Component definition
const VersionCompare: React.FC<VersionCompareProps> = ({ currentVersion, compareVersion }) => {
  const [showHtml, setShowHtml] = useState(false);
  const theme = usePayloadTheme();

  // Create theme-aware styles
  const styles = useMemo(() => {
    const isDark = theme === 'dark';

    return {
      ...baseStyles,
      container: {
        ...baseStyles.container,
        color: isDark ? '#e5e7eb' : '#374151',
      },
      headerRow: {
        ...baseStyles.headerRow,
        borderBottom: `1px solid ${isDark ? '#4b5563' : '#e5e7eb'}`,
      },
      highlightedRow: {
        ...baseStyles.highlightedRow,
        backgroundColor: isDark ? '#1f2937' : '#f9fafb',
        borderLeft: `4px solid ${isDark ? '#d97706' : '#facc15'}`,
      },
      addedContent: {
        ...baseStyles.addedContent,
        backgroundColor: isDark ? '#064e3b' : '#dcfce7',
        color: isDark ? '#d1fae5' : '#065f46',
      },
      removedContent: {
        ...baseStyles.removedContent,
        backgroundColor: isDark ? '#7f1d1d' : '#fee2e2',
        color: isDark ? '#fecaca' : '#b91c1c',
      },
      scrollContainer: {
        ...baseStyles.scrollContainer,
        border: `1px solid ${isDark ? '#4b5563' : '#e5e7eb'}`,
        backgroundColor: isDark ? '#111827' : 'white',
      },
      legend: {
        ...baseStyles.legend,
        borderTop: `1px solid ${isDark ? '#4b5563' : '#e5e7eb'}`,
      },
      legendRed: {
        ...baseStyles.legendRed,
        backgroundColor: isDark ? '#7f1d1d' : '#fee2e2',
      },
      legendGreen: {
        ...baseStyles.legendGreen,
        backgroundColor: isDark ? '#064e3b' : '#dcfce7',
      },
      grayText: {
        ...baseStyles.grayText,
        color: isDark ? '#9ca3af' : '#6b7280',
      },
      // Update global styles for scrollbar hiding
      globalStyles: `
        .scroll-container::-webkit-scrollbar {
          display: none;
        }
        .scroll-container {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
      `,
    };
  }, [theme]);

  // Add global styles to hide scrollbars
  useEffect(() => {
    const doc = getDocument();
    if (!doc) return undefined;

    // Add a style tag to handle webkit scrollbar hiding
    const styleTag = doc?.createElement('style');
    styleTag.innerHTML = styles.globalStyles;
    doc?.head.appendChild(styleTag);

    return () => {
      // Clean up the style tag when component unmounts
      doc?.head.removeChild(styleTag);
    };
  }, [styles.globalStyles]);

  // Helper function to render diff with highlighting
  const renderDiff = (text1: string, text2: string, type: 'chars' | 'words' = 'words') => {
    if (!text1 && !text2) return <span style={styles.grayText}>No content</span>;
    if (!text1) return <span style={styles.addedContent}>{text2}</span>;
    if (!text2) return <span style={styles.removedContent}>{text1}</span>;

    const diff = type === 'chars' ? diffChars(text1, text2) : diffWords(text1, text2);

    return diff.map((part, index) => {
      if (part.added) {
        return (
          <span key={index} style={styles.addedContent}>
            {part.value}
          </span>
        );
      }
      if (part.removed) {
        return (
          <span key={index} style={styles.removedContent}>
            {part.value}
          </span>
        );
      }
      return <span key={index}>{part.value}</span>;
    });
  };

  // Helper to render arrays with differences
  const renderArrayDiff = (arr1: any[] = [], arr2: any[] = []) => {
    const combined = [...arr1, ...arr2].filter(
      (item, index, self) => self.findIndex((i) => i.id === item.id) === index,
    );

    return (
      <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
        {combined.map((item) => {
          const inCurrent = arr1.some((i) => i.id === item.id);
          const inCompare = arr2.some((i) => i.id === item.id);

          let itemStyle = {};
          if (inCurrent && !inCompare) itemStyle = styles.removedContent;
          if (!inCurrent && inCompare) itemStyle = styles.addedContent;

          return (
            <div key={item.id} style={itemStyle}>
              {item.value || item.label || item.id}
            </div>
          );
        })}
      </div>
    );
  };

  // Helper to render relationship fields
  const renderRelationship = (rel1: any, rel2: any) => {
    if (!rel1 && !rel2) return <span style={styles.grayText}>None</span>;

    const val1 = rel1?.value || rel1?.label || (rel1 ? 'Present' : 'None');
    const val2 = rel2?.value || rel2?.label || (rel2 ? 'Present' : 'None');

    if (val1 === val2) return <span>{val1}</span>;

    return (
      <div style={{ display: 'flex', flexDirection: 'column' }}>
        <span style={val1 !== val2 ? styles.removedContent : {}}>{val1}</span>
        {val1 !== val2 && <span style={{ ...styles.addedContent, marginTop: '4px' }}>{val2}</span>}
      </div>
    );
  };

  if (!currentVersion || !compareVersion) {
    return <div>Missing version data for comparison</div>;
  }

  const fields = [
    { key: 'title', label: 'Title', type: 'text' },
    { key: 'description', label: 'Description', type: 'longtext' },
    { key: 'organizationName', label: 'Organization', type: 'text' },
    { key: 'name', label: 'Contact Name', type: 'text' },
    { key: 'email', label: 'Email', type: 'text' },
    { key: 'phoneNo', label: 'Phone', type: 'text' },
    { key: 'link', label: 'Link', type: 'text' },
    { key: 'currentStageOfDevelopment', label: 'Development Stage', type: 'relationship' },
    { key: 'interventionType', label: 'Intervention Types', type: 'array' },
    { key: 'createdAt', label: 'Created', type: 'date' },
    { key: 'updatedAt', label: 'Updated', type: 'date' },
  ];

  return (
    <div style={styles.container}>
      <button
        type="button"
        onClick={() => setShowHtml(!showHtml)}
        style={{
          marginBottom: '16px',
          padding: '8px 12px',
          border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
          borderRadius: '6px',
          background: theme === 'dark' ? '#374151' : '#f3f4f6',
          color: theme === 'dark' ? '#e5e7eb' : '#374151',
          cursor: 'pointer',
        }}
      >
        {showHtml ? 'Show Text Diff' : 'Show Full HTML'}
      </button>

      <div style={{ ...styles.gridContainer, ...styles.headerRow }}>
        <div>Field</div>
        <div>Current Version</div>
        <div>Version {compareVersion.versionNumber}</div>
      </div>

      {fields.map((field) => {
        const currentValue = currentVersion[field.key];
        const compareValue = compareVersion[field.key];
        const isDifferent = JSON.stringify(currentValue) !== JSON.stringify(compareValue);

        const rowStyle = {
          ...styles.fieldRow,
          ...(isDifferent ? styles.highlightedRow : {}),
        };

        return (
          <div key={field.key} style={rowStyle}>
            <div style={styles.fieldLabel}>{field.label}</div>

            {/* Left column: Original value (no diff highlighting) */}
            <div>
              {field.type === 'text' && (
                <span>{currentValue || <span style={styles.grayText}>None</span>}</span>
              )}
              {field.type === 'longtext' && (
                <div style={styles.scrollContainer} className="scroll-container">
                  {showHtml ? (
                    <div dangerouslySetInnerHTML={{ __html: currentValue || '' }} />
                  ) : (
                    <span>
                      {stripHtml(currentValue) || <span style={styles.grayText}>None</span>}
                    </span>
                  )}
                </div>
              )}
              {field.type === 'date' && formatDate(currentValue)}
              {field.type === 'array' &&
                (currentValue?.length ? (
                  currentValue.map((item: any) => (
                    <div key={item.id}>{item.value || item.label || item.id}</div>
                  ))
                ) : (
                  <span style={styles.grayText}>None</span>
                ))}
              {field.type === 'relationship' && (
                <span>{currentValue?.value || currentValue?.label || 'None'}</span>
              )}
            </div>

            {/* Right column: Compared value (with diff highlighting) */}
            <div>
              {field.type === 'text' && renderDiff(currentValue, compareValue)}
              {field.type === 'longtext' && (
                <div style={styles.scrollContainer} className="scroll-container">
                  {showHtml ? (
                    <div dangerouslySetInnerHTML={{ __html: compareValue || '' }} />
                  ) : (
                    renderDiff(stripHtml(currentValue), stripHtml(compareValue))
                  )}
                </div>
              )}
              {field.type === 'date' && formatDate(compareValue)}
              {field.type === 'array' && renderArrayDiff(currentValue, compareValue)}
              {field.type === 'relationship' && renderRelationship(currentValue, compareValue)}
            </div>
          </div>
        );
      })}

      <div style={styles.legend}>
        <div style={styles.legendContainer}>
          <div style={styles.legendItem}>
            <span style={{ ...styles.legendColor, ...styles.legendRed }}></span>
            <span>Removed in newer version</span>
          </div>
          <div style={styles.legendItem}>
            <span style={{ ...styles.legendColor, ...styles.legendGreen }}></span>
            <span>Added in newer version</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VersionCompare;
