'use client';

import { getDocument } from '@/app/(frontend)/utils/clientUtils';
import React, { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';

interface ModalPortalProps {
  children: React.ReactNode;
  isOpen: boolean;
}

const ModalPortal: React.FC<ModalPortalProps> = ({ children, isOpen }) => {
  const [portalElement, setPortalElement] = useState<HTMLElement | null>(null);

  useEffect(() => {
    const doc = getDocument();
    if (!doc) {
      return;
    }
    let portalRoot = doc?.getElementById('modal-portal-root');

    if (!portalRoot) {
      portalRoot = doc?.createElement('div');
      portalRoot.id = 'modal-portal-root';
      portalRoot.style.position = 'fixed';
      portalRoot.style.top = '0';
      portalRoot.style.left = '0';
      portalRoot.style.width = '100%';
      portalRoot.style.height = '100%';
      portalRoot.style.zIndex = '2147483647';
      portalRoot.style.pointerEvents = 'none';
      doc?.body.appendChild(portalRoot);
    }

    setPortalElement(portalRoot);
  }, []);

  if (!portalElement || !isOpen) return null;

  return createPortal(<div style={{ pointerEvents: 'auto' }}>{children}</div>, portalElement);
};

export default ModalPortal;
