'use client';

import { getDocument, getWindow } from '@/app/(frontend)/utils/clientUtils';
import { useField, useWatchForm } from '@payloadcms/ui';
import { AnimatePresence, motion } from 'framer-motion';
import { usePathname } from 'next/navigation';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import ModalPortal from './ModalPortal';
import VersionCompare, { usePayloadTheme } from './VersionCompare';

// Base styles that will be modified based on theme
const baseStyles = {
  versionList: {
    marginTop: '12px',
    fontSize: '14px',
    color: '#374151',
    display: 'flex',
    flexDirection: 'column' as const,
    gap: '12px',
    maxHeight: '400px',
    overflowY: 'auto' as const,
    padding: '4px',
    scrollbarWidth: 'thin' as const,
  },
  searchContainer: {
    marginBottom: '12px',
    position: 'relative' as const,
  },
  searchInput: {
    width: '100%',
    padding: '8px 12px',
    paddingLeft: '32px',
    borderRadius: '4px',
    border: '1px solid #e5e7eb',
    fontSize: '14px',
  },
  searchIcon: {
    position: 'absolute' as const,
    left: '10px',
    top: '50%',
    transform: 'translateY(-50%)',
    color: '#9ca3af',
  },
  versionItem: {
    display: 'flex',
    flexDirection: 'column' as const,
    gap: '8px',
    border: '1px solid #e5e7eb',
    padding: '12px',
    borderRadius: '6px',
    transition: 'all 0.2s ease',
    backgroundColor: 'white',
    position: 'relative' as const,
  },
  currentVersionBadge: {
    position: 'absolute' as const,
    top: '-8px',
    right: '-8px',
    backgroundColor: '#3b82f6',
    color: 'white',
    padding: '2px 8px',
    borderRadius: '12px',
    fontSize: '11px',
    fontWeight: 'bold',
    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
    zIndex: 1,
  },
  currentVersionItem: {
    borderColor: '#3b82f6',
    boxShadow: '0 2px 8px rgba(59, 130, 246, 0.15)',
  },
  disabledButton: {
    opacity: 0.5,
    cursor: 'not-allowed',
    backgroundColor: '#9ca3af',
  },
  versionHeader: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  versionInfo: {
    display: 'flex',
    flexDirection: 'column' as const,
    gap: '4px',
  },
  versionTitle: {
    fontWeight: 'bold',
    fontSize: '15px',
  },
  versionMeta: {
    fontSize: '12px',
    color: '#6b7280',
  },
  versionRemark: {
    fontSize: '13px',
    color: '#4b5563',
    fontStyle: 'italic',
    marginTop: '4px',
    padding: '6px 0',
    borderTop: '1px dashed #e5e7eb',
  },
  statusBadge: {
    display: 'inline-block',
    padding: '2px 8px',
    borderRadius: '12px',
    fontSize: '12px',
    fontWeight: '500',
    textTransform: 'capitalize' as const,
    marginLeft: '8px',
  },
  statusPending: {
    backgroundColor: '#fef3c7',
    color: '#92400e',
  },
  statusApproved: {
    backgroundColor: '#d1fae5',
    color: '#065f46',
  },
  statusRejected: {
    backgroundColor: '#fee2e2',
    color: '#b91c1c',
  },
  statusArchived: {
    backgroundColor: '#f3f4f6',
    color: '#4b5563',
  },
  compareButton: {
    backgroundColor: '#10b981',
    color: 'white',
    padding: '6px 12px',
    borderRadius: '4px',
    border: 'none',
    cursor: 'pointer',
    transition: 'all 0.2s',
    display: 'flex',
    alignItems: 'center',
    gap: '6px',
    fontSize: '13px',
  },
  compareButtonHover: {
    backgroundColor: '#059669',
    transform: 'translateY(-1px)',
    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
  },
  modalOverlay: {
    position: 'fixed' as const,
    inset: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.75)',
    backdropFilter: 'blur(4px)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 9999,
  },
  modalContent: {
    backgroundColor: 'white',
    padding: '24px',
    borderRadius: '8px',
    boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2)',
    width: '100%',
    maxWidth: '1280px',
    maxHeight: '90vh',
    overflowY: 'auto' as const,
    position: 'relative' as const,
    scrollbarWidth: 'none' as const /* Firefox */,
    msOverflowStyle: 'none' as const /* IE and Edge */,
  },
  closeButton: {
    position: 'absolute' as const,
    top: '12px',
    right: '16px',
    color: '#6b7280',
    backgroundColor: 'transparent',
    border: 'none',
    fontSize: '20px',
    cursor: 'pointer',
    width: '32px',
    height: '32px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: '50%',
    transition: 'all 0.2s',
  },
  closeButtonHover: {
    backgroundColor: '#f3f4f6',
    color: '#111827',
  },
  modalTitle: {
    fontSize: '20px',
    fontWeight: 'bold',
    marginBottom: '20px',
  },
  loadingContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '160px',
    flexDirection: 'column' as const,
    gap: '16px',
  },
  loadingText: {
    color: '#6b7280',
    fontSize: '14px',
  },
  spinner: {
    height: '32px',
    width: '32px',
    borderRadius: '50%',
    border: '2px solid #e5e7eb',
    borderTopColor: '#3b82f6',
    animation: 'spin 1s linear infinite',
  },
  noVersions: {
    padding: '16px',
    textAlign: 'center' as const,
    color: '#6b7280',
    backgroundColor: '#f9fafb',
    borderRadius: '6px',
    border: '1px dashed #e5e7eb',
  },
  approveButton: {
    backgroundColor: '#3b82f6',
    color: 'white',
    padding: '10px 16px',
    borderRadius: '4px',
    border: 'none',
    cursor: 'pointer',
    transition: 'all 0.2s',
    display: 'inline-flex',
    alignItems: 'center',
    gap: '8px',
    fontSize: '14px',
  },
  approveButtonHover: {
    backgroundColor: '#2563eb',
    transform: 'translateY(-1px)',
    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
  },
  cancelButton: {
    backgroundColor: 'transparent',
    color: '#6b7280',
    padding: '10px 16px',
    borderRadius: '4px',
    border: '1px solid #e5e7eb',
    cursor: 'pointer',
    transition: 'all 0.2s',
  },
  cancelButtonHover: {
    backgroundColor: '#f3f4f6',
  },
  buttonContainer: {
    display: 'flex',
    justifyContent: 'flex-end',
    gap: '12px',
    marginTop: '24px',
    borderTop: '1px solid #e5e7eb',
    paddingTop: '20px',
  },
  confirmDialog: {
    position: 'absolute' as const,
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    backgroundColor: 'white',
    padding: '24px',
    borderRadius: '8px',
    boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.3)',
    width: '100%',
    maxWidth: '400px',
    zIndex: 10,
  },
  confirmOverlay: {
    position: 'absolute' as const,
    inset: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    backdropFilter: 'blur(2px)',
    zIndex: 9,
  },
  confirmTitle: {
    fontSize: '18px',
    fontWeight: 'bold',
    marginBottom: '12px',
  },
  confirmText: {
    marginBottom: '20px',
    lineHeight: 1.5,
  },
  confirmButtons: {
    display: 'flex',
    justifyContent: 'flex-end',
    gap: '12px',
  },
  keyboardShortcut: {
    display: 'inline-block',
    padding: '2px 6px',
    borderRadius: '4px',
    backgroundColor: '#f3f4f6',
    color: '#4b5563',
    fontSize: '12px',
    marginLeft: '8px',
    fontFamily: 'monospace',
  },
};

interface ProjectVersion {
  id: string;
  name: string;
  versionNumber: number;
  title?: string;
  description?: string;
  currentStageOfDevelopment?: any;
  interventionType?: any[];
  organizationName?: string;
  link?: string;
  email?: string;
  phoneNo?: string;
  createdAt?: string;
  updatedAt?: string;
  status?: 'pending' | 'approved' | 'rejected' | 'archived';
  remark?: string;
  createdBy?: {
    id: string;
    email: string;
    name?: string;
  };
  isCurrent?: boolean; // Flag to indicate if this is the current active version
}

const VersionDetailsField = () => {
  useField<string[] | undefined>({ path: 'versions' }); // Access the versions field
  const form = useWatchForm();
  const pathname = usePathname();
  const theme = usePayloadTheme();
  const modalRef = useRef<HTMLDivElement>(null);

  const [availableVersions, setAvailableVersions] = useState<ProjectVersion[]>([]);
  const [filteredVersions, setFilteredVersions] = useState<ProjectVersion[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [selectedVersion, setSelectedVersion] = useState<ProjectVersion | null>(null);
  const [currentVersion, setCurrentVersion] = useState<ProjectVersion | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingModal, setIsLoadingModal] = useState(false);
  const [isApproving, setIsApproving] = useState(false);
  const [approvalSuccess, setApprovalSuccess] = useState(false);

  const innovationId = pathname?.split('/').pop();

  // Filter versions based on search term
  const filterVersions = useCallback(
    (term: string) => {
      if (!term.trim()) {
        setFilteredVersions(availableVersions);
        return;
      }

      const lowerTerm = term.toLowerCase();
      const filtered = availableVersions.filter((version) => {
        return (
          version.title?.toLowerCase().includes(lowerTerm) ||
          version.remark?.toLowerCase().includes(lowerTerm) ||
          version.versionNumber.toString().includes(lowerTerm) ||
          version.status?.toLowerCase().includes(lowerTerm) ||
          (version.createdBy?.name && version.createdBy.name.toLowerCase().includes(lowerTerm))
        );
      });

      setFilteredVersions(filtered);
    },
    [availableVersions],
  );

  // Update filtered versions when search term changes
  useEffect(() => {
    filterVersions(searchTerm);
  }, [searchTerm, filterVersions]);

  // Update filtered versions when available versions change
  useEffect(() => {
    setFilteredVersions(availableVersions);
  }, [availableVersions]);

  // Create theme-aware styles
  const styles = useMemo(() => {
    const isDark = theme === 'dark';

    return {
      ...baseStyles,
      versionList: {
        ...baseStyles.versionList,
        color: isDark ? '#e5e7eb' : '#374151',
        scrollbarWidth: 'thin' as const,
        scrollbarColor: isDark ? '#4b5563 #1f2937' : '#d1d5db #f9fafb',
      },
      searchContainer: {
        ...baseStyles.searchContainer,
      },
      searchInput: {
        ...baseStyles.searchInput,
        backgroundColor: isDark ? '#1f2937' : 'white',
        color: isDark ? '#e5e7eb' : '#374151',
        border: `1px solid ${isDark ? '#4b5563' : '#e5e7eb'}`,
      },
      searchIcon: {
        ...baseStyles.searchIcon,
        color: isDark ? '#6b7280' : '#9ca3af',
      },
      versionItem: {
        ...baseStyles.versionItem,
        border: `1px solid ${isDark ? '#4b5563' : '#e5e7eb'}`,
        backgroundColor: isDark ? '#1f2937' : 'white',
      },
      currentVersionBadge: {
        ...baseStyles.currentVersionBadge,
        backgroundColor: isDark ? '#2563eb' : '#3b82f6',
      },
      currentVersionItem: {
        ...baseStyles.currentVersionItem,
        borderColor: isDark ? '#2563eb' : '#3b82f6',
        boxShadow: isDark
          ? '0 2px 8px rgba(37, 99, 235, 0.2)'
          : '0 2px 8px rgba(59, 130, 246, 0.15)',
      },
      disabledButton: {
        ...baseStyles.disabledButton,
        backgroundColor: isDark ? '#4b5563' : '#9ca3af',
      },
      versionHeader: {
        ...baseStyles.versionHeader,
      },
      versionInfo: {
        ...baseStyles.versionInfo,
      },
      versionTitle: {
        ...baseStyles.versionTitle,
        color: isDark ? '#e5e7eb' : '#111827',
      },
      versionMeta: {
        ...baseStyles.versionMeta,
        color: isDark ? '#9ca3af' : '#6b7280',
      },
      versionRemark: {
        ...baseStyles.versionRemark,
        color: isDark ? '#9ca3af' : '#4b5563',
        borderTop: `1px dashed ${isDark ? '#4b5563' : '#e5e7eb'}`,
      },
      statusBadge: {
        ...baseStyles.statusBadge,
      },
      statusPending: {
        ...baseStyles.statusPending,
        backgroundColor: isDark ? '#78350f' : '#fef3c7',
        color: isDark ? '#fef3c7' : '#92400e',
      },
      statusApproved: {
        ...baseStyles.statusApproved,
        backgroundColor: isDark ? '#064e3b' : '#d1fae5',
        color: isDark ? '#d1fae5' : '#065f46',
      },
      statusRejected: {
        ...baseStyles.statusRejected,
        backgroundColor: isDark ? '#7f1d1d' : '#fee2e2',
        color: isDark ? '#fee2e2' : '#b91c1c',
      },
      statusArchived: {
        ...baseStyles.statusArchived,
        backgroundColor: isDark ? '#1f2937' : '#f3f4f6',
        color: isDark ? '#9ca3af' : '#4b5563',
      },
      compareButton: {
        ...baseStyles.compareButton,
        backgroundColor: isDark ? '#059669' : '#10b981',
      },
      compareButtonHover: {
        ...baseStyles.compareButtonHover,
        backgroundColor: isDark ? '#047857' : '#059669',
      },
      modalOverlay: {
        ...baseStyles.modalOverlay,
      },
      modalContent: {
        ...baseStyles.modalContent,
        backgroundColor: isDark ? '#111827' : 'white',
        color: isDark ? '#e5e7eb' : '#374151',
        border: isDark ? '1px solid #4b5563' : 'none',
      },
      closeButton: {
        ...baseStyles.closeButton,
        color: isDark ? '#9ca3af' : '#6b7280',
      },
      closeButtonHover: {
        ...baseStyles.closeButtonHover,
        backgroundColor: isDark ? '#1f2937' : '#f3f4f6',
        color: isDark ? '#e5e7eb' : '#111827',
      },
      modalTitle: {
        ...baseStyles.modalTitle,
        color: isDark ? '#e5e7eb' : '#111827',
      },
      loadingContainer: {
        ...baseStyles.loadingContainer,
      },
      loadingText: {
        ...baseStyles.loadingText,
        color: isDark ? '#9ca3af' : '#6b7280',
      },
      spinner: {
        ...baseStyles.spinner,
        border: `2px solid ${isDark ? '#4b5563' : '#e5e7eb'}`,
        borderTopColor: isDark ? '#60a5fa' : '#3b82f6',
      },
      noVersions: {
        ...baseStyles.noVersions,
        color: isDark ? '#9ca3af' : '#6b7280',
        backgroundColor: isDark ? '#1f2937' : '#f9fafb',
        border: `1px dashed ${isDark ? '#4b5563' : '#e5e7eb'}`,
      },
      buttonContainer: {
        ...baseStyles.buttonContainer,
        borderTop: `1px solid ${isDark ? '#4b5563' : '#e5e7eb'}`,
      },
      approveButton: {
        ...baseStyles.approveButton,
        backgroundColor: isDark ? '#3b82f6' : '#3b82f6',
        color: 'white',
      },
      approveButtonHover: {
        ...baseStyles.approveButtonHover,
        backgroundColor: isDark ? '#2563eb' : '#2563eb',
      },
      cancelButton: {
        ...baseStyles.cancelButton,
        color: isDark ? '#9ca3af' : '#6b7280',
        border: `1px solid ${isDark ? '#4b5563' : '#e5e7eb'}`,
      },
      cancelButtonHover: {
        ...baseStyles.cancelButtonHover,
        backgroundColor: isDark ? '#1f2937' : '#f3f4f6',
      },
      confirmDialog: {
        ...baseStyles.confirmDialog,
        backgroundColor: isDark ? '#111827' : 'white',
        color: isDark ? '#e5e7eb' : '#374151',
        border: isDark ? '1px solid #4b5563' : 'none',
      },
      confirmTitle: {
        ...baseStyles.confirmTitle,
        color: isDark ? '#e5e7eb' : '#111827',
      },
      confirmText: {
        ...baseStyles.confirmText,
        color: isDark ? '#d1d5db' : '#4b5563',
      },
      keyboardShortcut: {
        ...baseStyles.keyboardShortcut,
        backgroundColor: isDark ? '#1f2937' : '#f3f4f6',
        color: isDark ? '#9ca3af' : '#4b5563',
      },
    };
  }, [theme]);

  // Add styles to hide webkit scrollbars
  useEffect(() => {
    const doc = getDocument();
    if (!doc) {
      return;
    }
    const styleTag = doc?.createElement('style');
    styleTag.innerHTML = `
      .scrollbar-hide::-webkit-scrollbar {
        display: none;
      }
    `;
    doc?.head.appendChild(styleTag);

    return () => {
      doc?.head.removeChild(styleTag);
    };
  }, []);

  // Fetch versions with loading state
  const fetchFilteredVersions = useCallback(async () => {
    const currentVersionId = form?.getData()?.currentVersion;

    if (!innovationId) return;

    setIsLoading(true);

    try {
      // First, fetch all versions for this innovation
      const query = new URLSearchParams({
        'where[innovation][equals]': innovationId,
        limit: '100',
        depth: '1', // Include related data like createdBy
        sort: '-versionNumber', // Sort by version number descending
      });

      const res = await fetch(
        `${process.env.NEXT_PUBLIC_BACKEND_URL}/api/project-versions?${query.toString()}`,
      );
      const json = await res.json();
      const allVersions = json.docs || [];

      // Separate current version from other versions
      let currentVersionData = null;
      let otherVersions = allVersions;

      // If we have a current version ID, find it in the results
      if (currentVersionId) {
        // Try to find current version in the fetched results
        currentVersionData = allVersions.find((v: ProjectVersion) => v.id === currentVersionId);

        // If not found in the results, fetch it directly
        if (!currentVersionData) {
          const currentRes = await fetch(
            `${process.env.NEXT_PUBLIC_BACKEND_URL}/api/project-versions/${currentVersionId}?depth=1`,
          );
          if (currentRes.ok) {
            currentVersionData = await currentRes.json();
          }
        }

        // Set the current version
        if (currentVersionData) {
          // Mark it as the current version for UI purposes
          currentVersionData.isCurrent = true;
          setCurrentVersion(currentVersionData);

          // Remove it from other versions if it was in the list
          otherVersions = allVersions.filter((v: ProjectVersion) => v.id !== currentVersionId);
        }
      }

      // Combine current version with other versions for display
      // Put current version at the top of the list
      const displayVersions = currentVersionData
        ? [currentVersionData, ...otherVersions]
        : otherVersions;

      setAvailableVersions(displayVersions);
    } catch (err) {
      console.error('Failed to fetch project versions:', err);
    } finally {
      setIsLoading(false);
    }
  }, [innovationId, form]);

  // Fetch versions on component mount and when dependencies change
  useEffect(() => {
    fetchFilteredVersions();
  }, []);

  // Close the modal and reset state
  const closeModal = useCallback(() => {
    setShowModal(false);
    setShowConfirmDialog(false);
    setSelectedVersion(null);
    setApprovalSuccess(false);
  }, []);

  // Handle keyboard shortcuts
  useEffect(() => {
    const window = getWindow();
    if (!window) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (showModal) {
        // Close modal with Escape key
        if (e.key === 'Escape') {
          closeModal();
        }

        // Approve with Ctrl+Enter
        if (
          e.key === 'Enter' &&
          (e.ctrlKey || e.metaKey) &&
          !showConfirmDialog &&
          !approvalSuccess
        ) {
          e.preventDefault();
          setShowConfirmDialog(true);
        }
      }
    };

    window?.addEventListener('keydown', handleKeyDown);
    return () => window?.removeEventListener('keydown', handleKeyDown);
  }, [showModal, showConfirmDialog, approvalSuccess, closeModal]);

  // Open the comparison modal with loading state
  const openCompareModal = async (version: ProjectVersion) => {
    setSelectedVersion(version);
    setShowModal(true);
    setIsLoadingModal(true);

    try {
      // Fetch full version details if we don't have them yet
      if (!version.description) {
        const res = await fetch(
          `${process.env.NEXT_PUBLIC_BACKEND_URL}/api/project-versions/${version.id}?depth=1`,
        );
        if (res.ok) {
          const fullVersion = await res.json();
          setSelectedVersion(fullVersion);
        }
      }
    } catch (error) {
      console.error('Error fetching version details:', error);
    } finally {
      setIsLoadingModal(false);
    }
  };

  // Show confirmation dialog before approving
  const showApproveConfirmation = () => {
    setShowConfirmDialog(true);
  };

  // Cancel approval confirmation
  const cancelApproval = () => {
    setShowConfirmDialog(false);
  };

  // Approve the selected version
  const approveVersion = async () => {
    if (!selectedVersion) return;

    setIsApproving(true);
    setShowConfirmDialog(false);

    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_BACKEND_URL}/api/project-versions/${selectedVersion.id}`,
        {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            status: 'approved',
          }),
        },
      );

      if (response.ok) {
        setApprovalSuccess(true);
        window?.location.reload();
      } else {
        console.error('Failed to approve version:', await response.text());
      }
    } catch (error) {
      console.error('Error approving version:', error);
    } finally {
      setIsApproving(false);
    }
  };

  // Format date for display
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // Get status badge style based on status
  const getStatusBadgeStyle = (status?: string) => {
    switch (status) {
      case 'pending':
        return styles.statusPending;
      case 'approved':
        return styles.statusApproved;
      case 'rejected':
        return styles.statusRejected;
      case 'archived':
        return styles.statusArchived;
      default:
        return {};
    }
  };

  return (
    <div className="field-type">
      <label className="field-label">Available Project Versions</label>

      {/* Search input */}
      <div style={styles.searchContainer}>
        <span style={styles.searchIcon}>🔍</span>
        <input
          type="text"
          placeholder="Search versions..."
          style={styles.searchInput}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </div>

      {/* Loading state */}
      {isLoading ? (
        <div style={styles.loadingContainer}>
          <div
            style={{
              ...styles.spinner,
              animation: 'spin 1s linear infinite',
            }}
          ></div>
          <style jsx>{`
            @keyframes spin {
              0% {
                transform: rotate(0deg);
              }
              100% {
                transform: rotate(360deg);
              }
            }
          `}</style>
          <div style={styles.loadingText}>Loading versions...</div>
        </div>
      ) : (
        <div style={styles.versionList}>
          {filteredVersions.length === 0 && (
            <div style={styles.noVersions}>
              {searchTerm ? 'No versions match your search' : 'No versions available'}
            </div>
          )}

          {filteredVersions.map((version) => (
            <div
              key={version.id}
              style={{
                ...styles.versionItem,
                ...(version.isCurrent ? styles.currentVersionItem : {}),
              }}
            >
              {version.isCurrent && <div style={styles.currentVersionBadge}>Current</div>}
              <div style={styles.versionHeader}>
                <div style={styles.versionInfo}>
                  <div style={styles.versionTitle}>
                    Version {version.versionNumber}
                    <span
                      style={{
                        ...styles.statusBadge,
                        ...getStatusBadgeStyle(version.status),
                      }}
                    >
                      {version.status || 'unknown'}
                    </span>
                  </div>
                  <div style={styles.versionMeta}>
                    Created {formatDate(version.createdAt)}
                    {version.createdBy?.name && ` by ${version.createdBy.name}`}
                  </div>
                </div>
                {version.isCurrent ? (
                  <button
                    type="button"
                    style={{ ...styles.compareButton, ...styles.disabledButton }}
                    disabled={true}
                    title="Cannot compare current version with itself"
                  >
                    <span>Current Version</span>
                  </button>
                ) : (
                  <button
                    type="button"
                    style={styles.compareButton}
                    onMouseOver={(e) => {
                      Object.assign(e.currentTarget.style, {
                        backgroundColor: theme === 'dark' ? '#047857' : '#059669',
                        transform: 'translateY(-1px)',
                        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                      });
                    }}
                    onMouseOut={(e) => {
                      Object.assign(e.currentTarget.style, {
                        backgroundColor: theme === 'dark' ? '#059669' : '#10b981',
                        transform: 'translateY(0)',
                        boxShadow: 'none',
                      });
                    }}
                    onClick={() => openCompareModal(version)}
                  >
                    <span>Compare</span>
                    <span style={styles.keyboardShortcut}>⏎</span>
                  </button>
                )}
              </div>

              {version.remark && (
                <div
                  style={{
                    ...styles.versionRemark,
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                    maxWidth: '100%',
                  }}
                >
                  "{version.remark}"
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Modal - Using React Portal to ensure it's outside the DOM hierarchy */}
      <ModalPortal isOpen={showModal && !!selectedVersion && !!currentVersion}>
        <AnimatePresence>
          {showModal && (
            <motion.div
              style={styles.modalOverlay}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.2 }}
            >
              <motion.div
                className="scrollbar-hide"
                style={styles.modalContent}
                ref={modalRef}
                initial={{ scale: 0.95, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.95, opacity: 0 }}
                transition={{ duration: 0.2 }}
              >
                <button
                  type="button"
                  style={styles.closeButton}
                  onClick={closeModal}
                  onMouseOver={(e) => {
                    Object.assign(e.currentTarget.style, styles.closeButtonHover);
                  }}
                  onMouseOut={(e) => {
                    Object.assign(e.currentTarget.style, {
                      backgroundColor: 'transparent',
                      color: theme === 'dark' ? '#9ca3af' : '#6b7280',
                    });
                  }}
                  aria-label="Close modal"
                >
                  ✕
                </button>

                <h2 style={styles.modalTitle}>
                  Compare Current Version with Version {selectedVersion?.versionNumber}
                  <span style={styles.keyboardShortcut}>ESC to close</span>
                </h2>

                {isLoadingModal ? (
                  <div style={styles.loadingContainer}>
                    <div
                      style={{
                        ...styles.spinner,
                        animation: 'spin 1s linear infinite',
                      }}
                    ></div>
                    <div style={styles.loadingText}>Loading comparison data...</div>
                    <style jsx>{`
                      @keyframes spin {
                        0% {
                          transform: rotate(0deg);
                        }
                        100% {
                          transform: rotate(360deg);
                        }
                      }
                    `}</style>
                  </div>
                ) : (
                  <>
                    <VersionCompare
                      currentVersion={currentVersion}
                      compareVersion={selectedVersion}
                    />

                    {/* Confirmation dialog overlay */}
                    {showConfirmDialog && (
                      <>
                        <div style={styles.confirmOverlay}></div>
                        <div style={styles.confirmDialog}>
                          <h3 style={styles.confirmTitle}>Confirm Version Approval</h3>
                          <p style={styles.confirmText}>
                            Are you sure you want to approve Version{' '}
                            {selectedVersion?.versionNumber}? This will make it the current active
                            version and archive the previous version.
                          </p>
                          <div style={styles.confirmButtons}>
                            <button
                              type="button"
                              style={styles.cancelButton}
                              onClick={cancelApproval}
                              onMouseOver={(e) => {
                                Object.assign(e.currentTarget.style, styles.cancelButtonHover);
                              }}
                              onMouseOut={(e) => {
                                Object.assign(e.currentTarget.style, {
                                  backgroundColor: 'transparent',
                                });
                              }}
                            >
                              Cancel
                            </button>
                            <button
                              type="button"
                              style={styles.approveButton}
                              onClick={approveVersion}
                              onMouseOver={(e) => {
                                Object.assign(e.currentTarget.style, styles.approveButtonHover);
                              }}
                              onMouseOut={(e) => {
                                Object.assign(e.currentTarget.style, {
                                  backgroundColor: theme === 'dark' ? '#3b82f6' : '#3b82f6',
                                  transform: 'translateY(0)',
                                  boxShadow: 'none',
                                });
                              }}
                            >
                              Yes, Approve
                            </button>
                          </div>
                        </div>
                      </>
                    )}

                    <div style={styles.buttonContainer}>
                      {approvalSuccess ? (
                        <div style={{ color: theme === 'dark' ? '#34d399' : '#10b981' }}>
                          ✓ Version {selectedVersion?.versionNumber} has been approved successfully!
                        </div>
                      ) : (
                        <>
                          <button
                            type="button"
                            style={styles.cancelButton}
                            onClick={closeModal}
                            onMouseOver={(e) => {
                              Object.assign(e.currentTarget.style, styles.cancelButtonHover);
                            }}
                            onMouseOut={(e) => {
                              Object.assign(e.currentTarget.style, {
                                backgroundColor: 'transparent',
                              });
                            }}
                          >
                            Close
                          </button>
                          <button
                            type="button"
                            style={styles.approveButton}
                            onClick={showApproveConfirmation}
                            disabled={isApproving}
                            onMouseOver={(e) => {
                              if (!isApproving) {
                                Object.assign(e.currentTarget.style, styles.approveButtonHover);
                              }
                            }}
                            onMouseOut={(e) => {
                              Object.assign(e.currentTarget.style, {
                                backgroundColor: theme === 'dark' ? '#3b82f6' : '#3b82f6',
                                transform: 'translateY(0)',
                                boxShadow: 'none',
                              });
                            }}
                          >
                            {isApproving ? (
                              'Approving...'
                            ) : (
                              <>
                                Approve this version
                                <span style={styles.keyboardShortcut}>Ctrl+⏎</span>
                              </>
                            )}
                          </button>
                        </>
                      )}
                    </div>
                  </>
                )}
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </ModalPortal>
    </div>
  );
};

export default VersionDetailsField;
