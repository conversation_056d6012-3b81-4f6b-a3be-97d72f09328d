'use client';
import { container } from '@/app/(frontend)/utils/constant';
import { useField } from '@payloadcms/ui';
import dynamic from 'next/dynamic';
import Quill from 'quill';
import QuillImageDropAndPaste from 'quill-image-drop-and-paste';
import React, { useCallback, useEffect, useRef, useState } from 'react';

import 'react-quill-new/dist/quill.snow.css';
// export const dynamic = 'force-dynamic'; // Force dynamic rendering
// Use dynamic import with loading optimization
const ReactQuill = dynamic(() => import('react-quill-new'), {
  ssr: false,
  loading: () => (
    <div className="border border-gray-300 rounded p-4 min-h-[200px] bg-gray-50 flex items-center justify-center">
      <div className="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mr-2"></div>
      <span className="text-gray-500">Loading editor...</span>
    </div>
  ),
});
// Register the image drop and paste module
Quill.register('modules/imageDropAndPaste', QuillImageDropAndPaste);

// Define allowed image types
const ALLOWED_IMAGE_TYPES = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
const MAX_FILE_SIZE_MB = 6;
const MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024;
const TOAST_TIMEOUT = 5000; // 5 seconds

interface RichTextEditorProps {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
}

interface ToastMessage {
  message: string;
  type: 'success' | 'error' | 'info';
  id: string; // For managing multiple toasts
}

const RichTextEditor: React.FC<RichTextEditorProps> = ({
  value: propValue,
  onChange: propOnChange,
  placeholder = 'Enter detailed description',
}) => {
  const { value: fieldValue, setValue, errorMessage } = useField({ path: 'description' });

  const value = propValue !== undefined ? propValue : fieldValue;
  const quillRef = useRef<any>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [toasts, setToasts] = useState<ToastMessage[]>([]);
  const [uploadProgress, setUploadProgress] = useState<number | null>(null);

  const addToast = useCallback((message: string, type: 'success' | 'error' | 'info') => {
    const id = Date.now().toString();
    const newToast = { message, type, id };

    setToasts((prev) => [...prev, newToast]);

    setTimeout(() => {
      setToasts((prev) => prev.filter((toast) => toast.id !== id));
    }, TOAST_TIMEOUT);
  }, []);

  const validateFile = useCallback((file: File): { valid: boolean; error?: string } => {
    if (!ALLOWED_IMAGE_TYPES.includes(file.type)) {
      return {
        valid: false,
        error: `Invalid file type. Allowed: ${ALLOWED_IMAGE_TYPES.map((t) => t.replace('image/', '')).join(', ')}`,
      };
    }

    if (file.size > MAX_FILE_SIZE_BYTES) {
      return { valid: false, error: `File exceeds ${MAX_FILE_SIZE_MB}MB limit` };
    }

    return { valid: true };
  }, []);

  const imageHandler = useCallback(() => {
    const input = document.createElement('input');
    input.setAttribute('type', 'file');
    input.setAttribute('accept', ALLOWED_IMAGE_TYPES.join(','));

    const editor = quillRef.current?.getEditor();
    if (editor) editor.focus();

    input.click();

    input.onchange = async () => {
      const file = input.files?.[0];
      if (!file) return;

      const validation = validateFile(file);
      if (!validation.valid) {
        addToast(validation.error || 'Invalid file', 'error');
        return;
      }

      try {
        setIsUploading(true);
        setUploadProgress(0);
        const imageUrl = await uploadImage(file);
        insertImage(imageUrl);
        addToast('Image uploaded successfully', 'success');
      } catch (error) {
        console.error('Upload error:', error);
        addToast(error instanceof Error ? error.message : 'Upload failed', 'error');
      } finally {
        setIsUploading(false);
        setUploadProgress(null);
      }
    };
  }, [addToast, validateFile]);

  // Optimized image upload with debounce and caching
  const uploadImage = useCallback(async (file: File): Promise<string> => {
    // Create a cache key based on file properties
    const cacheKey = `${file.name}-${file.size}-${file.lastModified}`;

    // Check if we have this image cached in sessionStorage
    const cachedUrl = sessionStorage.getItem(`image-cache-${cacheKey}`);
    if (cachedUrl) {
      // Use cached URL if available
      setUploadProgress(100);
      return cachedUrl;
    }

    const formData = new FormData();
    formData.append('file', file);

    const xhr = new XMLHttpRequest();
    const uploadPromise = new Promise<string>((resolve, reject) => {
      // Throttle progress updates to reduce UI updates
      let lastProgressUpdate = 0;
      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          const now = Date.now();
          // Only update progress every 100ms to reduce rendering
          if (now - lastProgressUpdate > 100) {
            const percentComplete = Math.round((event.loaded / event.total) * 100);
            setUploadProgress(percentComplete);
            lastProgressUpdate = now;
          }
        }
      });

      xhr.onload = () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            const data = JSON.parse(xhr.responseText);
            const awsEndpoint = process.env._AWS_END_POINT || '';
            const filename = data.doc?.filename || file.name;
            const imageUrl = `${awsEndpoint}/${filename}`;

            // Cache the URL in sessionStorage
            try {
              sessionStorage.setItem(`image-cache-${cacheKey}`, imageUrl);
            } catch (e) {
              // Ignore storage errors
            }

            resolve(imageUrl);
          } catch {
            reject(new Error('Invalid server response'));
          }
        } else {
          let errorMessage = `Status ${xhr.status}`;
          try {
            const errorData = JSON.parse(xhr.responseText);
            errorMessage = errorData?.error || errorMessage;
          } catch {}
          reject(new Error(errorMessage));
        }
      };

      xhr.onerror = () => reject(new Error('Network error'));
      xhr.ontimeout = () => reject(new Error('Request timed out'));
    });

    xhr.open('POST', `${process.env.NEXT_PUBLIC_BACKEND_URL}/api/upload`, true);
    xhr.timeout = 60000;
    xhr.send(formData);

    return uploadPromise;
  }, []);

  const insertImage = useCallback(
    (imageUrl: string) => {
      const editor = quillRef.current?.getEditor();
      if (!editor) return;

      try {
        editor.focus();
        let range = editor.getSelection() || { index: editor.getLength(), length: 0 };
        editor.insertEmbed(range.index, 'image', imageUrl, 'user');
        editor.setSelection(range.index + 1, 0);
        editor.insertText(range.index + 1, '\n');
      } catch (err) {
        console.error('Insert error:', err);
        addToast('Error inserting image', 'error');
      }
    },
    [addToast],
  );

  const handlePastedImage = useCallback(
    async (_dataUrl: string, type: string, imageData: Blob) => {
      try {
        setIsUploading(true);
        setUploadProgress(0);
        const timestamp = Date.now();
        const file = new File([imageData], `pasted-${timestamp}.png`, { type });

        const validation = validateFile(file);
        if (!validation.valid) {
          addToast(validation.error || 'Invalid pasted image', 'error');
          return;
        }

        const imageUrl = await uploadImage(file);
        insertImage(imageUrl);
        addToast('Pasted image uploaded', 'success');
      } catch (err) {
        console.error('Paste error:', err);
        addToast(err instanceof Error ? err.message : 'Paste upload failed', 'error');
      } finally {
        setIsUploading(false);
        setUploadProgress(null);
      }
    },
    [addToast, insertImage, uploadImage, validateFile],
  );

  // Memoize modules configuration to prevent unnecessary re-renders
  const modules = React.useMemo(
    () => ({
      toolbar: {
        container,
        handlers: {
          image: imageHandler,
        },
      },
      imageDropAndPaste: {
        handler: handlePastedImage,
      },
      clipboard: {
        matchVisual: false,
      },
      // Add performance optimizations
      history: {
        delay: 1000,
        maxStack: 50,
        userOnly: true,
      },
      keyboard: {
        bindings: {
          // Disable some keyboard shortcuts that might cause performance issues
          'list autofill': {
            key: ' ',
            shiftKey: null,
            handler: () => true, // Prevent default behavior
          },
        },
      },
    }),
    [imageHandler, handlePastedImage],
  );

  // useEffect(() => {
  //   if (value === undefined || value === null) {
  //     setValue('');
  //   }
  // }, [value, setValue]);

  useEffect(() => {
    return () => {
      setToasts([]);
    };
  }, []);

  return (
    <div className="rich-text-editor-container relative mb-4">
      {isUploading && (
        <div className="absolute inset-0 bg-white/70 flex flex-col items-center justify-center z-10">
          <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
          {uploadProgress !== null && (
            <div className="mt-2 text-sm text-gray-700">
              Uploading: {uploadProgress}%
              <div className="w-48 h-2 bg-gray-200 rounded-full mt-1">
                <div
                  className="h-full bg-blue-500 rounded-full transition-all duration-300"
                  style={{ width: `${uploadProgress}%` }}
                />
              </div>
            </div>
          )}
        </div>
      )}

      <ReactQuill
        // @ts-ignore
        ref={quillRef as any}
        value={value || ('' as any)}
        onChange={(val) => {
          setValue(val);
          propOnChange?.(val);
        }}
        placeholder={placeholder}
        modules={modules}
        theme="snow"
      />

      {errorMessage && <p className="mt-1 text-red-600 text-sm">{errorMessage}</p>}

      {/* Toast Notifications */}
      <div className="absolute bottom-0 right-0 flex flex-col gap-2 p-2 z-20">
        {toasts.map((toast) => (
          <div
            key={toast.id}
            className={`px-4 py-2 rounded shadow text-white ${
              toast.type === 'error'
                ? 'bg-red-600'
                : toast.type === 'success'
                  ? 'bg-green-600'
                  : 'bg-blue-600'
            }`}
          >
            {toast.message}
          </div>
        ))}
      </div>
    </div>
  );
};

export default RichTextEditor;
