/* tslint:disable */
/* eslint-disable */
/**
 * This file was automatically generated by Payload.
 * DO NOT MODIFY IT BY HAND. Instead, modify your source Payload config,
 * and re-run `payload generate:types` to regenerate this file.
 */

/**
 * Supported timezones in IANA format.
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "supportedTimezones".
 */
export type SupportedTimezones =
  | 'Pacific/Midway'
  | 'Pacific/Niue'
  | 'Pacific/Honolulu'
  | 'Pacific/Rarotonga'
  | 'America/Anchorage'
  | 'Pacific/Gambier'
  | 'America/Los_Angeles'
  | 'America/Tijuana'
  | 'America/Denver'
  | 'America/Phoenix'
  | 'America/Chicago'
  | 'America/Guatemala'
  | 'America/New_York'
  | 'America/Bogota'
  | 'America/Caracas'
  | 'America/Santiago'
  | 'America/Buenos_Aires'
  | 'America/Sao_Paulo'
  | 'Atlantic/South_Georgia'
  | 'Atlantic/Azores'
  | 'Atlantic/Cape_Verde'
  | 'Europe/London'
  | 'Europe/Berlin'
  | 'Africa/Lagos'
  | 'Europe/Athens'
  | 'Africa/Cairo'
  | 'Europe/Moscow'
  | 'Asia/Riyadh'
  | 'Asia/Dubai'
  | 'Asia/Baku'
  | 'Asia/Karachi'
  | 'Asia/Tashkent'
  | 'Asia/Calcutta'
  | 'Asia/Dhaka'
  | 'Asia/Almaty'
  | 'Asia/Jakarta'
  | 'Asia/Bangkok'
  | 'Asia/Shanghai'
  | 'Asia/Singapore'
  | 'Asia/Tokyo'
  | 'Asia/Seoul'
  | 'Australia/Brisbane'
  | 'Australia/Sydney'
  | 'Pacific/Guam'
  | 'Pacific/Noumea'
  | 'Pacific/Auckland'
  | 'Pacific/Fiji';

export interface Config {
  auth: {
    users: UserAuthOperations;
    appUsers: AppUserAuthOperations;
  };
  blocks: {};
  collections: {
    users: User;
    appUsers: AppUser;
    'submit-criteria': SubmitCriterion;
    innovations: Innovation;
    upload: Upload;
    'form-select-options': FormSelectOption;
    flowchart: Flowchart;
    sunburst: Sunburst;
    inquiries: Inquiry;
    'project-versions': ProjectVersion;
    innovationClass: InnovationClass;
    'email-templates': EmailTemplate;
    'payload-locked-documents': PayloadLockedDocument;
    'payload-preferences': PayloadPreference;
    'payload-migrations': PayloadMigration;
  };
  collectionsJoins: {};
  collectionsSelect: {
    users: UsersSelect<false> | UsersSelect<true>;
    appUsers: AppUsersSelect<false> | AppUsersSelect<true>;
    'submit-criteria': SubmitCriteriaSelect<false> | SubmitCriteriaSelect<true>;
    innovations: InnovationsSelect<false> | InnovationsSelect<true>;
    upload: UploadSelect<false> | UploadSelect<true>;
    'form-select-options': FormSelectOptionsSelect<false> | FormSelectOptionsSelect<true>;
    flowchart: FlowchartSelect<false> | FlowchartSelect<true>;
    sunburst: SunburstSelect<false> | SunburstSelect<true>;
    inquiries: InquiriesSelect<false> | InquiriesSelect<true>;
    'project-versions': ProjectVersionsSelect<false> | ProjectVersionsSelect<true>;
    innovationClass: InnovationClassSelect<false> | InnovationClassSelect<true>;
    'email-templates': EmailTemplatesSelect<false> | EmailTemplatesSelect<true>;
    'payload-locked-documents':
      | PayloadLockedDocumentsSelect<false>
      | PayloadLockedDocumentsSelect<true>;
    'payload-preferences': PayloadPreferencesSelect<false> | PayloadPreferencesSelect<true>;
    'payload-migrations': PayloadMigrationsSelect<false> | PayloadMigrationsSelect<true>;
  };
  db: {
    defaultIDType: string;
  };
  globals: {
    'submit-innovation-content': SubmitInnovationContent;
    'innovations-hero': InnovationsHero;
    landingPageUi: LandingPageUi;
    'footer-content': FooterContent;
    'aboutUs-content': AboutUsContent;
    'contactPage-content': ContactPageContent;
    'faq-content': FaqContent;
    'mapOfInnovation-content': MapOfInnovationContent;
  };
  globalsSelect: {
    'submit-innovation-content':
      | SubmitInnovationContentSelect<false>
      | SubmitInnovationContentSelect<true>;
    'innovations-hero': InnovationsHeroSelect<false> | InnovationsHeroSelect<true>;
    landingPageUi: LandingPageUiSelect<false> | LandingPageUiSelect<true>;
    'footer-content': FooterContentSelect<false> | FooterContentSelect<true>;
    'aboutUs-content': AboutUsContentSelect<false> | AboutUsContentSelect<true>;
    'contactPage-content': ContactPageContentSelect<false> | ContactPageContentSelect<true>;
    'faq-content': FaqContentSelect<false> | FaqContentSelect<true>;
    'mapOfInnovation-content':
      | MapOfInnovationContentSelect<false>
      | MapOfInnovationContentSelect<true>;
  };
  locale: null;
  user:
    | (User & {
        collection: 'users';
      })
    | (AppUser & {
        collection: 'appUsers';
      });
  jobs: {
    tasks: unknown;
    workflows: unknown;
  };
}
export interface UserAuthOperations {
  forgotPassword: {
    email: string;
    password: string;
  };
  login: {
    email: string;
    password: string;
  };
  registerFirstUser: {
    email: string;
    password: string;
  };
  unlock: {
    email: string;
    password: string;
  };
}
export interface AppUserAuthOperations {
  forgotPassword: {
    email: string;
    password: string;
  };
  login: {
    email: string;
    password: string;
  };
  registerFirstUser: {
    email: string;
    password: string;
  };
  unlock: {
    email: string;
    password: string;
  };
}
/**
 * Admin panel users
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users".
 */
export interface User {
  id: string;
  updatedAt: string;
  createdAt: string;
  email: string;
  resetPasswordToken?: string | null;
  resetPasswordExpiration?: string | null;
  salt?: string | null;
  hash?: string | null;
  loginAttempts?: number | null;
  lockUntil?: string | null;
  password?: string | null;
}
/**
 * User accounts for the website
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "appUsers".
 */
export interface AppUser {
  id: string;
  name?: string | null;
  updatedAt: string;
  createdAt: string;
  email: string;
  resetPasswordToken?: string | null;
  resetPasswordExpiration?: string | null;
  salt?: string | null;
  hash?: string | null;
  _verified?: boolean | null;
  _verificationToken?: string | null;
  loginAttempts?: number | null;
  lockUntil?: string | null;
  password?: string | null;
}
/**
 * Criteria for submitting innovations
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "submit-criteria".
 */
export interface SubmitCriterion {
  id: string;
  number: number;
  title: string;
  items?:
    | {
        text: string;
        id?: string | null;
      }[]
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 *  Core innovation entries
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "innovations".
 */
export interface Innovation {
  id: string;
  title: string;
  slug: string;
  description: string;
  currentStageOfDevelopment: string | FormSelectOption;
  status?: ('pending' | 'approved' | 'rejected') | null;
  currentVersion?: string | null;
  versions?: (string | ProjectVersion)[] | null;
  name: string;
  email: string;
  phoneNo: string;
  link: string;
  organizationName: string;
  interventionType: (string | FormSelectOption)[];
  targetedTo: string | FormSelectOption;
  supportingDocument?: (string | Upload)[] | null;
  declaration: boolean;
  isFeatured?: boolean | null;
  /**
   * Index of the innovation in the featured list
   */
  featuredIndex?: number | null;
  innovationClass?: (string | null) | InnovationClass;
  versionNumber: number;
  createdBy: string | AppUser;
  updatedAt: string;
  createdAt: string;
}
/**
 * Manage select options for the Innovation Form here.
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "form-select-options".
 */
export interface FormSelectOption {
  id: string;
  /**
   * The value submitted in the form (e.g., "healthcare").
   */
  value: string;
  /**
   * The form field this option belongs to.
   */
  fieldName: 'currentStageOfDevelopment' | 'interventionType' | 'targetedTo';
  /**
   * Toggle to show/hide this option in the form.
   */
  isActive?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * Manage versions of innovations here.
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "project-versions".
 */
export interface ProjectVersion {
  id: string;
  title: string;
  innovation: string;
  description: string;
  currentStageOfDevelopment: string | FormSelectOption;
  status?: ('pending' | 'approved' | 'rejected' | 'archived') | null;
  name: string;
  email: string;
  phoneNo: string;
  link: string;
  organizationName: string;
  interventionType: (string | FormSelectOption)[];
  targetedTo: string | FormSelectOption;
  supportingDocument?: (string | Upload)[] | null;
  declaration: boolean;
  versionNumber: number;
  createdBy?: (string | null) | AppUser;
  updatedAt: string;
  createdAt: string;
}
/**
 * File upload management
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "upload".
 */
export interface Upload {
  id: string;
  alt?: string | null;
  updatedAt: string;
  createdAt: string;
  url?: string | null;
  thumbnailURL?: string | null;
  filename?: string | null;
  mimeType?: string | null;
  filesize?: number | null;
  width?: number | null;
  height?: number | null;
  focalX?: number | null;
  focalY?: number | null;
}
/**
 * Categories/classifications for innovations.
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "innovationClass".
 */
export interface InnovationClass {
  id: string;
  name: string;
  processFrom: string | Flowchart;
  processTo?: (string | null) | Flowchart;
  /**
   * Innovations are automatically added/removed here based on their status and assigned class.
   */
  innovations?: (string | Innovation)[] | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * Data for flowchart visualizations
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "flowchart".
 */
export interface Flowchart {
  id: string;
  name: string;
  type: 'Stage' | 'Process' | 'Innovation';
  parent?: (string | null) | Flowchart;
  innovationclass?: (string | null) | InnovationClass;
  processFrom?: (string | null) | Flowchart;
  processTo?: (string | null) | Flowchart;
  updatedAt: string;
  createdAt: string;
}
/**
 * Data for sunburst visualizations
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "sunburst".
 */
export interface Sunburst {
  id: string;
  title?: string | null;
  innovation?: (string | null) | Innovation;
  innovationClass?: (string | null) | InnovationClass;
  parent?: (string | null) | Sunburst;
  updatedAt: string;
  createdAt: string;
}
/**
 *  User inquiries and communications
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "inquiries".
 */
export interface Inquiry {
  id: string;
  email: string;
  message: string;
  updatedAt: string;
  createdAt: string;
}
/**
 * Manage email templates for system notifications and user communications.
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "email-templates".
 */
export interface EmailTemplate {
  id: string;
  name: string;
  /**
   * Select a trigger to see available variables in the sidebar.
   */
  trigger:
    | 'verify-email'
    | 'welcome-email'
    | 'forgot-password'
    | 'submitted-innovation'
    | 'innovation-approved'
    | 'innovation-rejected';
  /**
   * Use variables in your template by surrounding them with double curly braces: {{variable}}
   */
  html: string;
  /**
   * Available variables for this template:
   */
  variablesHelp?: string | null;
  variables?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents".
 */
export interface PayloadLockedDocument {
  id: string;
  document?:
    | ({
        relationTo: 'users';
        value: string | User;
      } | null)
    | ({
        relationTo: 'appUsers';
        value: string | AppUser;
      } | null)
    | ({
        relationTo: 'submit-criteria';
        value: string | SubmitCriterion;
      } | null)
    | ({
        relationTo: 'innovations';
        value: string | Innovation;
      } | null)
    | ({
        relationTo: 'upload';
        value: string | Upload;
      } | null)
    | ({
        relationTo: 'form-select-options';
        value: string | FormSelectOption;
      } | null)
    | ({
        relationTo: 'flowchart';
        value: string | Flowchart;
      } | null)
    | ({
        relationTo: 'sunburst';
        value: string | Sunburst;
      } | null)
    | ({
        relationTo: 'inquiries';
        value: string | Inquiry;
      } | null)
    | ({
        relationTo: 'project-versions';
        value: string | ProjectVersion;
      } | null)
    | ({
        relationTo: 'innovationClass';
        value: string | InnovationClass;
      } | null)
    | ({
        relationTo: 'email-templates';
        value: string | EmailTemplate;
      } | null);
  globalSlug?: string | null;
  user:
    | {
        relationTo: 'users';
        value: string | User;
      }
    | {
        relationTo: 'appUsers';
        value: string | AppUser;
      };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences".
 */
export interface PayloadPreference {
  id: string;
  user:
    | {
        relationTo: 'users';
        value: string | User;
      }
    | {
        relationTo: 'appUsers';
        value: string | AppUser;
      };
  key?: string | null;
  value?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations".
 */
export interface PayloadMigration {
  id: string;
  name?: string | null;
  batch?: number | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users_select".
 */
export interface UsersSelect<T extends boolean = true> {
  updatedAt?: T;
  createdAt?: T;
  email?: T;
  resetPasswordToken?: T;
  resetPasswordExpiration?: T;
  salt?: T;
  hash?: T;
  loginAttempts?: T;
  lockUntil?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "appUsers_select".
 */
export interface AppUsersSelect<T extends boolean = true> {
  name?: T;
  password?: T;
  updatedAt?: T;
  createdAt?: T;
  email?: T;
  resetPasswordToken?: T;
  resetPasswordExpiration?: T;
  salt?: T;
  hash?: T;
  _verified?: T;
  _verificationToken?: T;
  loginAttempts?: T;
  lockUntil?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "submit-criteria_select".
 */
export interface SubmitCriteriaSelect<T extends boolean = true> {
  number?: T;
  title?: T;
  items?:
    | T
    | {
        text?: T;
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "innovations_select".
 */
export interface InnovationsSelect<T extends boolean = true> {
  title?: T;
  slug?: T;
  description?: T;
  currentStageOfDevelopment?: T;
  status?: T;
  currentVersion?: T;
  versions?: T;
  name?: T;
  email?: T;
  phoneNo?: T;
  link?: T;
  organizationName?: T;
  interventionType?: T;
  targetedTo?: T;
  supportingDocument?: T;
  declaration?: T;
  isFeatured?: T;
  featuredIndex?: T;
  innovationClass?: T;
  versionNumber?: T;
  createdBy?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "upload_select".
 */
export interface UploadSelect<T extends boolean = true> {
  alt?: T;
  updatedAt?: T;
  createdAt?: T;
  url?: T;
  thumbnailURL?: T;
  filename?: T;
  mimeType?: T;
  filesize?: T;
  width?: T;
  height?: T;
  focalX?: T;
  focalY?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "form-select-options_select".
 */
export interface FormSelectOptionsSelect<T extends boolean = true> {
  value?: T;
  fieldName?: T;
  isActive?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "flowchart_select".
 */
export interface FlowchartSelect<T extends boolean = true> {
  name?: T;
  type?: T;
  parent?: T;
  innovationclass?: T;
  processFrom?: T;
  processTo?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "sunburst_select".
 */
export interface SunburstSelect<T extends boolean = true> {
  title?: T;
  innovation?: T;
  innovationClass?: T;
  parent?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "inquiries_select".
 */
export interface InquiriesSelect<T extends boolean = true> {
  email?: T;
  message?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "project-versions_select".
 */
export interface ProjectVersionsSelect<T extends boolean = true> {
  title?: T;
  innovation?: T;
  description?: T;
  currentStageOfDevelopment?: T;
  status?: T;
  name?: T;
  email?: T;
  phoneNo?: T;
  link?: T;
  organizationName?: T;
  interventionType?: T;
  supportingDocument?: T;
  declaration?: T;
  versionNumber?: T;
  createdBy?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "innovationClass_select".
 */
export interface InnovationClassSelect<T extends boolean = true> {
  name?: T;
  processFrom?: T;
  processTo?: T;
  innovations?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "email-templates_select".
 */
export interface EmailTemplatesSelect<T extends boolean = true> {
  name?: T;
  trigger?: T;
  html?: T;
  variablesHelp?: T;
  variables?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents_select".
 */
export interface PayloadLockedDocumentsSelect<T extends boolean = true> {
  document?: T;
  globalSlug?: T;
  user?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences_select".
 */
export interface PayloadPreferencesSelect<T extends boolean = true> {
  user?: T;
  key?: T;
  value?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations_select".
 */
export interface PayloadMigrationsSelect<T extends boolean = true> {
  name?: T;
  batch?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * Manage content for the Submit Innovation page
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "submit-innovation-content".
 */
export interface SubmitInnovationContent {
  id: string;
  heroTitle?: string | null;
  heroDescription?: string | null;
  cardTitle?: string | null;
  cardDescription?: string | null;
  /**
   * Select the criteria and then click on edit to change criteria content
   */
  criteria?: (string | SubmitCriterion)[] | null;
  updatedAt?: string | null;
  createdAt?: string | null;
}
/**
 * Manage content for the Browse Innovations page
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "innovations-hero".
 */
export interface InnovationsHero {
  id: string;
  title: string;
  description: {
    [k: string]: unknown;
  }[];
  updatedAt?: string | null;
  createdAt?: string | null;
}
/**
 * Manage content for the Landing page
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "landingPageUi".
 */
export interface LandingPageUi {
  id: string;
  heroSection: {
    heading: string;
    description: {
      [k: string]: unknown;
    }[];
  };
  marqueeContent: {
    text?: string | null;
    id?: string | null;
  }[];
  mapSection: {
    title: string;
    description: string;
  };
  featuredInnovationsSection: {
    title: string;
    description: string;
  };
  updatedAt?: string | null;
  createdAt?: string | null;
}
/**
 * Manage content for the Footer
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "footer-content".
 */
export interface FooterContent {
  id: string;
  footerTitle: string;
  footerDescription: string;
  updatedAt?: string | null;
  createdAt?: string | null;
}
/**
 * Manage content for the About Us page
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "aboutUs-content".
 */
export interface AboutUsContent {
  id: string;
  heroTitle?: string | null;
  heroDescription?: string | null;
  description?:
    | {
        [k: string]: unknown;
      }[]
    | null;
  updatedAt?: string | null;
  createdAt?: string | null;
}
/**
 * Manage content for the Contact Us page
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "contactPage-content".
 */
export interface ContactPageContent {
  id: string;
  contactTitle: string;
  contactSubtitle: string;
  email: string;
  phone: string;
  address: string;
  updatedAt?: string | null;
  createdAt?: string | null;
}
/**
 * Manage content for the FAQ page
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "faq-content".
 */
export interface FaqContent {
  id: string;
  title: string;
  subtitle: string;
  faqs?:
    | {
        question: string;
        answer: string;
        id?: string | null;
      }[]
    | null;
  updatedAt?: string | null;
  createdAt?: string | null;
}
/**
 * Manage content for the Map of Innovations page
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "mapOfInnovation-content".
 */
export interface MapOfInnovationContent {
  id: string;
  cardTitle?: string | null;
  cardDescription?: string | null;
  updatedAt?: string | null;
  createdAt?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "submit-innovation-content_select".
 */
export interface SubmitInnovationContentSelect<T extends boolean = true> {
  heroTitle?: T;
  heroDescription?: T;
  cardTitle?: T;
  cardDescription?: T;
  criteria?: T;
  updatedAt?: T;
  createdAt?: T;
  globalType?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "innovations-hero_select".
 */
export interface InnovationsHeroSelect<T extends boolean = true> {
  title?: T;
  description?: T;
  updatedAt?: T;
  createdAt?: T;
  globalType?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "landingPageUi_select".
 */
export interface LandingPageUiSelect<T extends boolean = true> {
  heroSection?:
    | T
    | {
        heading?: T;
        description?: T;
      };
  marqueeContent?:
    | T
    | {
        text?: T;
        id?: T;
      };
  mapSection?:
    | T
    | {
        title?: T;
        description?: T;
      };
  featuredInnovationsSection?:
    | T
    | {
        title?: T;
        description?: T;
      };
  updatedAt?: T;
  createdAt?: T;
  globalType?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "footer-content_select".
 */
export interface FooterContentSelect<T extends boolean = true> {
  footerTitle?: T;
  footerDescription?: T;
  updatedAt?: T;
  createdAt?: T;
  globalType?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "aboutUs-content_select".
 */
export interface AboutUsContentSelect<T extends boolean = true> {
  heroTitle?: T;
  heroDescription?: T;
  description?: T;
  updatedAt?: T;
  createdAt?: T;
  globalType?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "contactPage-content_select".
 */
export interface ContactPageContentSelect<T extends boolean = true> {
  contactTitle?: T;
  contactSubtitle?: T;
  email?: T;
  phone?: T;
  address?: T;
  updatedAt?: T;
  createdAt?: T;
  globalType?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "faq-content_select".
 */
export interface FaqContentSelect<T extends boolean = true> {
  title?: T;
  subtitle?: T;
  faqs?:
    | T
    | {
        question?: T;
        answer?: T;
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
  globalType?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "mapOfInnovation-content_select".
 */
export interface MapOfInnovationContentSelect<T extends boolean = true> {
  cardTitle?: T;
  cardDescription?: T;
  updatedAt?: T;
  createdAt?: T;
  globalType?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "auth".
 */
export interface Auth {
  [k: string]: unknown;
}

declare module 'payload' {
  export interface GeneratedTypes extends Config {}
}
