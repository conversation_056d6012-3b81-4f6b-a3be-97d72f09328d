import { slateEditor } from '@payloadcms/richtext-slate';
import { GlobalConfig } from 'payload';

const LandingPageContent: GlobalConfig = {
  slug: 'landingPageUi',
  label: 'Landing Page Content',
  admin: {
    group: 'Website Content', // This groups it under "Website" in the sidebar
    description: 'Manage content for the Landing page',
    livePreview: {
      url: ({ globalConfig }) =>
        `${process.env.NEXT_PUBLIC_BACKEND_URL}/preview/${globalConfig?.slug}`, // or your frontend URL
    },
    hideAPIURL: true,
  },
  access: {
    read: () => true,
  },
  fields: [
    {
      name: 'heroSection',
      type: 'group',
      fields: [
        {
          name: 'heading',
          type: 'text',
          required: true,
          defaultValue: 'Atlas of Innovations: Mapping the Future',
        },
        {
          name: 'description',
          type: 'richText',
          required: true,
          editor: slateEditor({
            admin: {
              elements: ['h1', 'h2', 'h3', 'h4', 'blockquote'],
              leaves: ['bold', 'italic'],
              link: {
                fields: [
                  {
                    name: 'rel',
                    label: 'Rel Attribute',
                    type: 'select',
                    hasMany: true,
                    options: ['noopener', 'noreferrer', 'nofollow'],
                  },
                ],
              },
            },
          }),
          defaultValue: [
            {
              children: [
                {
                  text: 'The "Atlas of Innovations" is a web application designed to showcase innovations in',
                },
                { text: 'Tuberculosis (TB) management.', bold: true },
              ],
            },
          ],
        },
        {
          name: 'sliderImages',
          label: 'Slider Images',
          type: 'array',
          admin: {
            description: 'Images to be displayed in the hero section slider',
          },
          fields: [
            {
              name: 'image',
              type: 'relationship',
              relationTo: 'upload',
              required: true,
              admin: {
                description: 'Select an image for the slider',
              },
            },
            {
              name: 'alt',
              type: 'text',
              required: true,
              admin: {
                description: 'Alternative text for the image (for accessibility)',
              },
            },
          ],
        },
      ],
    },
    {
      name: 'marqueeContent',
      type: 'array',
      required: true,
      fields: [
        {
          name: 'text',
          type: 'text',
        },
      ],
    },
    {
      name: 'mapSection',
      type: 'group',
      fields: [
        {
          name: 'title',
          type: 'text',
          required: true,
          defaultValue: 'Map of Innovations',
        },
        {
          name: 'description',
          type: 'textarea',
          required: true,
          defaultValue:
            "The four pillars of tuberculosis (TB) are Detect, Treat, Prevent, and Build (DTPB). These pillars are part of the National Strategic Plan (NSP) for TB elimination in India, which was in effect from 2017 to 2025. The NSP's goal was to reduce the number of TB cases, deaths, and out-of-pocket costs.",
        },
      ],
    },
    {
      name: 'featuredInnovationsSection',
      type: 'group',
      fields: [
        {
          name: 'title',
          type: 'text',
          required: true,
          defaultValue: 'Featured Innovations',
        },
        {
          name: 'description',
          type: 'textarea',
          required: true,
          defaultValue:
            'Featured Innovations showcases groundbreaking ideas, cutting-edge technologies, and visionary projects that are shaping the future. Aligned with the four pillars of the TB Program—Detect, Treat, Prevent, and Build (DTPB)—this section highlights innovations that enhance early detection, improve treatment methods, advance prevention strategies, and strengthen healthcare systems. Explore how transformative solutions are driving global progress in the fight against TB and beyond.',
        },
      ],
    },
    {
      name: 'imageSliderSection',
      type: 'group',
      admin: {
        description: 'Configure the standalone image slider section',
      },
      fields: [
        {
          name: 'title',
          type: 'text',
          defaultValue: 'Image Gallery',
        },
        {
          name: 'description',
          type: 'textarea',
          defaultValue: 'Explore our collection of images showcasing innovations and initiatives.',
        },
        {
          name: 'sliderImages',
          label: 'Slider Images',
          type: 'array',
          admin: {
            description: 'Images to be displayed in the standalone slider',
          },
          fields: [
            {
              name: 'image',
              type: 'relationship',
              relationTo: 'upload',
              required: true,
              admin: {
                description: 'Select an image for the slider',
              },
            },
            {
              name: 'alt',
              type: 'text',
              required: true,
              admin: {
                description: 'Alternative text for the image (for accessibility)',
              },
            },
            {
              name: 'caption',
              type: 'text',
              admin: {
                description: 'Optional caption to display with the image',
              },
            },
            {
              name: 'link',
              type: 'text',
              admin: {
                description: 'Optional URL to link this image to',
              },
            },
          ],
        },
        {
          name: 'displayOptions',
          type: 'group',
          admin: {
            description: 'Configure how the slider should be displayed',
          },
          fields: [
            {
              name: 'autoplay',
              type: 'checkbox',
              defaultValue: true,
              admin: {
                description: 'Automatically cycle through images',
              },
            },
            {
              name: 'autoplaySpeed',
              type: 'number',
              defaultValue: 5000,
              admin: {
                description: 'Time in milliseconds between slides (if autoplay is enabled)',
              },
            },
            {
              name: 'showDots',
              type: 'checkbox',
              defaultValue: true,
              admin: {
                description: 'Show navigation dots',
              },
            },
            {
              name: 'showArrows',
              type: 'checkbox',
              defaultValue: true,
              admin: {
                description: 'Show navigation arrows',
              },
            },
          ],
        },
      ],
    },
  ],
};

export default LandingPageContent;
