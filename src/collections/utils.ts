// utils.ts (or similar)
import config from '@payload-config';
import { getPayload, Payload } from 'payload';

// Simple In-Memory Cache (Consider Redis/Memcached for production)
const cacheStore: Record<string, { data: any; expiresAt: number }> = {};

export const getCache = (key: string) => {
  const item = cacheStore[key];
  if (item && item.expiresAt > Date.now()) {
    console.log(`Cache HIT for key: ${key}`);
    return item.data;
  }
  if (item) {
    console.log(`Cache EXPIRED for key: ${key}`);
    delete cacheStore[key];
  } else {
    console.log(`Cache MISS for key: ${key}`);
  }
  return null;
};

export const setCache = (key: string, data: any, ttl: number = 300000) => {
  // 5 minutes TTL
  console.log(`Cache SET for key: ${key} with TTL: ${ttl}ms`);
  cacheStore[key] = {
    data,
    expiresAt: Date.now() + ttl,
  };
};

export const clearCache = (key: string) => {
  console.log(`Cache CLEAR for key: ${key}`);
  delete cacheStore[key];
};

// ---

export async function documentExists(
  collection: any,
  id: string,
  payload: Payload,
): Promise<boolean> {
  if (!id) return false; // Prevent unnecessary lookups
  try {
    await payload.findByID({
      collection,
      id,
      depth: 0,
    });
    return true;
  } catch (error: any) {
    // Check if it's Payload's NotFound error or a generic 404 status
    if (error?.name === 'NotFound' || error?.status === 404) {
      return false;
    }
    console.error(`Error checking existence for ${collection} ID ${id}:`, error);
    throw error; // Re-throw other errors
  }
}

// ---

export const getNextVersionNumber = async (
  payload: Payload,
  innovationId: string,
): Promise<number> => {
  if (!innovationId) {
    console.error('getNextVersionNumber called with null/undefined innovationId');
    return 1; // Or throw an error, depending on desired handling
  }
  try {
    const latestVersion = await payload.find({
      collection: 'project-versions',
      where: { innovation: { equals: innovationId } },
      sort: '-versionNumber',
      limit: 1,
      depth: 0,
      overrideAccess: true, // Ensure we can find versions regardless of permissions
    });

    if (latestVersion.docs.length > 0 && latestVersion.docs[0]?.versionNumber !== undefined) {
      return (latestVersion.docs[0].versionNumber || 0) + 1;
    }
    // If no versions exist yet for this innovation
    return 1; // Start versioning at 1
  } catch (error) {
    console.error(`Error fetching latest version number for innovation ${innovationId}:`, error);
    // Depending on policy, either throw or return a default
    // Returning 1 assumes this might be the first attempt
    return 1;
  }
};

export async function syncInnovationClass(
  innovationId: string,
  isApproved: boolean, // The *new* status after the change
  newClassId: string | null | undefined, // The *new* class assigned (or null)
  oldClassId: string | null | undefined, // The *previous* class assigned (or null)
  payload: Payload,
) {
  const context = { triggerAfterChange: false }; // Prevent hook loops

  // --- Step 1: Handle Removal from the OLD Class (if the class changed) ---
  if (oldClassId && oldClassId !== newClassId) {
    console.log(
      `[SyncClass] Innovation ${innovationId}: Class changed from ${oldClassId} to ${newClassId}. Attempting removal from OLD class ${oldClassId}.`,
    );
    if (await documentExists('innovationClass', oldClassId, payload)) {
      try {
        const oldClass = await payload.findByID({
          collection: 'innovationClass',
          id: oldClassId,
          depth: 0,
        });

        const currentInnovations =
          oldClass.innovations?.map((i: any) => (typeof i === 'object' ? i.id : i)) || [];

        if (currentInnovations.includes(innovationId)) {
          const updatedInnovations = currentInnovations.filter((id: string) => id !== innovationId);
          await payload.update({
            collection: 'innovationClass',
            id: oldClassId,
            data: { innovations: updatedInnovations },
            context,
            overrideAccess: true,
          });
          console.log(
            `[SyncClass] Innovation ${innovationId}: Successfully removed from OLD class ${oldClassId}.`,
          );
        } else {
          console.log(
            `[SyncClass] Innovation ${innovationId}: Was not found in OLD class ${oldClassId} list.`,
          );
        }
      } catch (error) {
        console.error(
          `[SyncClass] Innovation ${innovationId}: Error removing from OLD class ${oldClassId}:`,
          error,
        );
      }
    } else {
      console.warn(
        `[SyncClass] Innovation ${innovationId}: OLD InnovationClass ${oldClassId} not found during sync.`,
      );
    }
  }

  // --- Step 2: Handle Addition to the NEW Class (if approved and assigned) ---
  if (newClassId && isApproved) {
    console.log(
      `[SyncClass] Innovation ${innovationId}: Is APPROVED and assigned to class ${newClassId}. Attempting addition/verification.`,
    );
    if (await documentExists('innovationClass', newClassId, payload)) {
      try {
        const newClass = await payload.findByID({
          collection: 'innovationClass',
          id: newClassId,
          depth: 0,
        });

        const existingInnovations =
          newClass.innovations?.map((i: any) => (typeof i === 'object' ? i.id : i)) || [];

        // Add only if it's not already there
        if (!existingInnovations.includes(innovationId)) {
          await payload.update({
            collection: 'innovationClass',
            id: newClassId,
            data: { innovations: [...existingInnovations, innovationId] },
            context,
            overrideAccess: true,
          });
          console.log(
            `[SyncClass] Innovation ${innovationId}: Successfully ADDED to NEW class ${newClassId}.`,
          );
        } else {
          console.log(
            `[SyncClass] Innovation ${innovationId}: Already exists in NEW class ${newClassId} list.`,
          );
        }
      } catch (error) {
        console.error(
          `[SyncClass] Innovation ${innovationId}: Error adding to NEW class ${newClassId}:`,
          error,
        );
      }
    } else {
      console.warn(
        `[SyncClass] Innovation ${innovationId}: NEW InnovationClass ${newClassId} not found during sync.`,
      );
    }
  }
  // --- Step 3: Handle Removal from the NEW (Current) Class (if NOT approved but assigned) ---
  // This covers transitions like Approved -> Pending/Rejected, or if assigned while Pending.
  else if (newClassId && !isApproved) {
    console.log(
      `[SyncClass] Innovation ${innovationId}: Is NOT APPROVED but assigned to class ${newClassId}. Attempting removal.`,
    );
    if (await documentExists('innovationClass', newClassId, payload)) {
      try {
        // Fetch the class it's currently (but shouldn't be) linked to
        const currentClass = await payload.findByID({
          collection: 'innovationClass',
          id: newClassId,
          depth: 0,
        });

        const currentInnovations =
          currentClass.innovations?.map((i: any) => (typeof i === 'object' ? i.id : i)) || [];

        // If it's found in the list, remove it
        if (currentInnovations.includes(innovationId)) {
          const updatedInnovations = currentInnovations.filter((id: string) => id !== innovationId);
          await payload.update({
            collection: 'innovationClass',
            id: newClassId,
            data: { innovations: updatedInnovations },
            context,
            overrideAccess: true,
          });
          console.log(
            `[SyncClass] Innovation ${innovationId}: Successfully REMOVED from its current (but now invalid) class ${newClassId} because it's not approved.`,
          );
        } else {
          console.log(
            `[SyncClass] Innovation ${innovationId}: Was not found in class ${newClassId} list during non-approved removal check.`,
          );
        }
      } catch (error) {
        console.error(
          `[SyncClass] Innovation ${innovationId}: Error removing non-approved innovation from class ${newClassId}:`,
          error,
        );
      }
    } else {
      // This case should be rare if the innovation points to a class, but handle it.
      console.warn(
        `[SyncClass] Innovation ${innovationId}: Current InnovationClass ${newClassId} (assigned while not approved) not found during sync.`,
      );
    }
  }
  // --- Step 4: Log if no action taken (optional) ---
  else if (!newClassId) {
    console.log(`[SyncClass] Innovation ${innovationId}: No class assigned. No action needed.`);
  }
}

export function renderTemplate(template: string, variables: Record<string, any>): string {
  return template.replace(/{{(.*?)}}/g, (_, key) => {
    const trimmedKey = key.trim();
    return variables[trimmedKey] ?? '';
  });
}

export async function sendTriggeredEmail(
  trigger: string,
  to: string,
  variables: Record<string, any>,
) {
  const payload = await getPayload({ config });
  const templates = await payload.find({
    collection: 'email-templates',
    where: {
      trigger: {
        equals: trigger,
      },
    },
  });

  const template: any = templates?.docs?.[0];

  if (!template) {
    console.warn(`Email template for trigger "${trigger}" not found`);
    return;
  }

  const html = renderTemplate(template.html, variables);

  await payload.sendEmail({
    from: `Atlas of Innovations <${process.env.SMTP_FROM || ''}>`,
    to,
    subject: template?.subject,
    html,
  });
}

export async function getEmailHTMLFromTemplate(
  trigger: string,
  variables: Record<string, any>,
): Promise<string> {
  const payload = await getPayload({ config });

  const templates = await payload.find({
    collection: 'email-templates',
    where: {
      trigger: {
        equals: trigger,
      },
    },
  });

  const template = templates?.docs?.[0];

  if (!template) {
    console.warn(`Email template for trigger "${trigger}" not found`);
    return '';
  }

  return renderTemplate(template.html, variables);
}
