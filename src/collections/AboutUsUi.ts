import { slateEditor } from '@payloadcms/richtext-slate';
import { GlobalConfig } from 'payload';

const aboutUsContent: GlobalConfig = {
  slug: 'aboutUs-content',
  access: {
    read: () => true,
  },
  admin: {
    group: 'Website Content', // This groups it under "Website" in the sidebar
    description: 'Manage content for the About Us page',
    livePreview: {
      url: ({ globalConfig }) =>
        `${process.env.NEXT_PUBLIC_BACKEND_URL}/preview/${globalConfig?.slug}`, // or your frontend URL
    },
    hideAPIURL: true,
  },
  fields: [
    {
      name: 'heroTitle',
      type: 'text',
      label: 'Card Title',
    },
    {
      name: 'heroDescription',
      type: 'textarea',
      label: 'Card Description',
    },
    {
      name: 'description',
      type: 'richText',
      label: 'Description',
      editor: slateEditor({
        admin: {
          elements: ['h1', 'h2', 'h3', 'h4', 'blockquote'],
          leaves: ['bold', 'italic'],
          link: {
            fields: [
              {
                name: 'rel',
                label: 'Rel Attribute',
                type: 'select',
                hasMany: true,
                options: ['noopener', 'noreferrer', 'nofollow'],
              },
            ],
          },
        },
      }),
    },
  ],
};

export default aboutUsContent;
