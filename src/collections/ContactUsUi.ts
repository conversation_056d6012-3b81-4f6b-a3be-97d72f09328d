import { GlobalConfig } from 'payload';

const ContactPage: GlobalConfig = {
  slug: 'contactPage-content',
  label: 'Contact Page',
  access: {
    read: () => true, // Allow public read access
  },
  admin: {
    group: 'Website Content', // This groups it under "Website" in the sidebar
    description: 'Manage content for the Contact Us page',
    livePreview: {
      url: ({ globalConfig }) =>
        `${process.env.NEXT_PUBLIC_BACKEND_URL}/preview/${globalConfig?.slug}`, // or your frontend URL
    },
    hideAPIURL: true,
  },
  fields: [
    {
      name: 'contactTitle',
      type: 'text',
      label: 'Contact Section Title',
      defaultValue: 'Contact Us',
      required: true,
    },
    {
      name: 'contactSubtitle',
      type: 'textarea',
      label: 'Contact Section Subtitle',
      defaultValue:
        'Have questions, ideas, or collaboration opportunities? We’re dedicated to driving innovation in TB control and would love to hear from you.',
      required: true,
    },
    {
      name: 'email',
      type: 'text',
      label: 'Email',
      defaultValue: '<EMAIL>',
      required: true,
    },
    {
      name: 'phone',
      type: 'text',
      label: 'Phone',
      defaultValue: '011-26588296',
      required: true,
    },
    {
      name: 'address',
      type: 'textarea',
      label: 'Address',
      defaultValue: `Division of Communicable Diseases, ICMR
V. Ramalingaswami Bhawan
P.O. Box No. 4911, Ansari Nagar,
New Delhi - 110029, India`,
      required: true,
    },
  ],
};

export default ContactPage;
