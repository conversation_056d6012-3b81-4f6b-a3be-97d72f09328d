import { GlobalConfig } from 'payload';

const FooterContent: GlobalConfig = {
  slug: 'footer-content',
  label: 'Footer Content',
  access: {
    read: () => true,
  },
  admin: {
    group: 'Website Content', // This groups it under "Website" in the sidebar
    description: 'Manage content for the Footer',
    livePreview: {
      url: ({ globalConfig }) =>
        `${process.env.NEXT_PUBLIC_BACKEND_URL}/preview/${globalConfig?.slug}`, // or your frontend URL
    },
    hideAPIURL: true,
  },
  fields: [
    {
      type: 'text',
      name: 'footerTitle',
      label: 'Footer Section Title',
      required: true,
      defaultValue: 'Atlas of Innovations',
    },
    {
      type: 'textarea',
      name: 'footerDescription',
      label: 'Footer Section Description',
      required: true,
      defaultValue: 'We are committed to advancing TB care through innovation and collaboration.',
    },
  ],
};

export default FooterContent;
