import { slateEditor } from '@payloadcms/richtext-slate';
import { GlobalConfig } from 'payload';

const browseInnovationContent: GlobalConfig = {
  slug: 'innovations-hero',
  label: 'Browse Innovations Content', // Admin UI label

  admin: {
    group: 'Website Content', // This groups it under "Website" in the sidebar
    description: 'Manage content for the Browse Innovations page',
    livePreview: {
      url: ({ globalConfig }) =>
        `${process.env.NEXT_PUBLIC_BACKEND_URL}/preview/${globalConfig?.slug}`, // or your frontend URL
    },
    hideAPIURL: true,
  },
  access: {
    read: () => true, // Allow anyone to read
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
      defaultValue: 'Browse Innovations',
    },
    {
      name: 'description',
      type: 'richText',
      required: true,
      editor: slateEditor({
        admin: {
          elements: ['h1', 'h2', 'h3', 'h4', 'blockquote'],
          leaves: ['bold', 'italic'],
          link: {
            fields: [
              {
                name: 'rel',
                label: 'Rel Attribute',
                type: 'select',
                hasMany: true,
                options: ['noopener', 'noreferrer', 'nofollow'],
              },
            ],
          },
        },
      }),
      defaultValue: [
        {
          children: [
            { text: 'Innovations showcases cutting-edge ' },
            { text: 'solutions', bold: true },
            { text: ', breakthrough ' },
            { text: 'technologies', bold: true },
            { text: ', and ' },
            { text: 'transformative ideas', bold: true },
            { text: ' that drive progress. Explore advancements ' },
            { text: 'shaping the future', bold: true },
            { text: ' across various industries.' },
          ],
        },
      ],
    },
  ],
};

export default browseInnovationContent;
