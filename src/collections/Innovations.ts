import ConfirmSaveEdit from '@/admin/components/ConfirmSaveEdit';
import RichTextEditor from '@/admin/components/RichTextEditor';
import VersionDetailsFeild from '@/admin/components/VersionDetailsFeild';
import { CollectionConfig } from 'payload';
import { getNextVersionNumber, sendTriggeredEmail, syncInnovationClass } from './utils';

const Innovations: CollectionConfig = {
  slug: 'innovations',
  labels: {
    singular: 'Innovation',
    plural: 'Innovations',
  },
  admin: {
    components: {
      edit: {
        SaveButton: ConfirmSaveEdit as any,
      },
    },
    useAsTitle: 'title',
    description: ' Core innovation entries',
    group: 'Innovation Management',
    hideAPIURL: true,
  },
  endpoints: [
    {
      path: '/top4',
      method: 'get',
      handler: async (req) => {
        try {
          const innovations: any = await req.payload.find({
            collection: 'innovations',
            where: {
              status: { equals: 'approved' },
              isFeatured: { equals: true },
            },
            sort: 'featuredIndex',
            limit: 4,
          });
          const formattedInnovations = {
            docs: innovations.docs.map((doc: any) => ({
              id: doc?.id,
              title: doc?.title,
              description: doc?.description,
              link: doc?.link,
              organizationName: doc?.organizationName,
              slug: doc?.slug,
              bannerImage: doc?.bannerImage,
            })),
          };
          return new Response(JSON.stringify(formattedInnovations), {
            status: 200,
            headers: { 'Content-Type': 'application/json' },
          });
        } catch (error) {
          return Response.json({ error: error }, { status: 500 });
        }
      },
    },
    {
      path: '/innovations',
      method: 'get',
      handler: async (req: any) => {
        try {
          const { payload, user } = req;
          const { searchParams } = new URL(req.url);
          const [page, limit, category, interventionType, stage, myInnovations, searchQuery] = [
            parseInt(searchParams.get('page') || '1', 10),
            parseInt(searchParams.get('limit') || '10', 10),
            searchParams.get('category')?.split(',') || [],
            searchParams.get('interventionType')?.split(',') || [],
            searchParams.get('currentStageOfDevelopment')?.split(',') || [],
            searchParams.get('myInnovations') === 'true',
            searchParams.get('searchQuery') || '',
          ];

          // Fetch form options directly without caching
          const formOptions = await payload.find({
            collection: 'form-select-options',
            pagination: false,
          });

          const cachedMappings = formOptions.docs.reduce(
            (acc: any, option: any) => {
              const key = `${option.fieldName}:${option.value.toLowerCase()}`;
              acc[key] = option.id;
              return acc;
            },
            {} as Record<string, string>,
          );
          // Use cached mappings with case-insensitive lookup
          const getCachedId = (field: string, value: string) =>
            cachedMappings[`${field}:${value.toLowerCase()}`] || '';

          // Build the `where` clause only if filters are applied
          const where: any = {};

          // Log authentication info for debugging
          console.log('myInnovations:', myInnovations);
          console.log('user:', user ? `User ID: ${user.id}` : 'No user');

          // Default behavior: Show only approved innovations
          if (myInnovations && user) {
            // My Innovations page - show only user's innovations (both approved and pending)
            where.status = { in: ['approved', 'pending'] };
            where.createdBy = { equals: user.id };
          } else {
            // Browse Innovations page - show only approved innovations
            where.status = { equals: 'approved' };
          }

          // Apply filters only if values exist
          if (category.length && category[0] !== '') {
            where.category = { in: category.map((v) => getCachedId('category', v)) };
          }
          if (interventionType.length && interventionType[0] !== '') {
            where.interventionType = {
              in: interventionType.map((v) => getCachedId('interventionType', v)),
            };
          }
          if (stage.length && stage[0] !== '') {
            where.currentStageOfDevelopment = {
              in: stage.map((v) => getCachedId('currentStageOfDevelopment', v)),
            };
          }
          if (searchQuery.trim() !== '') {
            where.or = [
              { title: { contains: searchQuery } },
              { description: { contains: searchQuery } },
            ];
          }
          // Fetch innovations with the applied `where` clause only if it has conditions
          const queryParams: any = {
            collection: 'innovations',
            limit,
            page,
            where,
            overrideAccess: true,
            showHiddenFields: true,
            depth: 0,
          };
          const innovations = await payload.find(queryParams);

          const docs = innovations.docs.map((doc: any) => ({
            id: doc?.id,
            title: doc?.title,
            description: doc?.description,
            status: doc?.status,
            link: doc?.link,
            organizationName: doc?.organizationName,
            slug: doc?.slug,
            bannerImage: doc?.bannerImage,
          }));

          return new Response(
            JSON.stringify({
              docs,
              totalDocs: innovations.totalDocs,
              limit,
              page,
              totalPages: innovations.totalPages,
              hasPrevPage: innovations.hasPrevPage,
              hasNextPage: innovations.hasNextPage,
              prevPage: innovations.prevPage,
              nextPage: innovations.nextPage,
            }),
            {
              status: 200,
              headers: {
                'Content-Type': 'application/json',
              },
            },
          );
        } catch (error) {
          console.error('API Error:', error);
          return new Response(JSON.stringify({ error: 'Internal Server Error' }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' },
          });
        }
      },
    },

    {
      path: '/total',
      method: 'get',
      handler: async (req) => {
        try {
          const totalApproved = await req.payload.count({
            collection: 'innovations',
            where: { status: { equals: 'approved' } }, // ✅ Count only approved innovations
          });
          return new Response(JSON.stringify(totalApproved), {
            status: 200,
            headers: { 'Content-Type': 'application/json' },
          });
        } catch (error) {
          return new Response(JSON.stringify({ error: error || 'Server error' }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' },
          });
        }
      },
    },
    {
      path: '/innovation/:id',
      method: 'get',
      handler: async (req: any) => {
        try {
          const { payload, user } = req;
          console.log('user=====================', user);

          const { id } = req.routeParams;

          // Validate ID presence
          if (!id) {
            return new Response(JSON.stringify({ error: 'Innovation ID is required' }), {
              status: 400,
              headers: { 'Content-Type': 'application/json' },
            });
          }

          // Fetch the innovation
          const innovation = await payload.findByID({
            collection: 'innovations',
            id: id,
          });

          if (!innovation) {
            return new Response(JSON.stringify({ error: 'Innovation not found' }), {
              status: 404,
              headers: { 'Content-Type': 'application/json' },
            });
          }

          // Check if the logged-in user is the creator
          if (!user?.id || String(innovation.createdBy?.id) !== String(user.id)) {
            return new Response(JSON.stringify({ error: 'Unauthorized access' }), {
              status: 403,
              headers: { 'Content-Type': 'application/json' },
            });
          }

          // Prepare response data
          const docs = {
            title: innovation.title || '',
            description: innovation.description || '',
            link: innovation.link || '',
            organizationName: innovation.organizationName || '',
            currentStageOfDevelopment: innovation.currentStageOfDevelopment
              ? {
                  value: innovation.currentStageOfDevelopment.value,
                  id: innovation.currentStageOfDevelopment.id,
                }
              : null,
            category: Array.isArray(innovation.category)
              ? innovation.category.map((cat: any) => ({ value: cat.value, id: cat.id }))
              : [],
            name: innovation.name || '',
            email: innovation.email || '',
            phoneNo: innovation.phoneNo || '',
            interventionType: Array.isArray(innovation.interventionType)
              ? innovation.interventionType.map((intervention: any) => ({
                  id: intervention.id,
                  value: intervention.value,
                }))
              : [],
            declaration: innovation.declaration || false,
            supportingDocument: innovation.supportingDocument || null,
            id: innovation.id,
            bannerImage: innovation.bannerImage || null,
          };

          return new Response(JSON.stringify(docs), {
            status: 200,
            headers: {
              'Content-Type': 'application/json',
            },
          });
        } catch (error) {
          console.error('API Error:', error);
          return new Response(JSON.stringify({ error: 'Internal Server Error' }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' },
          });
        }
      },
    },
  ],
  access: {
    create: async ({ req }) => {
      if (!req.user) return false;
      try {
        const appUser = await req.payload.find({
          collection: 'appUsers',
          where: { id: { equals: req.user.id } },
          depth: 0,
          pagination: false,
        });

        return appUser.docs.length > 0 && appUser.docs[0].id ? true : false; // Only allow admins
      } catch (error) {
        console.error('Error fetching appUser:', error);
        return false;
      }
    },
    delete: async ({ req }) => {
      if (!req.user) return false;

      try {
        const appUser = await req.payload.find({
          collection: 'users',
          where: { id: { equals: req.user.id } },
        });

        return appUser.docs.length > 0 && appUser.docs[0].id ? true : false;
      } catch (error) {
        console.error('Error fetching appUser:', error);
        return false;
      }
    },
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      unique: true,
      required: true,
    },
    {
      name: 'slug',
      type: 'text',
      unique: true,
      required: true,
      admin: {
        position: 'sidebar',
        hidden: true,
      },
      hooks: {
        beforeValidate: [
          ({ siblingData }) => {
            if (!siblingData.slug && siblingData.title) {
              return siblingData.title
                .toLowerCase()
                .replace(/[^a-z0-9]+/g, '-')
                .replace(/(^-|-$)/g, '');
            }
          },
        ],
      },
    },
    {
      name: 'description',
      type: 'textarea',
      required: true,
      admin: {
        description: 'Enter a brief description of the innovation',
        hidden: true,
      },
    },
    {
      name: 'descriptionEditor',
      type: 'ui',
      admin: {
        components: {
          Field: RichTextEditor as any,
        },
      },
      label: 'Description',
    },
    {
      name: 'currentStageOfDevelopment',
      type: 'relationship',
      relationTo: 'form-select-options',
      required: true,
      filterOptions: () => {
        return {
          fieldName: { equals: 'currentStageOfDevelopment' },
          isActive: { equals: true },
        };
      },
    },
    {
      name: 'status',
      type: 'select',
      options: [
        { label: 'Pending', value: 'pending' },
        { label: 'Approved', value: 'approved' },
        { label: 'Rejected', value: 'rejected' },
      ],
      defaultValue: 'pending',
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'currentVersion',
      type: 'text',
      admin: {
        hidden: true,
      },
    },
    {
      name: 'versions',
      type: 'relationship',
      relationTo: 'project-versions',
      hasMany: true,
      admin: {
        position: 'sidebar',
        hidden: true,
      },
      filterOptions: ({ id, data }) => {
        return {
          innovation: { equals: id },
          id: { not_equals: data.currentVersion },
        };
      },
    },
    {
      name: 'versionUi',
      type: 'ui',
      admin: {
        components: {
          Field: VersionDetailsFeild as any,
        },
        position: 'sidebar',
      },
    },
    {
      name: 'name',
      type: 'text',
      required: true,
    },
    {
      name: 'email',
      type: 'email',
      required: true,
    },
    {
      name: 'phoneNo',
      type: 'text',
      required: true,
    },
    {
      name: 'link',
      type: 'text',
      required: true,
    },
    {
      name: 'organizationName',
      type: 'text',
      required: true,
    },
    {
      name: 'interventionType',
      type: 'relationship',
      relationTo: 'form-select-options',
      required: true,
      hasMany: true,
      filterOptions: () => {
        return {
          fieldName: { equals: 'interventionType' },
          isActive: { equals: true },
        };
      },
      admin: {
        // Sort options by their order field
        description: 'Select one or more intervention types (sorted by display order)',
      },
    },
    {
      name: 'supportingDocument',
      type: 'relationship',
      relationTo: 'upload',
      required: false,
      hasMany: true,
    },
    {
      name: 'bannerImage',
      type: 'relationship',
      relationTo: 'upload',
      required: false,
      hasMany: false,
    },
    {
      name: 'declaration',
      type: 'checkbox',
      required: true,
      validate: (val) => {
        return val === true ? true : 'You must agree to the declaration.';
      },
    },
    {
      name: 'isFeatured',
      type: 'checkbox',
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'featuredIndex',
      type: 'number',
      admin: {
        description: 'Index of the innovation in the featured list',
        condition: (data) => data?.isFeatured,
        position: 'sidebar',
      },
    },
    /**
     * Innovation Class Relationship
     *
     * This field establishes a relationship between an innovation and an innovation class.
     * Each innovation can belong to one innovation class, which helps categorize and organize
     * innovations based on their characteristics, purpose, or domain.
     *
     * The relationship is bidirectional - when an innovation is assigned to a class:
     * 1. The innovation gets this field populated with the class ID
     * 2. The innovation class's 'innovations' array gets updated to include this innovation
     *
     * This relationship is used for:
     * - Filtering innovations by class on the frontend
     * - Organizing innovations in visualizations
     * - Creating logical groupings for reporting and analysis
     */
    {
      name: 'innovationClass',
      type: 'relationship',
      relationTo: 'innovationClass',
      hasMany: false,
      admin: {
        position: 'sidebar',
        description: 'Select the innovation class this innovation belongs to',
      },
    },

    /**
     * Innovation Class Details
     *
     * This group field contains additional metadata about how this innovation
     * relates to its assigned innovation class. These details enhance the basic
     * class relationship with specific attributes that affect how the innovation
     * is displayed, categorized, or highlighted within its class.
     *
     * Only visible and applicable when an innovation class is selected.
     *
     * Used for:
     * - Providing additional context for the innovation within its class
     * - Controlling visualization behavior in class-based views
     * - Supporting filtering and sorting within a class
     */
    {
      name: 'innovationClassDetails',
      type: 'group',
      admin: {
        description: 'Additional details related to the innovation class',
        condition: (data) => !!data.innovationClass,
      },
      fields: [
        /**
         * Category within the innovation class
         *
         * This allows for sub-categorization within an innovation class.
         * The four main categories (Detect, Treat, Prevent, Build) represent
         * the primary function or purpose of the innovation.
         */
        {
          name: 'category',
          type: 'select',
          options: [
            { label: 'Detect', value: 'detect' },
            { label: 'Treat', value: 'treat' },
            { label: 'Prevent', value: 'prevent' },
            { label: 'Build', value: 'build' },
          ],
          admin: {
            description: 'The primary category for this innovation within its class',
          },
        },

        /**
         * Highlight Flag
         *
         * When enabled, this innovation will be visually highlighted in class-based
         * visualizations and listings. This can be used to draw attention to
         * particularly important or featured innovations within a class.
         */
        {
          name: 'highlight',
          type: 'checkbox',
          defaultValue: false,
          admin: {
            description: 'Whether to highlight this innovation in class-based visualizations',
          },
        },

        /**
         * Class-Specific Description
         *
         * This description is specific to how the innovation relates to or functions
         * within its class. It may differ from the general innovation description
         * by focusing on class-relevant aspects.
         */
        {
          name: 'classSpecificDescription',
          type: 'textarea',
          admin: {
            description: 'A short description specific to this innovation class (max 150 chars)',
          },
        },
      ],
    },
    {
      name: 'versionNumber',
      type: 'number',
      defaultValue: 0,
      required: true,
      admin: {
        readOnly: true,
        position: 'sidebar',
      },
    },
    {
      name: 'createdBy',
      type: 'relationship',
      relationTo: 'appUsers',
      required: true,
      hasMany: false,
      hooks: {
        beforeChange: [
          async ({ data, req }) => {
            if (data?.createdBy) {
              const user = await req?.payload?.findByID({
                collection: 'appUsers',
                id: data.createdBy,
              });
              if (!user) {
                throw new Error('Invalid user ID. The referenced user does not exist.');
              }
            }
          },
        ],
      },
    },
  ],
  hooks: {
    beforeChange: [
      async ({ data, operation, req, originalDoc, context }) => {
        // Skip version creation if skipVersionCreation flag is set
        const skipVersionCreation = (context as any)?.skipVersionCreation;

        // Performance optimization: Skip unnecessary processing
        if ((context as any)?.disableHooks) {
          return data;
        }

        if (operation === 'create') {
          data.status = 'pending'; // Force pending status on creation
        }

        if (operation === 'update' && data.status === 'approved' && !skipVersionCreation) {
          try {
            // Check if this is the first approval or if it's being re-approved
            if (originalDoc.status === 'pending') {
              // Validate required fields before creating version
              const missingFields = [];
              console.log({ data });

              if (!data.targetedTo) {
                missingFields.push('Targeted To');
              }

              if (
                !data.interventionType ||
                (Array.isArray(data.interventionType) && data.interventionType.length === 0)
              ) {
                missingFields.push('Intervention Type');
              }

              if (missingFields.length > 0) {
                const errorMessage = `The following fields are invalid: ${missingFields.join(', ')}`;
                console.error(errorMessage);
                // throw new Error(errorMessage);
              }

              // First approval - create initial version
              const newVersion = await req.payload.create({
                collection: 'project-versions',
                data: {
                  ...data,
                  innovation: originalDoc.id,
                  status: 'approved',
                  versionNumber: 0,
                } as any,
                context: { disableTransactions: true, skipVersionCreation: true },
              });

              data.currentVersion = newVersion.id;
              data.versionNumber = 0;
            }
            // If it's being re-approved and had a previous version, we'll handle that in the ProjectVersions hooks
          } catch (error) {
            console.error('Error creating initial version:', error);
            throw error; // Preserve the original error message
          }
        }
        return data;
      },
    ],
    afterChange: [
      async ({ doc, req, previousDoc, operation, context }) => {
        // Skip certain operations if skipVersionCreation flag is set
        const skipVersionCreation = (context as any)?.skipVersionCreation;

        // Performance optimization: Skip unnecessary processing
        if ((context as any)?.disableHooks) {
          return;
        }

        const { payload } = req;

        /**
         * Innovation Class Relationship Management
         *
         * This section handles changes to the innovation class relationship.
         * When an innovation is assigned to a different class, we need to:
         * 1. Detect the change by comparing old and new class IDs
         * 2. Log the change for auditing purposes
         * 3. Fetch details about the new class
         * 4. Perform any necessary operations based on the class change
         *
         * The bidirectional relationship is maintained through:
         * - This hook (updating the innovation when class changes)
         * - The InnovationClass hooks (updating the class when innovations are added/removed)
         * - The syncInnovationClass function (called later in this hook)
         */
        if (operation === 'update') {
          // Extract class IDs, handling both populated objects and direct IDs
          const oldClassId = previousDoc?.innovationClass?.id ?? previousDoc?.innovationClass;
          const newClassId = doc?.innovationClass?.id ?? doc?.innovationClass;

          // Check if the innovation class has changed
          if (oldClassId !== newClassId) {
            console.log(
              `Innovation class changed from ${oldClassId} to ${newClassId} for innovation ${doc.id}`,
            );

            try {
              // If there's a new class, fetch its details
              if (newClassId) {
                const innovationClass = await payload.findByID({
                  collection: 'innovationClass',
                  id: newClassId,
                  depth: 0,
                });

                console.log(
                  `Innovation ${doc.id} is now associated with innovation class ${innovationClass.name}`,
                );

                /**
                 * Additional operations can be performed here based on the class change:
                 *
                 * - Update related collections
                 * - Trigger specific workflows
                 * - Send notifications
                 * - Update statistics or analytics
                 * - Perform validation or data enrichment
                 */
              }
            } catch (error) {
              console.error(
                `Error handling innovation class change for innovation ${doc.id}:`,
                error,
              );
            }
          }
        }

        // Function to get creator's email
        const getCreatorEmail = async (creatorId: string): Promise<string | null> => {
          if (!creatorId) return null;

          try {
            const creator = await payload.findByID({
              collection: 'appUsers',
              id: creatorId,
              depth: 0,
            });
            return creator?.email || null;
          } catch (error) {
            console.error('Error fetching creator email:', error);
            return null;
          }
        };

        // Function to send emails to both innovation email and creator
        const sendEmailsToRecipients = async (
          trigger: string,
          variables: Record<string, any>,
        ): Promise<void> => {
          // Always send to the innovation email
          await sendTriggeredEmail(trigger, doc?.email, variables);

          // Get creator's email
          const creatorId = doc?.createdBy?.id || doc?.createdBy;
          if (creatorId) {
            const creatorEmail = await getCreatorEmail(creatorId);

            // Send to creator if email exists and is different from innovation email
            if (creatorEmail && creatorEmail !== doc?.email) {
              await sendTriggeredEmail(trigger, creatorEmail, variables);
            }
          }
        };

        // Skip email sending if this is part of a version creation process
        if (operation === 'create' && !skipVersionCreation) {
          await sendEmailsToRecipients('submitted-innovation', {
            name: doc?.name,
            title: doc?.title,
          });
        }

        const isApproved = doc?.status === 'approved';
        const statusChanged = doc?.status !== previousDoc?.status;
        const classChanged =
          (doc?.innovationClass?.id ?? doc?.innovationClass) !==
          (previousDoc?.innovationClass?.id ?? previousDoc?.innovationClass);

        // Send email notifications when innovation status changes
        // Skip if this is part of a version creation process
        if (operation === 'update' && statusChanged && !skipVersionCreation) {
          const currentDate = new Date().toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
          });

          if (doc?.status === 'approved' && previousDoc.status !== 'approved') {
            // Send approval email to both recipients
            await sendEmailsToRecipients('innovation-approved', {
              name: doc?.name,
              title: doc?.title,
              submissionDate: currentDate,
              link: `${process.env.NEXT_PUBLIC_BACKEND_URL}/innovation/${doc?.slug}`,
            });
          } else if (doc?.status === 'rejected' && previousDoc.status !== 'rejected') {
            // Send rejection email to both recipients
            await sendEmailsToRecipients('innovation-rejected', {
              name: doc?.name,
              title: doc?.title,
              submissionDate: currentDate,
              feedback:
                'Your innovation submission requires additional information or revisions. Please review our guidelines and resubmit with more details.',
              // editLink: `${process.env.NEXT_PUBLIC_BACKEND_URL}/how-to-submit/submit-an-innovation/${doc?.slug}`,
            });
          }
        }

        /**
         * Synchronize Innovation Class Relationship
         *
         * This section maintains the bidirectional relationship between innovations and innovation classes.
         * It's triggered when either:
         * - The innovation's status changes (approved/rejected/pending)
         * - The innovation's class assignment changes
         *
         * The syncInnovationClass function:
         * 1. Removes the innovation from its old class (if any)
         * 2. Adds the innovation to its new class (if any and if approved)
         * 3. Updates the innovations array in the InnovationClass collection
         *
         * This ensures data consistency across collections and prevents orphaned relationships.
         */
        if (operation === 'update' && (statusChanged || classChanged) && !skipVersionCreation) {
          console.log(`Syncing InnovationClass for ${doc?.id} due to change.`);
          const oldClassId = previousDoc?.innovationClass?.id ?? previousDoc?.innovationClass;
          const newClassId = doc?.innovationClass?.id ?? doc?.innovationClass;

          // Call the sync function to maintain bidirectional relationship
          await syncInnovationClass(doc?.id, isApproved, newClassId, oldClassId, payload);
        }
      },
    ],

    afterDelete: [
      async ({ doc, req }) => {
        const { payload } = req;
        console.log(`Cleaning up after deleting innovation ${doc?.id}`);

        /**
         * Clean up Innovation Class Relationship on Deletion
         *
         * When an innovation is deleted, we need to remove it from any innovation class
         * it was associated with. This prevents orphaned references and maintains
         * data integrity in the InnovationClass collection.
         *
         * The syncInnovationClass function with these parameters:
         * - innovationId: The ID of the innovation being deleted
         * - isApproved: false (to force removal regardless of status)
         * - newClassId: null (no new class to add to)
         * - oldClassId: The class it was previously in
         *
         * This ensures the innovation is properly removed from its class.
         */
        const oldClassId = doc?.innovationClass?.id ?? doc?.innovationClass;
        if (oldClassId) {
          await syncInnovationClass(doc?.id, false, null, oldClassId, payload); // Force removal
          console.log(`Removed innovation ${doc?.id} from innovation class ${oldClassId}`);
        }

        // Delete associated versions
        try {
          await payload.delete({
            collection: 'project-versions',
            where: { innovation: { equals: doc?.id } },
            overrideAccess: true, // Ensure deletion happens
          });
          console.log(`Deleted associated versions for innovation ${doc?.id}`);
        } catch (error) {
          console.error(`Error deleting versions for innovation ${doc?.id}:`, error);
        }
      },
    ],
    afterOperation: [
      async ({ args, operation, result }) => {
        // Skip if not a create operation or if skipVersionCreation flag is set
        if (operation !== 'create' || (args as any).context?.skipVersionCreation) return;

        const { req } = args;
        const project = result;

        try {
          if (project.status === 'approved') {
            const nextVersionNumber = await getNextVersionNumber(req?.payload, project.id);

            const createdVersion = await req?.payload.create({
              collection: 'project-versions',
              data: {
                ...project,
                versionNumber: nextVersionNumber,
                innovation: project.id,
              },
              context: { disableTransactions: true },
            });

            await req?.payload.update({
              collection: 'innovations',
              id: project.id,
              data: {
                versionNumber: nextVersionNumber,
                currentVersion: createdVersion?.id,
              },
              // Set skipVersionCreation flag to prevent recursion
              context: { disableTransactions: true, skipVersionCreation: true },
            });
          }
        } catch (error) {
          console.error('Error creating initial version:', error);
          throw new Error('Failed to create initial version');
        }
      },
    ],
  },
  timestamps: true,
};

export default Innovations;
