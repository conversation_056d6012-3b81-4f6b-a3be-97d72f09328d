import { CollectionConfig } from 'payload';

const SubmitCriteria: CollectionConfig = {
  slug: 'submit-criteria',
  labels: {
    singular: 'Submit Criteria',
    plural: 'Submit Criteria',
  },
  admin: {
    useAsTitle: 'title',
    group: 'Forms and options',
    description: 'Criteria for submitting innovations',
    hideAPIURL: true,
  },
  access: {
    read: () => true, // Publicly readable
  },
  fields: [
    {
      name: 'number',
      label: 'Section Number',
      type: 'number',
      required: true,
    },
    {
      name: 'title',
      label: 'Title',
      type: 'text',
      required: true,
    },
    {
      name: 'items',
      label: 'Criteria Items',
      type: 'array',
      labels: {
        singular: 'Item',
        plural: 'Items',
      },
      fields: [
        {
          name: 'text',
          type: 'text',
          required: true,
        },
      ],
    },
  ],
};

export default SubmitCriteria;
