import { GlobalConfig } from 'payload';

const submitInnovationContent: GlobalConfig = {
  slug: 'submit-innovation-content',
  access: {
    read: () => true,
  },
  admin: {
    group: 'Website Content', // This groups it under "Website" in the sidebar
    description: 'Manage content for the Submit Innovation page',
    livePreview: {
      url: ({ globalConfig }) =>
        `${process.env.NEXT_PUBLIC_BACKEND_URL}/preview/${globalConfig?.slug}`, // or your frontend URL
    },
    hideAPIURL: true,
  },
  fields: [
    {
      name: 'heroTitle',
      type: 'text',
      label: 'Hero Section Title',
    },
    {
      name: 'heroDescription',
      type: 'textarea',
      label: 'Hero Section Description',
    },
    {
      name: 'cardTitle',
      type: 'text',
      label: 'card Section Title',
    },
    {
      name: 'cardDescription',
      type: 'textarea',
      label: 'card Section Description',
    },
    {
      name: 'criteria',
      type: 'relationship',
      label: 'Submission Criteria',
      relationTo: 'submit-criteria',
      hasMany: true,
      admin: {
        description: 'Select the criteria and then click on edit to change criteria content',
      },
    },
  ],
};

export default submitInnovationContent;
