import LiveVariablesComponent from '@/admin/components/LiveVariablesComponent';
import { CollectionConfig } from 'payload';

const variableMap: Record<string, { required: string[]; optional?: string[] }> = {
  'verify-email': {
    required: ['verifyUrl'],
  },
  'welcome-email': {
    required: ['name', 'dashboardLink'],
  },
  'forgot-password': {
    required: ['resetUrl'],
  },
  'submitted-innovation': {
    required: ['name', 'title'],
    optional: ['submissionDate'],
  },
  'innovation-approved': {
    required: ['name', 'title', 'submissionDate', 'link'],
  },
  'innovation-rejected': {
    required: ['name', 'title', 'submissionDate', 'feedback'],
    optional: ['editLink'],
  },
};

export const EmailTemplates: CollectionConfig = {
  slug: 'email-templates',
  admin: {
    useAsTitle: 'name',
    description: 'Manage email templates for system notifications and user communications.',
    group: 'Communication',
    hideAPIURL: true,
  },
  hooks: {
    beforeChange: [
      ({ data }) => {
        const trigger = data.trigger;
        if (trigger && variableMap[trigger]) {
          data.variables = variableMap[trigger];
        }
        return data;
      },
    ],
    // beforeValidate: [
    //   ({ data, req }) => {
    //     // Skip validation if html is not provided (other validators will catch this)
    //     if (!data?.html || !data?.trigger) return data;

    //     const triggerVars = variableMap[data.trigger];
    //     if (!triggerVars) return data;

    //     // Check for required variables in the HTML content
    //     const missingVars = triggerVars.required.filter((variable) => {
    //       const varPattern = new RegExp(`{{\\s*${variable}\\s*}}`, 'g');
    //       return !varPattern.test(data.html);
    //     });

    //     if (missingVars.length > 0) {
    //       throw new Error(
    //         `Missing required variables in template: ${missingVars.join(', ')}. Please include these variables in the format {{variableName}}`,
    //       );
    //     }

    //     return data;
    //   },
    // ],
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
    },
    {
      name: 'subject',
      type: 'text',
      required: true,
    },
    {
      name: 'trigger',
      type: 'select',
      options: Object.keys(variableMap).map((value) => ({
        label: value
          .split('-')
          .map((word) => word[0].toUpperCase() + word.slice(1))
          .join(' '),
        value,
      })),
      required: true,
      unique: true,
      admin: {
        description: 'Select the trigger that this email template is associated with.',
      },
    },
    {
      name: 'variables',
      type: 'ui',
      // required: false,
      admin: {
        components: {
          Field: LiveVariablesComponent as any,
        },
        // readOnly: true,
        // description: 'Auto-generated list of available variables based on the trigger',
      },
    },
    {
      name: 'html',
      type: 'textarea',
      required: true,
      validate: (value, { siblingData }: any) => {
        if (!value) return 'HTML content is required';
        if (!siblingData.trigger || !variableMap[siblingData.trigger]) return true;

        const triggerVars = variableMap[siblingData.trigger];
        const missingVars = triggerVars.required.filter((variable) => {
          const varPattern = new RegExp(`{{\\s*${variable}\\s*}}`, 'g');
          return !varPattern.test(value);
        });

        if (missingVars.length > 0) {
          return `Missing required variables: ${missingVars.join(', ')}. Please include these variables in the format {{variableName}}`;
        }

        return true;
      },
    },
  ],
};
