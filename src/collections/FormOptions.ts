// src/collections/FormSelectOptions.ts
import RichTextEditor from '@/admin/components/RichTextEditor';
import { CollectionConfig } from 'payload';

const FormSelectOptions: CollectionConfig = {
  slug: 'form-select-options',
  labels: {
    singular: 'Master Data',
    plural: 'Master Data',
  },
  // Default sort by order field within each fieldName group
  defaultSort: 'order',
  // Add indexes for better query performance
  indexes: [
    // Compound index for fieldName + order for efficient sorted queries
    {
      fields: ['fieldName', 'order'],
    },
    // Index for order field alone
    {
      fields: ['order'],
    },
  ],
  admin: {
    useAsTitle: 'value',
    description: 'Manage select options for the Innovation Form here.',
    group: 'Forms and options',
    defaultColumns: ['value', 'fieldName', 'isActive', 'order'],
    hideAPIURL: true,
  },
  access: {
    read: () => true, // Allow public read access for the form
  },
  fields: [
    {
      name: 'value',
      type: 'text',
      label: 'Option Value',
      required: true,
      unique: true, // Ensures no duplicate values
      admin: {
        description: 'The value submitted in the form (e.g., "healthcare").',
      },
    },
    {
      name: 'slug',
      type: 'text',
      unique: true,
      required: true,
      admin: {
        position: 'sidebar',
        description: 'URL-friendly version of the option value. Auto-generated if left empty.',
        readOnly: true,
      },
      hooks: {
        beforeValidate: [
          ({ siblingData }) => {
            if (!siblingData.slug && siblingData.value) {
              return siblingData.value
                .toLowerCase()
                .replace(/[^a-z0-9]+/g, '-')
                .replace(/(^-|-$)/g, '');
            }
          },
        ],
      },
    },
    {
      name: 'fieldName',
      type: 'select',
      label: 'Form Field',
      required: true,
      options: [
        { label: 'Current Stage of Development', value: 'currentStageOfDevelopment' },
        { label: 'Intervention Type', value: 'interventionType' },
        { label: 'Category', value: 'category' },
      ],
      admin: {
        description: 'The form field this option belongs to.',
      },
    },
    {
      name: 'isActive',
      type: 'checkbox',
      label: 'Active',
      defaultValue: true,
      admin: {
        description: 'Toggle to show/hide this option in the form.',
      },
    },
    {
      name: 'order',
      type: 'number',
      label: 'Display Order',
      defaultValue: 999, // High default value to place new items at the end
      admin: {
        description: 'Controls the display order of options (lower numbers appear first).',
        position: 'sidebar',
      },
      hooks: {
        beforeChange: [
          async ({ value, data, req }) => {
            // If no order is provided, find the highest current order and add 10
            if (value === undefined || value === null) {
              try {
                // Skip if fieldName is not available
                if (!data || !data.fieldName) {
                  return 999;
                }

                const options = await req.payload.find({
                  collection: 'form-select-options',
                  where: {
                    fieldName: { equals: data.fieldName },
                  },
                  sort: '-order',
                  limit: 1,
                });

                if (options.docs.length > 0) {
                  // Use type assertion to access the order property
                  const highestOrder = (options.docs[0] as any).order;
                  if (typeof highestOrder === 'number') {
                    return highestOrder + 10;
                  }
                }
                return 10; // Start at 10 if no existing options
              } catch (error) {
                console.error('Error determining order:', error);
                return 999; // Fallback
              }
            }
            return value;
          },
        ],
      },
    },
    {
      name: 'description',
      type: 'textarea',
      label: 'Description',
      admin: {
        description: 'Optional description for this option.',
        hidden: true,
      },
    },
    {
      name: 'descriptionEditor',
      type: 'ui',
      admin: {
        components: {
          Field: RichTextEditor as any,
        },
      },
      label: 'Description',
    },
  ],
};

export default FormSelectOptions;
