import { GlobalConfig } from 'payload';

const mapOfInnovationContent: GlobalConfig = {
  slug: 'mapOfInnovation-content',
  access: {
    read: () => true, // Allow anyone to read
  },
  admin: {
    group: 'Website Content', // This groups it under "Website" in the sidebar
    description: 'Manage content for the Map of Innovations page',
    livePreview: {
      url: ({ globalConfig }) =>
        `${process.env.NEXT_PUBLIC_BACKEND_URL}/preview/${globalConfig?.slug}`, // or your frontend URL
    },
    hideAPIURL: true,
  },
  fields: [
    {
      name: 'cardTitle',
      type: 'text',
      label: 'card Section Title',
    },
    {
      name: 'cardDescription',
      type: 'textarea',
      label: 'card Section Description',
    },
  ],
};

export default mapOfInnovationContent;
