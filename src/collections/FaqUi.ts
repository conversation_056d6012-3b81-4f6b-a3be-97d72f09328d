import { GlobalConfig } from 'payload';

const FAQContent: GlobalConfig = {
  slug: 'faq-content',
  label: 'FAQ Content',
  access: {
    read: () => true, // Publicly readable
  },
  admin: {
    group: 'Website Content',
    description: 'Manage content for the FAQ page',
    livePreview: {
      url: ({ globalConfig }) =>
        `${process.env.NEXT_PUBLIC_BACKEND_URL}/preview/${globalConfig?.slug}`, // or your frontend URL
    },
    hideAPIURL: true,
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      label: 'FAQ Section Title',
      defaultValue: 'Frequently Asked Questions',
      required: true,
    },
    {
      name: 'subtitle',
      type: 'textarea',
      label: 'FAQ Section Subtitle',
      defaultValue:
        "Have questions about TB control innovations? We've got answers! Below, you'll find information on our research, technologies, partnerships, and ways you can get involved.",
      required: true,
    },
    {
      name: 'faqs',
      type: 'array',
      label: 'FAQ Items',
      labels: {
        singular: 'FAQ',
        plural: 'FAQs',
      },
      fields: [
        {
          name: 'question',
          type: 'text',
          label: 'Question',
          required: true,
        },
        {
          name: 'answer',
          type: 'textarea',
          label: 'Answer',
          required: true,
        },
      ],
    },
  ],
};

export default FAQContent;
