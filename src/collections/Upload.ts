import type { CollectionConfig } from 'payload';

export const Upload: CollectionConfig = {
  slug: 'upload',
  access: {
    read: () => true,
    create: () => true,
  },
  admin: {
    description: 'File upload management',
    group: 'Forms and options',
    hidden: ({ user }) => user?.role !== 'admin',
    hideAPIURL: true,
  },
  fields: [
    {
      name: 'alt',
      type: 'text',
    },
  ],
  upload: {
    disableLocalStorage: true, // Disables local storage
    //   adminThumbnail: ({ doc }) =>
    //     `${process.env._AWS_END_POINT}/${doc?.filename}`,
  },
};
