import { CollectionConfig } from 'payload';
import { getNextVersionNumber } from './utils';
export const ProjectVersions: CollectionConfig = {
  slug: 'project-versions',
  access: {
    read: () => true,
    create: () => true,
  },
  labels: {
    singular: 'Innovation Version',
    plural: 'Innovation Versions',
  },
  admin: {
    defaultColumns: ['title', 'status', 'versionNumber', 'updatedAt'],
    useAsTitle: 'title',
    description: 'Manage versions of innovations here.',
    group: 'Innovation Management',
    hidden: ({ user }) => user?.role !== 'admin',
    hideAPIURL: true,
  },
  fields: [
    // Project relationship
    {
      name: 'title',
      type: 'text',
      required: true,
    },
    {
      name: 'innovation',
      type: 'text',
      required: true,
    },
    {
      name: 'description',
      type: 'textarea',
      required: true,
    },
    {
      name: 'currentStageOfDevelopment',
      type: 'relationship',
      relationTo: 'form-select-options',
      required: true,
      filterOptions: () => {
        return {
          fieldName: { equals: 'currentStageOfDevelopment' },
          isActive: { equals: true },
        };
      },
    },
    // {
    //   name: 'category',
    //   type: 'relationship',
    //   relationTo: 'form-select-options',
    //   required: true,
    //   hasMany: true,
    //   filterOptions: () => {
    //     return {
    //       fieldName: { equals: 'category' },
    //       isActive: { equals: true },
    //     };
    //   },
    // },
    {
      name: 'status',
      type: 'select',
      options: [
        { label: 'Pending', value: 'pending' },
        { label: 'Approved', value: 'approved' },
        { label: 'Rejected', value: 'rejected' },
        { label: 'Archived', value: 'archived' },
      ],
      defaultValue: 'pending',
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'name',
      type: 'text',
      required: true,
    },
    {
      name: 'email',
      type: 'email',
      required: true,
    },
    {
      name: 'phoneNo',
      type: 'text',
      required: true,
    },
    {
      name: 'link',
      type: 'text',
      required: true,
    },
    {
      name: 'organizationName',
      type: 'text',
      required: true,
    },
    {
      name: 'interventionType',
      type: 'relationship',
      relationTo: 'form-select-options',
      required: false,
      hasMany: true,
      filterOptions: () => {
        return {
          fieldName: { equals: 'interventionType' }, // Only show options where fieldName is 'currentStageOfDevelopment'
          isActive: { equals: true },
        };
      },
    },
    {
      name: 'supportingDocument',
      type: 'relationship',
      relationTo: 'upload',
      required: false,
      hasMany: true,
    },
    {
      name: 'bannerImage',
      type: 'relationship',
      relationTo: 'upload',
      required: false,
      hasMany: false,
    },
    {
      name: 'declaration',
      type: 'checkbox',
      required: true,
    },
    // {
    //   name: "slug",
    //   type: "text",
    //   unique: true,
    //   admin: { position: "sidebar" },
    //   hooks: {
    //     beforeChange: [({ data }) => {
    //       if (data?.title) {
    //         data.slug = data.title.toLowerCase().replace(/\s+/g, "-"); // Generate slug
    //       }
    //       return data;
    //     }],
    //   },
    // }
    // {
    //   name: "flowchartId",
    //   type: "text", // Store as a simple text field
    //   required: false, // Optional since it’s only set when status is "approved"
    //   admin: {
    //     position: "sidebar",
    //     readOnly: true, // Prevent manual editing
    //     description: "ID of the associated flowchart entry, set automatically.",
    //   },
    // },
    {
      name: 'versionNumber',
      type: 'number',
      required: true,
      admin: {
        readOnly: true,
      },
      index: true, // Add index for faster queries
    },
    {
      name: 'createdBy',
      type: 'relationship',
      relationTo: 'appUsers', // Link to Users collection
      // required: true,
    },
    {
      name: 'remark',
      label: 'Change Remark',
      type: 'textarea',
      admin: {
        description: 'Description of changes made in this version',
        position: 'sidebar',
      },
    },
  ],
  hooks: {
    afterChange: [
      async ({ doc, req, operation, context }) => {
        // Skip if skipVersionCreation flag is set
        const skipVersionCreation = (context as any)?.skipVersionCreation;

        if (operation === 'create' && doc?.status === 'approved' && !skipVersionCreation) {
          const innovation = await req.payload.findByID({
            collection: 'innovations',
            id: doc?.innovation,
            depth: 0,
          });

          if (innovation.currentVersion) {
            await req.payload.update({
              collection: 'project-versions',
              id: innovation.currentVersion as string,
              data: { status: 'archived' },
            });
          }

          await req.payload.update({
            collection: 'innovations',
            id: doc?.innovation,
            data: {
              ...doc,
              currentVersion: doc?.id,
            },
            // Set skipVersionCreation flag to prevent recursion
            context: { skipVersionCreation: true },
          });
        }
      },
    ],
    beforeChange: [
      async ({ data, req, operation, originalDoc, context }) => {
        // Skip if skipVersionCreation flag is set
        const skipVersionCreation = (context as any)?.skipVersionCreation;
        // console.log(originalDoc);

        // if (operation === 'create' && data.status) {
        //   if (data.status === 'approved') {
        //     const innovation = await req.payload.findByID({
        //       collection: 'innovations',
        //       id: data.innovation,
        //       depth: 0,
        //     });
        //     if (data.status === 'approved') {
        //       if (innovation.currentVersion) {
        //         await req.payload.update({
        //           collection: 'project-versions',
        //           id: innovation.currentVersion as string,
        //           data: { status: 'archived' },
        //         });
        //       }

        //       await req.payload.update({
        //         collection: 'innovations',
        //         id: data.innovation,
        //         data: {
        //           ...data,
        //           currentVersion: originalDoc.id,
        //         },
        //       });
        //     }
        //   }
        // }

        if (operation === 'update' && data.status && !skipVersionCreation) {
          try {
            const innovation = await req.payload.findByID({
              collection: 'innovations',
              id: data.innovation,
              depth: 0,
            });

            // Only check the innovation status if we're not in the process of approving it
            // This allows changing from pending to approved to work properly
            if (
              innovation.status !== 'approved' &&
              !(data.status === 'approved' && originalDoc?.status !== 'approved')
            ) {
              throw new Error('Only approved innovations can have versions modified');
            }

            if (data.status === 'approved' && originalDoc?.status !== 'approved') {
              const nextVersionNumber = originalDoc.versionNumber;

              if (innovation.currentVersion) {
                await req.payload.update({
                  collection: 'project-versions',
                  id: innovation.currentVersion as string,
                  data: { status: 'archived' },
                });
              }

              await req.payload.update({
                collection: 'innovations',
                id: data.innovation,
                data: {
                  ...data,
                  currentVersion: originalDoc.id,
                  versionNumber: nextVersionNumber,
                },
                // Set skipVersionCreation flag to prevent recursion
                context: { skipVersionCreation: true },
              });

              data.versionNumber = nextVersionNumber;
            }
          } catch (error) {
            console.error('Error in beforeChange hook:', error);
            throw error;
          }
        }
        return data;
      },
    ],
  },
  timestamps: true,
  endpoints: [
    {
      path: '/:id/update',
      method: 'post',
      handler: async (req: any) => {
        const { id } = req.routeParams;
        const user = req.user;

        if (!user) {
          return new Response(JSON.stringify({ error: 'Authentication required' }), {
            status: 401,
            headers: { 'Content-Type': 'application/json' },
          });
        }

        try {
          const project = await req.payload.findByID({
            collection: 'innovations',
            id,
          });

          if (!project) {
            return new Response(JSON.stringify({ error: 'Innovation not found' }), {
              status: 404,
              headers: { 'Content-Type': 'application/json' },
            });
          }

          const body = await req.json();
          if (project.status !== 'approved') {
            const updatedInnovation = await req.payload.update({
              collection: 'innovations',
              id,
              data: { ...body },
              context: { skipVersionCreation: true },
            });

            return new Response(JSON.stringify(updatedInnovation), {
              status: 200,
              headers: { 'Content-Type': 'application/json' },
            });
          }

          const nextVersionNumber = await getNextVersionNumber(req.payload, id);
          const newVersion = await req.payload.create({
            collection: 'project-versions',
            data: {
              ...body,
              innovation: id,
              status: 'pending',
              versionNumber: nextVersionNumber,
            },
            context: { skipVersionCreation: true },
          });

          return new Response(JSON.stringify(newVersion), {
            status: 200,
            headers: { 'Content-Type': 'application/json' },
          });
        } catch (error) {
          console.error('Error in update endpoint:', error);
          return new Response(
            JSON.stringify({
              error: error instanceof Error ? error.message : 'Internal Server Error',
            }),
            {
              status: 500,
              headers: { 'Content-Type': 'application/json' },
            },
          );
        }
      },
    },
  ],
};
