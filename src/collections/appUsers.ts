import type { CollectionConfig } from 'payload';
import { getEmailHTMLFromTemplate, sendTriggeredEmail } from './utils';

const appUsers: CollectionConfig = {
  slug: 'appUsers',
  auth: {
    tokenExpiration: 315360000,
    verify: {
      generateEmailHTML: async ({ token }) => {
        const verifyUrl = `${process.env.NEXT_PUBLIC_BACKEND_URL}/token-auth?token=${token}`;
        const html = await getEmailHTMLFromTemplate('verify-email', {
          verifyUrl,
        });
        return html;
      },
      generateEmailSubject: async () => {
        return '✅ Verify Your Email Address - Atlas of Innovations';
      },
    },
    forgotPassword: {
      generateEmailHTML: async ({ token }: any) => {
        const resetUrl = `${process.env.NEXT_PUBLIC_BACKEND_URL}/reset-password?token=${token}`;
        const html = await getEmailHTMLFromTemplate('forgot-password', {
          resetUrl,
        });
        return html;
      },
      generateEmailSubject: async () => {
        return '🔐 Reset Your Password - Atlas of Innovations';
      },
    },
  },
  access: {
    create: () => true,
    read: ({ req }) => !!req.user,
  },
  admin: {
    useAsTitle: 'name',
    description: 'User accounts for the website',
    group: 'User Management',
    hideAPIURL: true,
  },
  labels: {
    singular: 'Innovator',
    plural: 'Innovators',
  },
  fields: [
    { name: 'name', type: 'text' },
    { name: 'email', type: 'email', required: true, unique: true },
    { name: 'password', type: 'password' as any },
  ],
  hooks: {
    afterChange: [
      async ({ doc, operation }) => {
        // Send welcome email when a user is verified
        if (operation === 'create' && doc?._verified) {
          await sendTriggeredEmail('welcome-email', doc?.email, {
            name: doc?.name || 'User',
            dashboardLink: `${process.env.NEXT_PUBLIC_BACKEND_URL}/browse-innovations`,
          });
        }
      },
    ],
  },
};

export default appUsers;
