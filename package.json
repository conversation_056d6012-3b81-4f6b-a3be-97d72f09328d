{"name": "atlas-of-Innovations", "version": "1.0.0", "description": "A blank template to get started with Payload 3.0", "license": "MIT", "type": "module", "scripts": {"build": "cross-env NODE_OPTIONS=--no-deprecation next build", "dev": "cross-env NODE_OPTIONS=--no-deprecation next dev", "devsafe": "rm -rf .next && cross-env NODE_OPTIONS=--no-deprecation next dev", "generate:importmap": "cross-env NODE_OPTIONS=--no-deprecation payload generate:importmap", "generate:types": "cross-env NODE_OPTIONS=--no-deprecation payload generate:types", "lint": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "payload": "cross-env NODE_OPTIONS=--no-deprecation payload", "start": "cross-env NODE_OPTIONS=--no-deprecation next start"}, "dependencies": {"@hookform/resolvers": "^4.1.3", "@payloadcms/db-mongodb": "^3.27.0", "@payloadcms/live-preview-react": "^3.33.0", "@payloadcms/next": "^3.27.0", "@payloadcms/payload-cloud": "^3.27.0", "@payloadcms/richtext-lexical": "^3.27.0", "@payloadcms/richtext-slate": "^3.33.0", "@payloadcms/storage-s3": "^3.27.0", "@payloadcms/ui": "^3.27.0", "@tailwindcss/postcss": "^4.0.7", "@tanstack/react-query": "^5.66.7", "@xyflow/react": "^12.4.3", "cross-env": "^7.0.3", "d3": "^7.9.0", "dagre": "^0.8.5", "diff": "^7.0.0", "dotenv": "^16.4.7", "framer-motion": "^12.8.0", "graphql": "^16.8.1", "jodit-react": "^5.2.18", "next": "^15.2.4", "nextjs-toploader": "^3.7.15", "nodemailer": "^6.10.0", "payload": "^3.27.0", "postcss": "^8.5.2", "quill-image-drop-and-paste": "^2.0.1", "react": "19.0.0", "react-cropper": "^2.3.3", "react-dom": "19.0.0", "react-google-recaptcha": "^3.1.0", "react-hook-form": "^7.54.2", "react-phone-number-input": "^3.4.12", "react-quill-new": "^3.4.6", "reactflow": "^11.11.4", "sharp": "0.32.6", "tailwindcss": "^4.0.7", "uuid": "^11.1.0", "yup": "^1.6.1"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@tanstack/eslint-plugin-query": "^5.66.1", "@types/dagre": "^0.7.52", "@types/diff": "^7.0.2", "@types/js-cookie": "^3.0.6", "@types/node": "^22.5.4", "@types/nodemailer": "^6.4.17", "@types/react": "19.0.7", "@types/react-dom": "19.0.3", "@types/react-google-recaptcha": "^2.1.9", "eslint": "^9.16.0", "eslint-config-next": "15.1.5", "eslint-config-prettier": "^10.1.1", "eslint-plugin-prettier": "^5.2.5", "prettier": "^3.5.3", "typescript": "5.7.3"}, "engines": {"node": "^18.20.2 || >=20.9.0", "pnpm": "^9"}, "pnpm": {"onlyBuiltDependencies": ["sharp"]}}