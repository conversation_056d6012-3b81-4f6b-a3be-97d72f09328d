{"compilerOptions": {"baseUrl": ".", "lib": ["DOM", "DOM.Iterable", "ES2022"], "allowJs": true, "skipLibCheck": true, "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"], "@payload-config": ["./src/payload.config.ts"]}, "target": "ES2022"}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "tailwind.config.js", "src/pages/404.tjs"], "exclude": ["node_modules"]}